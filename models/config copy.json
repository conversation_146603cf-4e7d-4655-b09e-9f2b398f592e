[{"model_id": "effnetb7_imagenet", "model_name": "EfficientNetB7 (Keras)", "model_type": "embedding", "applicable_ip_category": ["trademark", "copyright", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "KerasApplicationModel", "parameters": {"keras_app_name": "EfficientNetB7", "vector_size": 2560, "input_shape": [480, 480, 3], "pooling": "avg", "preprocess_func": "efficientnet"}}, "description": "EfficientNet B7 feature vector model from tf.keras.applications.", "ram_estimate_mb": 4096}, {"model_id": "jina_clip_v2", "model_name": "Jina CLIP V2", "model_type": "embedding", "applicable_ip_category": ["trademark", "copyright", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "ClipModel", "parameters": {"vector_size": 1024, "model_name_or_path": "jinaai/jina-clip-v2"}}, "description": "Jina CLIP V2 embedding model using SentenceTransformers.", "ram_estimate_mb": 4096}, {"model_id": "sift_500", "model_name": "SIFT (500 features)", "model_type": "descriptor", "applicable_ip_category": ["trademark", "patent"], "implementation": {"module": "backend.models.implementations.descriptors", "class": "SiftModel", "parameters": {"n_features": 500}}, "description": "SIFT descriptor extraction with 500 features max.", "ram_estimate_mb": 1024}, {"model_id": "dhash_8", "model_name": "dHash (8x8)", "model_type": "hash", "applicable_ip_category": ["trademark", "copyright", "patent"], "implementation": {"module": "backend.models.implementations.hashing", "class": "DHashModel", "parameters": {"hash_size": 8}}, "description": "Difference Hash (dHash) with an 8x8 hash size.", "ram_estimate_mb": 128}, {"model_id": "keras_resnet50", "model_name": "ResNet50 (Keras)", "model_type": "embedding", "applicable_ip_category": ["copyright", "trademark", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "KerasApplicationModel", "parameters": {"keras_app_name": "ResNet50", "vector_size": 2048, "input_shape": [224, 224, 3], "pooling": "avg", "preprocess_func": "resnet"}}, "description": "ResNet50 feature vector model from tf.keras.applications.", "ram_estimate_mb": 2048}, {"model_id": "keras_vgg16", "model_name": "VGG16 (Keras)", "model_type": "embedding", "applicable_ip_category": ["copyright", "trademark", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "KerasApplicationModel", "parameters": {"keras_app_name": "VGG16", "vector_size": 512, "input_shape": [224, 224, 3], "pooling": "avg", "preprocess_func": "vgg16"}}, "description": "VGG16 feature vector model from tf.keras.applications.", "ram_estimate_mb": 1536}, {"model_id": "keras_vgg19", "model_name": "VGG19 (Keras)", "model_type": "embedding", "applicable_ip_category": ["copyright", "trademark", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "KerasApplicationModel", "parameters": {"keras_app_name": "VGG19", "vector_size": 512, "input_shape": [224, 224, 3], "pooling": "avg", "preprocess_func": "vgg19"}}, "description": "VGG19 feature vector model from tf.keras.applications.", "ram_estimate_mb": 1600}, {"model_id": "keras_effnetv2l", "model_name": "EfficientNetV2L (Keras)", "model_type": "embedding", "applicable_ip_category": ["copyright", "trademark", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "KerasApplicationModel", "parameters": {"keras_app_name": "EfficientNetV2L", "vector_size": 1280, "input_shape": [480, 480, 3], "pooling": "avg", "preprocess_func": "efficientnet_v2"}}, "description": "EfficientNetV2 Large feature vector model from tf.keras.applications.", "ram_estimate_mb": 4096}, {"model_id": "keras_inceptionresnetv2", "model_name": "InceptionResNetV2 (Keras)", "model_type": "embedding", "applicable_ip_category": ["copyright", "trademark", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "KerasApplicationModel", "parameters": {"keras_app_name": "InceptionResNetV2", "vector_size": 1536, "input_shape": [299, 299, 3], "pooling": "avg", "preprocess_func": "inception_resnet_v2"}}, "description": "InceptionResNetV2 feature vector model from tf.keras.applications.", "ram_estimate_mb": 3072}, {"model_id": "keras_xception", "model_name": "Xception (Keras)", "model_type": "embedding", "applicable_ip_category": ["copyright", "trademark", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "KerasApplicationModel", "parameters": {"keras_app_name": "Xception", "vector_size": 2048, "input_shape": [299, 299, 3], "pooling": "avg", "preprocess_func": "xception"}}, "description": "Xception feature vector model from tf.keras.applications.", "ram_estimate_mb": 2560}, {"model_id": "keras_convnextxlarge", "model_name": "ConvNeXtXLarge (Keras)", "model_type": "embedding", "applicable_ip_category": ["copyright", "trademark", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "KerasApplicationModel", "parameters": {"keras_app_name": "ConvNeXtXLarge", "vector_size": 2048, "input_shape": [224, 224, 3], "pooling": "avg", "preprocess_func": "convnext"}}, "description": "ConvNeXt XLarge feature vector model from tf.keras.applications.", "ram_estimate_mb": 5120}, {"model_id": "keras_resnet152v2", "model_name": "ResNet152V2 (Keras)", "model_type": "embedding", "applicable_ip_category": ["copyright", "trademark", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "KerasApplicationModel", "parameters": {"keras_app_name": "ResNet152V2", "vector_size": 2048, "input_shape": [224, 224, 3], "pooling": "avg", "preprocess_func": "resnet_v2"}}, "description": "ResNet152V2 feature vector model from tf.keras.applications.", "ram_estimate_mb": 3072}, {"model_id": "timm_swin_large_384", "model_name": "Swin Transformer Large 384 (timm)", "model_type": "embedding", "applicable_ip_category": ["copyright", "trademark", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "TimmModel", "parameters": {"timm_model_name": "swin_large_patch4_window12_384", "vector_size": 1536}}, "description": "Swin Transformer (Large, 384 input) feature vector model from timm library (PyTorch).", "ram_estimate_mb": 4096}, {"model_id": "orb_500", "model_name": "ORB (500 features)", "model_type": "descriptor", "applicable_ip_category": ["trademark", "patent"], "implementation": {"module": "backend.models.implementations.descriptors", "class": "OrbModel", "parameters": {"n_features": 500}}, "description": "ORB descriptor extraction with 500 features max.", "ram_estimate_mb": 512}, {"model_id": "sift_enhanced_500", "model_name": "Enhanced SIFT (Sobel, 500 features)", "model_type": "descriptor", "applicable_ip_category": ["trademark", "patent"], "implementation": {"module": "backend.models.implementations.descriptors", "class": "EnhancedSiftModel", "parameters": {"n_features": 500}}, "description": "SIFT descriptor extraction with Sobel preprocessing and 500 features max.", "ram_estimate_mb": 1024}, {"model_id": "orb_enhanced_500", "model_name": "Enhanced ORB (Sobel, 500 features)", "model_type": "descriptor", "applicable_ip_category": ["trademark", "patent"], "implementation": {"module": "backend.models.implementations.descriptors", "class": "EnhancedOrbModel", "parameters": {"n_features": 500}}, "description": "ORB descriptor extraction with Sobel preprocessing and 500 features max.", "ram_estimate_mb": 512}, {"model_id": "siglip2_large_patch16_512", "model_name": "Siglip2 Large Patch16 512", "model_type": "embedding", "applicable_ip_category": ["copyright", "trademark", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "SiglipModel", "parameters": {"vector_size": 1024, "model_name_or_path": "google/siglip2-large-patch16-512"}}, "description": "Siglip2 Large Patch16 512 embedding model from Hugging Face transformers.", "ram_estimate_mb": 4096}, {"model_id": "siglip2_giant_opt_patch16_384", "model_name": "Siglip2 Giant Opt Patch16 384", "model_type": "embedding", "applicable_ip_category": ["copyright", "trademark", "patent"], "implementation": {"module": "backend.models.implementations.embedding", "class": "SiglipModel", "parameters": {"vector_size": 1536, "model_name_or_path": "google/siglip2-giant-opt-patch16-384"}}, "description": "Siglip2 Giant Opt Patch16 384 embedding model from Hugging Face transformers.", "ram_estimate_mb": 5120}]