# Segmentation Mask Issues and Fixes

## Problem Summary

Based on user feedback and testing, only `gemini-2.5-flash-lite-preview-06-17` was generating correct segmentation masks, while other Gemini models were either:
1. Not generating any segmentation mask output
2. Generating invalid base64 data that couldn't be displayed
3. Returning error messages instead of mask data
4. Producing malformed responses that caused frontend display issues

## Root Causes Identified

### 1. Model Capability Differences
- **Issue**: Not all Gemini models have the same segmentation capabilities
- **Impact**: Some models simply cannot generate segmentation masks regardless of prompt
- **Models Affected**: Most models except `gemini-2.5-flash-lite-preview-06-17` and a few others

### 2. Inconsistent Response Formats
- **Issue**: Different models return segmentation data in different formats
- **Impact**: Valid mask data was being rejected due to format mismatches
- **Examples**: 
  - Some models return raw base64 without data URI prefix
  - Others return malformed JSON structures
  - Some refuse to generate masks entirely

### 3. Invalid Base64 Data Validation
- **Issue**: No validation of base64 image data before frontend display
- **Impact**: Invalid data caused "cannot display" errors in browser
- **Root Cause**: Models sometimes return non-image data in mask fields

### 4. Poor Error Handling
- **Issue**: Generic error messages didn't distinguish between different failure modes
- **Impact**: Difficult to debug why specific models failed
- **Examples**: "Invalid AI response format" for all types of failures

## Comprehensive Fixes Implemented

### 1. Model-Specific Capability Detection

```python
# Define models with known segmentation capabilities
segmentation_capable_models = [
    "gemini-2.5-flash-lite-preview-06-17",
    "gemini-2.5-flash-preview-05-20", 
    "gemini-1.5-pro",
    "gemini-1.5-flash"
]

model_supports_segmentation = any(capable_model in model_name for capable_model in segmentation_capable_models)
```

**Benefits**:
- Only request segmentation from capable models
- Provide appropriate prompts based on model capabilities
- Set correct user expectations

### 2. Enhanced Base64 Validation Function

```python
def _validate_base64_image(base64_data):
    """Validate if base64 data represents a valid image."""
    try:
        import base64
        import io
        from PIL import Image
        
        # Remove data URI prefix if present
        if base64_data.startswith('data:image'):
            base64_data = base64_data.split(',')[1]
        
        # Decode base64
        image_bytes = base64.b64decode(base64_data)
        
        # Try to open as image
        image = Image.open(io.BytesIO(image_bytes))
        
        # Basic validation: image should have reasonable dimensions
        width, height = image.size
        if width > 0 and height > 0 and width <= 2048 and height <= 2048:
            return True
        else:
            print(f"Invalid image dimensions: {width}x{height}")
            return False
            
    except Exception as e:
        print(f"Base64 image validation failed: {e}")
        return False
```

**Benefits**:
- Validates base64 data before frontend display
- Prevents "cannot display" errors
- Provides specific error messages for debugging

### 3. Improved Response Processing

**Enhanced Mask Validation**:
```python
# Validate segmentation mask if present
if include_masks and 'mask' in item:
    mask_data = item['mask']
    if _validate_base64_image(mask_data):
        # Ensure proper data URI format
        if not mask_data.startswith('data:image'):
            item['mask'] = f'data:image/png;base64,{mask_data}'
        print(f"✓ Valid segmentation mask for: {item['label']}")
    else:
        # Remove invalid mask but keep bounding box
        print(f"⚠️ Invalid mask removed for: {item['label']}")
        item = item.copy()
        del item['mask']
```

**Benefits**:
- Gracefully handles invalid mask data
- Preserves bounding box detection even when masks fail
- Provides clear feedback about mask validation status

### 4. Proper Prompt Handling

**IMPORTANT CORRECTION**: The system now properly respects user prompts instead of overriding them.

**For Capable Models**:
```python
if include_masks and model_supports_segmentation:
    system_prompt = f"""{prompt_text}

TECHNICAL REQUIREMENTS:
Return a JSON array where each detected object includes:
- "box_2d": [y_min, x_min, y_max, x_max] coordinates (0-1000 scale)
- "label": clear, descriptive label
- "mask": base64-encoded PNG segmentation mask"""
```

**Key Fix**:
- ✅ **User's prompt comes first** - respects what the user actually wants to detect
- ✅ **Technical requirements added as instructions** - tells the AI how to format the response
- ❌ **Previous version incorrectly replaced user prompts** with system instructions

**For Non-Capable Models**:
- User prompt is preserved
- Only technical JSON formatting requirements are added
- No segmentation mask requests

### 5. Enhanced Error Reporting

**Specific Error Messages**:
- "Model doesn't support segmentation masks"
- "Invalid mask data format detected"
- "Segmentation requested but no valid masks generated"
- "Base64 validation failed for mask data"

**Benefits**:
- Clear understanding of failure reasons
- Better debugging capabilities
- Improved user experience

## Testing and Validation

### Test Script: `test_segmentation_fix.py`

The comprehensive test script validates:
1. **Image Upload**: Creates and uploads test image with distinct shapes
2. **Experiment Creation**: Sets up segmentation experiment
3. **Model Testing**: Tests all available models
4. **Result Analysis**: Analyzes success/failure rates and mask quality
5. **Validation**: Checks base64 data integrity and format

### Expected Results After Fixes

**For Capable Models** (e.g., `gemini-2.5-flash-lite-preview-06-17`):
- ✅ Successful bounding box detection
- ✅ Valid segmentation masks with proper base64 data
- ✅ Correct data URI format (`data:image/png;base64,...`)
- ✅ Displayable thumbnails in frontend

**For Non-Capable Models**:
- ✅ Successful bounding box detection
- ⚠️ No segmentation masks (expected behavior)
- ✅ Clear error message explaining limitation
- ✅ No frontend display errors

**For Failed Models**:
- ❌ Appropriate error messages
- ✅ No system crashes or undefined behavior
- ✅ Clear indication of failure reason

## Frontend Improvements

### Enhanced Display Logic

The frontend now:
1. **Validates mask data** before attempting to display
2. **Shows appropriate placeholders** for missing masks
3. **Provides clear error indicators** for failed masks
4. **Maintains functionality** even when masks are unavailable

### User Experience Improvements

- **Clear visual indicators** for models with/without segmentation
- **Helpful tooltips** explaining segmentation capabilities
- **Graceful degradation** when masks are unavailable
- **Consistent behavior** across different model types

## Configuration and Monitoring

### For Administrators

1. **Model Configuration**: Update `segmentation_capable_models` list as new models are released
2. **Monitoring**: Track segmentation success rates across models
3. **Debugging**: Use enhanced logging to identify new issues
4. **Performance**: Monitor impact of validation on response times

### For Users

1. **Model Selection**: Choose models known to support segmentation for mask tasks
2. **Fallback Strategy**: Use bounding box only for unsupported models
3. **Quality Assessment**: Rate both successful and failed results for better model ranking

## Future Improvements

### Short Term
1. **Automatic Model Detection**: Dynamically detect segmentation capabilities
2. **Caching**: Cache model capability information
3. **Batch Validation**: Optimize validation for multiple masks

### Long Term
1. **Model-Specific Optimization**: Fine-tune prompts for each model type
2. **Alternative Segmentation**: Implement fallback segmentation methods
3. **Quality Metrics**: Develop automated mask quality assessment
4. **User Preferences**: Allow users to specify preferred models for segmentation

## Conclusion

These fixes address the core issues with segmentation mask generation across different Gemini models:

1. **Reliability**: Robust handling of model differences and failures
2. **User Experience**: Clear feedback and graceful degradation
3. **Debugging**: Enhanced logging and error reporting
4. **Scalability**: Framework for handling new models and capabilities

The system now provides a consistent experience regardless of model capabilities, with clear indication of what each model can and cannot do regarding segmentation masks.
