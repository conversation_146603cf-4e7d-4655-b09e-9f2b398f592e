#!/usr/bin/env python3
"""
Test script to verify segmentation mask functionality across different Gemini models.
This script tests the fixes for segmentation mask generation and validation.
"""

import asyncio
import json
import os
import sys
import requests
from io import BytesIO
from PIL import Image, ImageDraw

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Test configuration
BASE_URL = "http://localhost:5000/api/v1/boundingbox"
TEST_IMAGE_SIZE = (512, 512)

# Models to test - including the problematic ones from your screenshot
TEST_MODELS = [
    "gemini-2.0-flash-exp",
    "gemini-2.0-flash-001", 
    "gemini-2.5-flash-preview-05-20",
    "gemini-2.5-pro-preview-06-05",
    "gemini-2.5-pro",
    "gemini-2.5-flash",
    "gemini-2.5-flash-lite-preview-06-17"  # The one that works according to your report
]

def create_test_image():
    """Create a simple test image with colored shapes for segmentation."""
    img = Image.new('RGB', TEST_IMAGE_SIZE, color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw distinct colored shapes that should be easy to segment
    # Red rectangle
    draw.rectangle([50, 50, 200, 150], fill='red', outline='darkred', width=3)
    # Blue circle (approximated with ellipse)
    draw.ellipse([300, 100, 450, 250], fill='blue', outline='darkblue', width=3)
    # Green triangle (approximated with polygon)
    draw.polygon([(150, 300), (250, 250), (350, 300)], fill='green', outline='darkgreen', width=3)
    
    # Add some text
    try:
        from PIL import ImageFont
        font = ImageFont.load_default()
        draw.text((100, 400), "TEST PRODUCT", fill='black', font=font)
    except:
        draw.text((100, 400), "TEST PRODUCT", fill='black')
    
    # Save to BytesIO buffer
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)
    return buffer

def upload_test_image():
    """Upload test image and return picture ID."""
    print("📤 Uploading test image...")
    
    test_image_buffer = create_test_image()
    files = {
        'picture': ('test_segmentation_image.png', test_image_buffer, 'image/png')
    }
    
    response = requests.post(f"{BASE_URL}/pictures", files=files)
    if response.status_code != 201:
        print(f"❌ Failed to upload image: {response.status_code} - {response.text}")
        return None
    
    picture_data = response.json()
    picture_id = picture_data['id']
    print(f"✅ Image uploaded successfully with ID: {picture_id}")
    return picture_id

def create_segmentation_experiment(picture_id):
    """Create an experiment with segmentation output type."""
    print("🧪 Creating segmentation experiment...")
    
    experiment_data = {
        'picture_id': picture_id,
        'prompt': 'Detect and segment all colored shapes and text in this image',
        'resize_width': 512,
        'resize_height': 512,
        'output_type': 'Bounding Box + Segmentation Task'
    }
    
    response = requests.post(f"{BASE_URL}/experiments", json=experiment_data)
    if response.status_code != 201:
        print(f"❌ Failed to create experiment: {response.status_code} - {response.text}")
        return None
    
    experiment_data = response.json()
    experiment_id = experiment_data['id']
    print(f"✅ Experiment created successfully with ID: {experiment_id}")
    return experiment_id

def wait_for_results(experiment_id, timeout=300):
    """Wait for all model results to complete."""
    print("⏳ Waiting for model results to complete...")
    
    import time
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        response = requests.get(f"{BASE_URL}/experiments", params={'picture_id': 1, 'page': 1, 'per_page': 100})
        if response.status_code != 200:
            print(f"❌ Failed to fetch experiments: {response.status_code}")
            return False
        
        data = response.json()
        experiments = data.get('experiments', [])
        
        # Find our experiment
        target_experiment = None
        for exp in experiments:
            if exp['id'] == experiment_id:
                target_experiment = exp
                break
        
        if not target_experiment:
            print(f"❌ Experiment {experiment_id} not found")
            return False
        
        results = target_experiment.get('results', [])
        if not results:
            print("⏳ No results yet, waiting...")
            time.sleep(5)
            continue
        
        # Check if all results are complete
        pending_results = [r for r in results if r['status'] in ['pending', 'processing']]
        if not pending_results:
            print(f"✅ All {len(results)} results completed!")
            return True
        
        print(f"⏳ {len(pending_results)} results still processing...")
        time.sleep(10)
    
    print(f"⏰ Timeout waiting for results after {timeout} seconds")
    return False

def analyze_segmentation_results(experiment_id):
    """Analyze the segmentation results from all models."""
    print("\n📊 Analyzing Segmentation Results")
    print("=" * 60)
    
    # Fetch experiment results
    response = requests.get(f"{BASE_URL}/experiments", params={'picture_id': 1, 'page': 1, 'per_page': 100})
    if response.status_code != 200:
        print(f"❌ Failed to fetch experiments: {response.status_code}")
        return
    
    data = response.json()
    experiments = data.get('experiments', [])
    
    # Find our experiment
    target_experiment = None
    for exp in experiments:
        if exp['id'] == experiment_id:
            target_experiment = exp
            break
    
    if not target_experiment:
        print(f"❌ Experiment {experiment_id} not found")
        return
    
    results = target_experiment.get('results', [])
    if not results:
        print("❌ No results found")
        return
    
    print(f"Found {len(results)} model results:")
    print()
    
    successful_segmentation = 0
    failed_segmentation = 0
    
    for result in results:
        model_name = result.get('model_name', 'Unknown')
        status = result.get('status', 'unknown')
        
        print(f"🤖 Model: {model_name}")
        print(f"   Status: {status}")
        
        if status == 'success':
            # Check for bounding boxes
            bounding_boxes = result.get('bounding_boxes')
            if bounding_boxes:
                try:
                    boxes = json.loads(bounding_boxes)
                    print(f"   ✅ Bounding boxes: {len(boxes)} detected")
                except:
                    print(f"   ❌ Invalid bounding box data")
            else:
                print(f"   ⚠️  No bounding boxes")
            
            # Check for segmentation masks
            segmentation_masks = result.get('segmentation_masks')
            if segmentation_masks:
                try:
                    masks = json.loads(segmentation_masks)
                    if masks and isinstance(masks, list) and len(masks) > 0:
                        print(f"   ✅ Segmentation masks: {len(masks)} found")
                        successful_segmentation += 1
                        
                        # Validate each mask
                        valid_masks = 0
                        for i, mask in enumerate(masks):
                            if isinstance(mask, dict):
                                label = mask.get('label', 'Unknown')
                                has_mask_data = 'mask_base64' in mask and mask['mask_base64']
                                if has_mask_data:
                                    # Check if it's a valid data URI
                                    mask_data = mask['mask_base64']
                                    if mask_data.startswith('data:image'):
                                        valid_masks += 1
                                        print(f"      ✅ Mask {i+1}: {label} (valid)")
                                    else:
                                        print(f"      ⚠️  Mask {i+1}: {label} (invalid format)")
                                else:
                                    print(f"      ❌ Mask {i+1}: {label} (no data)")
                        
                        print(f"   📊 Valid masks: {valid_masks}/{len(masks)}")
                    else:
                        print(f"   ❌ Empty or invalid segmentation masks")
                        failed_segmentation += 1
                except Exception as e:
                    print(f"   ❌ Error parsing segmentation masks: {e}")
                    failed_segmentation += 1
            else:
                print(f"   ⚠️  No segmentation masks field")
                failed_segmentation += 1
                
        elif status == 'failed':
            error_message = result.get('error_message', 'Unknown error')
            print(f"   ❌ Failed: {error_message}")
            failed_segmentation += 1
        else:
            print(f"   ⏳ Status: {status}")
        
        print()
    
    # Summary
    print("📈 SUMMARY")
    print("=" * 30)
    print(f"✅ Models with successful segmentation: {successful_segmentation}")
    print(f"❌ Models with failed segmentation: {failed_segmentation}")
    print(f"📊 Success rate: {successful_segmentation}/{len(results)} ({100*successful_segmentation/len(results):.1f}%)")
    
    if successful_segmentation == 0:
        print("\n⚠️  WARNING: No models successfully generated segmentation masks!")
        print("This indicates a systematic issue with segmentation mask generation.")
    elif successful_segmentation < len(results):
        print(f"\n⚠️  WARNING: Only {successful_segmentation} out of {len(results)} models generated segmentation masks.")
        print("Some models may not support segmentation or have implementation issues.")

def main():
    """Main test function."""
    print("🧪 Segmentation Mask Functionality Test")
    print("=" * 50)
    
    # Step 1: Upload test image
    picture_id = upload_test_image()
    if not picture_id:
        return False
    
    # Step 2: Create segmentation experiment
    experiment_id = create_segmentation_experiment(picture_id)
    if not experiment_id:
        return False
    
    # Step 3: Wait for results
    if not wait_for_results(experiment_id):
        print("❌ Test failed: Results did not complete in time")
        return False
    
    # Step 4: Analyze results
    analyze_segmentation_results(experiment_id)
    
    print("\n✅ Test completed!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
