# Product Requirements Document: Patent Visualization Platform

## 1. Introduction

This document outlines the requirements for a new Patent Visualization Platform. The platform will provide data visualization and exploration capabilities for an existing database of patent information. It is intended to be integrated into an existing Flask/React application, reusing common components and styling, and accessible via a new "Platform Switcher" in the main application navigation.

## 2. Goals

* Provide a comprehensive dashboard with key statistics about the patent data.
* Enable users to explore patent records through a filterable and customizable table.
* Allow users to view detailed information and images for individual patents.
* Integrate seamlessly with the existing application infrastructure and UI.
* Ensure efficient performance when querying and displaying data from a large patent database (approx. 8 million records).

## 3. User Personas

* **Primary User:** Internal user (as described in initial request) who needs to analyze and explore patent data for insights and decision-making.

## 4. Proposed Solution

### 4.1. Integration Strategy

* **Platform Switcher:** A new "Platform Switcher" dropdown menu will be added to the main application navigation bar, positioned to the left of the "ModelTestWorkbench" (or current primary application name) logo/title.
  * This menu will list available platforms:
    1. Current Platform (e.g., "ModelTestWorkbench")
    2. "Patent Visualization" (this new platform)
    3. (Placeholder for future platforms)
  * Selecting "Patent Visualization" will update the main navigation links to be specific to this platform (e.g., "Patent Dashboard", "Patent Explorer").
* **Codebase Structure:** The new features will be developed by extending the existing monolithic Flask (backend) and React (frontend) application.
  * **Backend:** New Flask API endpoints will be created under a distinct route prefix (e.g., `/api/v1/patents/...`).
  * **Frontend:** New React routes, pages, and components will be added.
  * **Styling & Components:** Existing UI libraries (`react-plotly.js`, `@mui/material`), custom components, and CSS styles will be reused and extended to maintain visual and functional consistency.

### 4.2. Technology Stack

* **Backend:** Flask (Python)
* **Frontend:** React, `react-plotly.js` (for charts), `@mui/material` (for UI components)
* **Database:** PostgreSQL (schema defined in [`docs/1_database_schema.md`](docs/1_database_schema.md:1))

## 5. Functional Requirements

### 5.1. Patent Dashboard

The dashboard will provide an overview of the patent data. It will feature a "Refresh" button to update all statistics. Cached results should be used to improve performance.

#### 5.1.1. Overall Database Statistics (Section 1)

This section displays statistics for the entire patent dataset.

* **Total Number of Records:** Display the total count of patents in the [`patents_records`](docs/1_database_schema.md:18) table.
* **Patent Type Distribution (Pie Chart):**
  * Show the distribution of patents by [`patent_type`](docs/1_database_schema.md:33).
  * Categories based on `patent_type` prefix:
    * `B%`: "Utility Patent"
    * `S%`: "Design Patent"
    * `A%`: "Patent Application (PGPub)"
    * `P%`: "Plant Patent"
    * `E%`: "Reissue Patent"
    * Other: "Other"
  * Display top N types and an "Others" category if many distinct types exist beyond these primary ones.
* **TRO True Percentage:** Display the percentage of patents where [`patents_records.tro`](docs/1_database_schema.md:24) is `True`.
* **Classification Statistics (Pie Charts - Top 10 and Others for each):**
  * **USPC Classification:** Pie chart showing distribution by [`patents_records.uspc_class`](docs/1_database_schema.md:32).
  * **LOC Classification:** Pie chart showing distribution by [`patents_records.loc_code`](docs/1_database_schema.md:42).
  * **CPC Classification:**
    * Pie chart showing distribution by CPC class.
    * Data is derived by joining [`patents_records`](docs/1_database_schema.md:18) with [`patents_cpc_assignments`](docs/1_database_schema.md:74) and then with [`patents_cpc_definitions`](docs/1_database_schema.md:139) to get the CPC `class`.
    * A single patent can have multiple CPC classifications. Count each assigned CPC class occurrence. The pie chart should represent the distribution of these total occurrences (Top 10 CPC classes and "Others").
* **Percentage of Records with Missing Data:** For each field below, display the percentage of patent records where the data is considered missing (NULL, empty array, or array of empty strings).
  * Missing Applicant ([`patents_records.applicant`](docs/1_database_schema.md:45))
  * Missing Inventors ([`patents_records.inventors`](docs/1_database_schema.md:43))
  * Missing Assignee ([`patents_records.assignee`](docs/1_database_schema.md:44))
  * Missing Associated Patents ([`patents_records.associated_patents`](docs/1_database_schema.md:35))
  * Missing Patent Title ([`patents_records.patent_title`](docs/1_database_schema.md:30))
  * Missing USPC Classification ([`patents_records.uspc_class`](docs/1_database_schema.md:32) is NULL or empty)
  * Missing LOC Classification ([`patents_records.loc_code`](docs/1_database_schema.md:42) is NULL or empty)
  * Missing CPC Classification (patent has no entries in [`patents_cpc_assignments`](docs/1_database_schema.md:74))

#### 5.1.2. TRO-Specific Statistics (Section 2)

This section displays the same statistics as in 5.1.1, but filtered **only for patents where [`patents_records.tro`](docs/1_database_schema.md:24) is `True`**.

* Total Number of Records (TRO = True)
* Patent Type Distribution (TRO = True)
* Classification Statistics (USPC, LOC, CPC) (TRO = True)
* Percentage of Records with Missing Data (Applicant, Inventors, etc.) (TRO = True)

### 5.2. Patent Exploration

This section allows users to explore patent records in a tabular format.

#### 5.2.1. Patent Table View

* **Display:** A table displaying patent records from [`patents_records`](docs/1_database_schema.md:18).
* **Pagination:** Ability to select the number of patents per page (e.g., 10, 25, 50, 100).
* **Default Columns:**
  * [`document_id`](docs/1_database_schema.md:41) (always shown)
  * [`patent_title`](docs/1_database_schema.md:30) (always shown)
* **Column Selection:** Ability for the user to select which other columns from [`patents_records`](docs/1_database_schema.md:18) to display in the table.
* **Filtering:** Ability to filter records based on the following key fields:
  * [`document_id`](docs/1_database_schema.md:41) (text search)
  * [`patent_title`](docs/1_database_schema.md:30) (text search)
  * [`abstract`](docs/1_database_schema.md:34) (text search)
  * [`date_published`](docs/1_database_schema.md:25) (date range picker)
  * [`patent_type`](docs/1_database_schema.md:33) (multi-select dropdown with defined types: Utility, Design, Application, Plant, Reissue, Other)
  * [`tro`](docs/1_database_schema.md:24) (dropdown: All, True, False)
  * [`inventors`](docs/1_database_schema.md:43) (text search)
  * [`assignee`](docs/1_database_schema.md:44) (text search)
  * [`applicant`](docs/1_database_schema.md:45) (text search)
  * [`uspc_class`](docs/1_database_schema.md:32) (text search)
  * [`loc_code`](docs/1_database_schema.md:42) (text search)
  * CPC Class (text search for CPC class codes or definition keywords; requires joining through [`patents_cpc_assignments`](docs/1_database_schema.md:74) to [`patents_cpc_definitions`](docs/1_database_schema.md:139))
* **Sorting:** Ability to sort the table by any displayed column.

#### 5.2.2. Patent Detail Overlay (on [`document_id`](docs/1_database_schema.md:41) click)

* When a user clicks on a [`document_id`](docs/1_database_schema.md:41) in the table, an overlay (modal) appears.
* **Content:**
  * Displays all columns/fields from the [`patents_records`](docs/1_database_schema.md:18) table for that specific patent.
  * **Classification Definitions:**
    * **USPC:** If [`uspc_class`](docs/1_database_schema.md:32) and [`uspc_subclass`](docs/1_database_schema.md:36) exist, look up and display the corresponding definition from [`patents_uspc_definitions.definition`](docs/1_database_schema.md:91) (joined on `class` and `subclass`).
    * **LOC:** If [`loc_code`](docs/1_database_schema.md:42) and [`loc_edition`](docs/1_database_schema.md:31) exist, look up and display [`patents_loc_definitions.class_definition`](docs/1_database_schema.md:163) and [`patents_loc_definitions.subclass_definition`](docs/1_database_schema.md:165) (joined on `loc_code` and `loc_edition`).
    * **CPC:** For each CPC associated with the patent (via [`patents_cpc_assignments`](docs/1_database_schema.md:74)), look up and display the corresponding [`patents_cpc_definitions.definition`](docs/1_database_schema.md:151) (joined on [`patents_cpc_assignments.cpc_id`](docs/1_database_schema.md:80) = [`patents_cpc_definitions.id`](docs/1_database_schema.md:144)).

#### 5.2.3. Patent Image Overlay (on [`patent_title`](docs/1_database_schema.md:30) click)

* When a user clicks on a [`patent_title`](docs/1_database_schema.md:30) in the table, an overlay (modal) appears.
* **Content:** Displays all patent images associated with that patent.
* **Image Path Construction:**
  1. Retrieve `master_folder` path using `backend.utils.file_utils.get_master_folder()`.
  2. Retrieve [`reg_no`](docs/1_database_schema.md:40) and [`date_published`](docs/1_database_schema.md:25) for the patent.
  3. Extract `XX` (last 2 digits of `reg_no`) and `YY` (2 digits before `XX` in `reg_no`).
  4. Construct folder path: `f"{master_folder}/IP/Patents/USPTO_Grants/Extracted/{YY}/{XX}/US{reg_no}-{date_published_formatted}"` (ensure `date_published` is formatted as YYYYMMDD or as required by directory structure).
  5. List all files in this directory.
  6. Display all files that do not end with `.xml` as images.

#### 5.2.4. Filtered Results Quick Stats

* Displayed above the patent exploration table *after* filters are applied.
* These stats apply to the **entire filtered result set**, not just the current page.
* **Stats to display:**
  * Total number of matching results.
  * Percentage of TRO ([`tro`](docs/1_database_schema.md:24) = `True`) within the results.
  * Percentage of Design Patents (where [`patent_type`](docs/1_database_schema.md:33) starts with 'S') within the results.

## 6. Data Model

The data model is defined by the PostgreSQL database schema documented in [`docs/1_database_schema.md`](docs/1_database_schema.md:1). Key tables include:

* [`patents_records`](docs/1_database_schema.md:18)
* [`patents_cpc_assignments`](docs/1_database_schema.md:74)
* [`patents_cpc_definitions`](docs/1_database_schema.md:139)
* [`patents_uspc_definitions`](docs/1_database_schema.md:84)
* [`patents_loc_definitions`](docs/1_database_schema.md:153)

## 7. Non-Functional Requirements

* **Performance:**
  * Dashboard statistics (especially aggregations over 8 million records) must load efficiently. Employ caching mechanisms for these statistics, with a manual "Refresh" option.
  * SQL queries must be optimized for large datasets. Consider database indexing strategies.
  * Patent exploration table filtering and pagination should be responsive.
* **Usability:**
  * The interface should be intuitive and easy to navigate.
  * Consistent design with the existing application.
  * Clear feedback to the user during data loading or processing.
* **Maintainability:**
  * Code should be modular, well-commented, and follow best practices for Flask and React.
* **Scalability:** While initially extending the monolith, the design should allow for potential future refactoring if parts of the application grow significantly.
* **Security:** Follow existing application security practices. Ensure no sensitive data is unnecessarily exposed.

## 8. Future Considerations

* Addition of more visualization types to the dashboard.
* Advanced search capabilities (e.g., semantic search if embeddings become available).
* User-saved views or filter presets.
* Integration of other IP data types (copyrights, trademarks) into similar visualization platforms.

## 9. Assumptions

* The `backend.utils.file_utils.get_master_folder()` function is accessible and correctly returns the base path for patent images.
* The directory structure for patent images (`IP/Patents/USPTO_Grants/Extracted/{YY}/{XX}/US{reg_no}-{date_published}`) is consistent.
* The format of `date_published` in the image path needs to match the actual directory naming (e.g., YYYYMMDD). This needs to be confirmed during development.
* For CPC pie chart normalization (Requirement 5.1.1): It's assumed that we count all occurrences of CPC classes linked to patents, and then the pie chart shows the distribution of these counts (Top 10 classes + "Others").
