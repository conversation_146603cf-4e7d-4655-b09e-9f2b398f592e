# setup.py
import os
import platform
import logging
from pathlib import Path
from dotenv import load_dotenv
import psycopg2
from psycopg2 import sql, errors
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from qdrant_client import QdrantClient, models

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables from .env file
dotenv_path = Path('.') / '.env'
if dotenv_path.exists():
    load_dotenv(dotenv_path=dotenv_path)
    logging.info(f"Loaded environment variables from {dotenv_path}")
else:
    logging.warning(f".env file not found at {dotenv_path}. Relying on system environment variables.")

# --- Environment Variable Retrieval ---
required_env_vars = [
    "POSTGRES_HOST", "POSTGRES_PORT", "POSTGRES_USER",
    "POSTGRES_PASSWORD", "POSTGRES_DB", "WIN_MAIN_DIR",
    "LINUX_MAIN_DIR", "QDRANT_URL", "QDRANT_API_KEY"
]

config = {}
missing_vars = []
for var in required_env_vars:
    value = os.getenv(var)
    if value is None:
        missing_vars.append(var)
    config[var] = value

if missing_vars:
    logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
    exit(1)

# --- Determine MASTERFOLDER ---
if platform.system() == "Windows":
    master_folder_path = Path(config["WIN_MAIN_DIR"])
    logging.info(f"Detected Windows OS. Using MASTERFOLDER: {master_folder_path}")
elif platform.system() == "Linux":
    master_folder_path = Path(config["LINUX_MAIN_DIR"])
    logging.info(f"Detected Linux OS. Using MASTERFOLDER: {master_folder_path}")
else:
    logging.error(f"Unsupported operating system: {platform.system()}")
    exit(1)

# --- Define and Create Image Directories ---
ip_types = ["trademark", "copyright", "patent"]
image_types = ["product", "ip"]
required_dirs = []

for ip_type in ip_types:
    for image_type in image_types:
        required_dirs.append(master_folder_path / ip_type / image_type)

logging.info("Ensuring MASTERFOLDER subdirectories exist...")
created_count = 0
for dir_path in required_dirs:
    try:
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            logging.info(f"Created directory: {dir_path}")
            created_count += 1
        # else:
        #     logging.debug(f"Directory already exists: {dir_path}")
    except OSError as e:
        logging.error(f"Failed to create directory {dir_path}: {e}")
        exit(1)
if created_count == 0:
    logging.info("All required MASTERFOLDER subdirectories already exist.")
else:
     logging.info(f"Created {created_count} new directories.")



# --- Connect and Setup Database ---
db_name = config["POSTGRES_DB"]
maint_conn = None
maint_cursor = None

try:
    # Connect to the default 'postgres' database to check if the target database exists
    logging.info("Connecting to PostgreSQL server ('postgres' db) to check for target database...")
    maint_conn = psycopg2.connect(
        host=config["POSTGRES_HOST"],
        port=config["POSTGRES_PORT"],
        user=config["POSTGRES_USER"],
        password=config["POSTGRES_PASSWORD"],
        dbname='postgres' # Connect to default db
    )
    maint_cursor = maint_conn.cursor()

    # Check if the target database exists
    maint_cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_name,))
    exists = maint_cursor.fetchone()

    if not exists:
        logging.warning(f"Database '{db_name}' does not exist. Creating it.")
        # Need autocommit mode to run CREATE DATABASE
        maint_conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        try:
            maint_cursor.execute(sql.SQL("CREATE DATABASE {}").format(sql.Identifier(db_name)))
            logging.info(f"Successfully created database '{db_name}'.")
        except psycopg2.Error as create_e:
            # Handle potential race condition if another process created the DB meanwhile
            if hasattr(create_e, 'pgcode') and create_e.pgcode == errors.lookup('42P04').pgcode: # duplicate_database
                 logging.warning(f"Database '{db_name}' already exists (created concurrently?).")
            else:
                logging.error(f"Failed to create database '{db_name}': {create_e}")
                exit(1) # Exit if database creation failed
        # Revert isolation level if needed, though we close the connection shortly
        # maint_conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_DEFAULT)
    else:
        logging.info(f"Database '{db_name}' already exists.")

except psycopg2.Error as maint_e:
    logging.error(f"Error during maintenance connection or database check/creation: {maint_e}")
    exit(1)
finally:
    if maint_cursor:
        maint_cursor.close()
    if maint_conn:
        maint_conn.close()
        logging.info("Closed maintenance database connection.")

# --- Add extensions to Target Database (Table and index creation is now handled by Alembic) ---
db_conn = None
db_cursor = None

SQL_COMMANDS = {
    "extensions": [
        "CREATE EXTENSION IF NOT EXISTS pgcrypto;", # Needed for gen_random_uuid()
        # Add other extensions if needed, e.g., "CREATE EXTENSION IF NOT EXISTS vector;" if using pgvector
    ],
}

try:
    # Now connect to the target database
    logging.info(f"Connecting to target database '{db_name}'...")
    db_conn = psycopg2.connect(
        host=config["POSTGRES_HOST"],
        port=config["POSTGRES_PORT"],
        user=config["POSTGRES_USER"],
        password=config["POSTGRES_PASSWORD"],
        dbname=db_name
    )
    db_cursor = db_conn.cursor()
    logging.info(f"Successfully connected to target database '{db_name}'.")

    # Create Extensions
    logging.info("Ensuring required database extensions exist...")
    if "extensions" in SQL_COMMANDS:
        for ext_sql in SQL_COMMANDS["extensions"]:
            try:
                db_cursor.execute(ext_sql)
                logging.info(f"Executed: {ext_sql.strip()}")
            except psycopg2.Error as ext_e:
                # Handle case where extension already exists
                if hasattr(ext_e, 'pgcode') and ext_e.pgcode == errors.lookup('42710').pgcode: # duplicate_object
                    logging.warning(f"Extension already exists (from '{ext_sql.strip()}'): {ext_e}")
                    db_conn.rollback() # Rollback the failed command
                else:
                    logging.warning(f"Could not execute '{ext_sql.strip()}': {ext_e}")
                    db_conn.rollback() # Rollback specific command error
        db_conn.commit() # Commit extension creations/checks
    else:
        logging.info("No extensions defined in SQL_COMMANDS.")

    # Table and Index creation logic removed - handled by Alembic
    logging.info("Table and index creation will be handled by Alembic.")

except psycopg2.Error as op_e:
    logging.error(f"Database operation failed on '{db_name}': {op_e}")
    if db_conn:
        db_conn.rollback()
    exit(1)
finally:
    if db_cursor:
        db_cursor.close()
    if db_conn:
        db_conn.close()
        logging.info(f"Closed connection to target database '{db_name}'.")


# --- Qdrant Connection Check ---
try:
    logging.info(f"Connecting to Qdrant at {config['QDRANT_URL']}...")
    qdrant_client = QdrantClient(
        url=config["QDRANT_URL"],
        api_key=config.get("QDRANT_API_KEY"), # API key might be optional depending on setup
        timeout=10 # Set a reasonable timeout
    )
    # Perform a basic operation to check connectivity, e.g., list collections
    collections = qdrant_client.get_collections()
    logging.info(f"Collections available: {collections}")
    logging.info("Successfully connected to Qdrant and verified reachability.")
except Exception as e:
    logging.error(f"Failed to connect to or interact with Qdrant: {e}")
    # Depending on requirements, this might be a warning or an error
    # exit(1)

logging.info("Setup script completed successfully.")