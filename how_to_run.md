Windows 11:

1st the setup:
Run setup.py
python -m alembic upgrade head

4 different powershell:

1. docker run --name redis -d -p 6380:6379 redis:alpine
2. python -m celery -A backend.celery_app:celery worker --loglevel=info -P eventlet
3. $env:FLASK_APP="backend" then flask run --debug
4. cd frontend then npm start ( after npm install)

Can you add "python -m alembic upgrade head" to the setup script?
