#!/usr/bin/env python3
"""
Test script for segmentation mask functionality in the bounding box system.
This script tests the complete flow from creating an experiment with segmentation
to verifying that masks are properly stored and can be retrieved.
"""

import requests
import json
import base64
import time
from PIL import Image
import io

# Configuration
BASE_URL = "http://localhost:5000/api/v1/boundingbox"
TEST_IMAGE_SIZE = (640, 480)

def create_test_image():
    """Create a simple test image with colored rectangles."""
    # Create a simple test image with different colored rectangles
    img = Image.new('RGB', TEST_IMAGE_SIZE, color='white')

    # Draw some colored rectangles that should be detected
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)

    # Red rectangle
    draw.rectangle([50, 50, 200, 150], fill='red')
    # Blue rectangle
    draw.rectangle([300, 100, 450, 200], fill='blue')
    # Green rectangle
    draw.rectangle([150, 250, 300, 350], fill='green')

    # Save to a BytesIO buffer
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)
    return buffer

def test_segmentation_functionality():
    """Test the complete segmentation mask functionality."""
    print("🧪 Testing Segmentation Mask Functionality")
    print("=" * 50)
    
    # Step 1: Create a test image
    print("1. Creating test image...")
    test_image_buffer = create_test_image()
    print("✅ Test image created")

    # Step 2: Upload the test image
    print("\n2. Uploading test image...")
    files = {
        'picture': ('test_segmentation_image.png', test_image_buffer, 'image/png')
    }

    response = requests.post(f"{BASE_URL}/pictures", files=files)
    if response.status_code != 201:
        print(f"❌ Failed to upload image: {response.status_code} - {response.text}")
        return False
    
    picture_data = response.json()
    picture_id = picture_data['id']
    print(f"✅ Image uploaded with ID: {picture_id}")
    
    # Step 3: Check if there are any active models
    print("\n3. Checking for active models...")
    response = requests.get(f"{BASE_URL}/models")
    if response.status_code != 200:
        print(f"❌ Failed to get models: {response.status_code}")
        return False
    
    models = response.json()
    active_models = [m for m in models if m.get('is_active', False)]
    
    if not active_models:
        print("⚠️  No active models found. Creating a test model...")
        # Create a test model
        model_data = {
            "name": "test-segmentation-model",
            "description": "Test model for segmentation functionality",
            "is_active": True
        }
        response = requests.post(f"{BASE_URL}/models", json=model_data)
        if response.status_code != 201:
            print(f"❌ Failed to create test model: {response.status_code}")
            return False
        print("✅ Test model created")
    else:
        print(f"✅ Found {len(active_models)} active models")
    
    # Step 4: Create experiment with segmentation masks
    print("\n4. Creating experiment with segmentation masks...")
    experiment_data = {
        "picture_id": picture_id,
        "prompt": "Detect colored rectangles in this image",
        "resize_height": 512,
        "resize_width": 512,
        "output_type": "Bounding Box + Segmentation Task"
    }
    
    response = requests.post(f"{BASE_URL}/experiments", json=experiment_data)
    if response.status_code != 201:
        print(f"❌ Failed to create experiment: {response.status_code} - {response.text}")
        return False
    
    experiment_data = response.json()
    experiment_id = experiment_data['id']
    print(f"✅ Experiment created with ID: {experiment_id}")
    
    # Step 5: Wait for processing and check results
    print("\n5. Waiting for AI processing...")
    max_wait_time = 60  # Wait up to 60 seconds
    start_time = time.time()

    experiment = None
    while time.time() - start_time < max_wait_time:
        # Get experiments for this picture
        response = requests.get(f"{BASE_URL}/experiments", params={"picture_id": picture_id})
        if response.status_code != 200:
            print(f"❌ Failed to get experiments: {response.status_code}")
            return False

        experiments = response.json()
        # Find our experiment
        experiment = next((exp for exp in experiments if exp['id'] == experiment_id), None)
        if not experiment:
            print(f"❌ Could not find experiment {experiment_id}")
            return False

        results = experiment.get('results', [])

        # Check if any results are completed
        completed_results = [r for r in results if r['status'] in ['success', 'failed']]
        if completed_results:
            print(f"✅ Processing completed. Found {len(completed_results)} results")
            break

        print("⏳ Still processing... waiting 5 seconds")
        time.sleep(5)
    else:
        print("⚠️  Processing took longer than expected, but continuing with test...")
        # Get the latest state
        response = requests.get(f"{BASE_URL}/experiments", params={"picture_id": picture_id})
        if response.status_code == 200:
            experiments = response.json()
            experiment = next((exp for exp in experiments if exp['id'] == experiment_id), None)

    # Step 6: Check results for segmentation masks
    print("\n6. Checking results for segmentation masks...")
    if not experiment:
        print("❌ Could not retrieve experiment data")
        return False
    results = experiment.get('results', [])
    
    segmentation_found = False
    for result in results:
        print(f"\n   Result ID {result['id']} (Model: {result.get('model_name', 'Unknown')}):")
        print(f"   Status: {result['status']}")
        
        if result['status'] == 'success':
            # Check for bounding boxes
            if result.get('bounding_boxes'):
                try:
                    bboxes = json.loads(result['bounding_boxes'])
                    print(f"   ✅ Bounding boxes: {len(bboxes) if isinstance(bboxes, list) else 'Invalid format'}")
                except:
                    print("   ❌ Invalid bounding boxes format")
            
            # Check for segmentation masks
            if result.get('segmentation_masks'):
                try:
                    masks = json.loads(result['segmentation_masks'])
                    if masks and isinstance(masks, list):
                        print(f"   ✅ Segmentation masks: {len(masks)} masks found")
                        for i, mask in enumerate(masks):
                            if isinstance(mask, dict):
                                label = mask.get('label', 'Unknown')
                                has_mask_data = 'mask_base64' in mask and mask['mask_base64']
                                print(f"      Mask {i+1}: {label} - {'✅' if has_mask_data else '❌'} mask data")
                        segmentation_found = True
                    else:
                        print("   ❌ No segmentation masks found")
                except Exception as e:
                    print(f"   ❌ Error parsing segmentation masks: {e}")
            else:
                print("   ⚠️  No segmentation_masks field in result")
        
        elif result['status'] == 'failed':
            error_msg = result.get('error_message', 'No error message')
            print(f"   ❌ Failed: {error_msg}")
    
    # Step 7: Test frontend API endpoint
    print("\n7. Testing frontend data retrieval...")
    response = requests.get(f"{BASE_URL}/experiments")
    if response.status_code == 200:
        experiments = response.json()
        print(f"✅ Frontend API working - found {len(experiments)} total experiments")
    else:
        print(f"❌ Frontend API failed: {response.status_code}")
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 TEST SUMMARY")
    print("=" * 50)
    
    if segmentation_found:
        print("✅ SUCCESS: Segmentation mask functionality is working!")
        print("   - Image upload: ✅")
        print("   - Experiment creation: ✅") 
        print("   - AI processing: ✅")
        print("   - Segmentation masks stored: ✅")
        print("   - Data retrieval: ✅")
    else:
        print("⚠️  PARTIAL SUCCESS: Basic functionality works, but no segmentation masks were generated")
        print("   This could be due to:")
        print("   - AI model limitations")
        print("   - Network connectivity issues")
        print("   - API key configuration")
        print("   - Mock data being returned instead of real AI results")
    
    return segmentation_found

if __name__ == "__main__":
    try:
        success = test_segmentation_functionality()
        exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
