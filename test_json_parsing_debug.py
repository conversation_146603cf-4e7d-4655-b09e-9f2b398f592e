#!/usr/bin/env python3
"""
Test script to debug the JSON parsing issue with gemini-2.5-flash response.
"""

import json
import re

def test_actual_response():
    """Test parsing the actual response from gemini-2.5-flash."""
    print("🔍 Testing Actual Response from gemini-2.5-flash")
    print("=" * 60)
    
    # This is the actual response you showed (truncated)
    actual_response = '''[
  {
    "box_2d": [482, 0, 967, 978],
    "label": "pelican",
    "mask": "iVBORw0KGgoAAAANSUhEUgAAArwAAAEzCAYAAAD829jGAAAAAXNSR0IArs4c6QAAAyZJREFUeJzt3UuOFEUYQNG3wA/gB3gB/gB+gB/gB/gB/gB+gB/gB/gB/gB+gB/gB/gB/gB+gB/gB/gB/gB+gB/gB/gB+gB/gB/gB/gB/gB+gB/gB/gB/gB/gB/gB/gB/gB/gB/..."
  }
]'''
    
    print(f"Response length: {len(actual_response)} characters")
    print(f"Contains mask field: {'mask' in actual_response}")
    print(f"Starts with '[': {actual_response.strip().startswith('[')}")
    print(f"Ends with ']': {actual_response.strip().endswith(']')}")
    print()
    
    # Test direct JSON parsing
    print("1. Testing direct JSON parsing...")
    try:
        result = json.loads(actual_response)
        print(f"   ✅ SUCCESS: Parsed {len(result)} objects")
        for i, obj in enumerate(result):
            print(f"      Object {i+1}: {obj.get('label', 'Unknown')}")
            print(f"         Has box_2d: {'box_2d' in obj}")
            print(f"         Has mask: {'mask' in obj}")
            if 'mask' in obj:
                mask_length = len(obj['mask'])
                print(f"         Mask length: {mask_length} characters")
        return result
    except json.JSONDecodeError as e:
        print(f"   ❌ FAILED: {e}")
        print(f"   Error position: {e.pos if hasattr(e, 'pos') else 'unknown'}")
    
    return None

def test_truncated_response():
    """Test what happens with a truncated response (common issue)."""
    print("\n🔍 Testing Truncated Response Scenario")
    print("=" * 60)
    
    # Simulate a truncated response (very common with long base64 strings)
    truncated_response = '''[
  {
    "box_2d": [482, 0, 967, 978],
    "label": "pelican",
    "mask": "iVBORw0KGgoAAAANSUhEUgAAArwAAAEzCAYAAAD829jGAAAAAXNSR0IArs4c6QAAAyZJREFUeJzt3UuOFEUYQNG3wA/gB3gB/gB+gB/gB/gB/gB+gB/gB/gB/gB+gB/gB/gB/gB+gB/gB/gB/gB+gB/gB/gB+gB/gB/gB/gB/gB+gB/gB/gB/gB/gB/gB/gB/gB/gB/'''
    
    print(f"Truncated response length: {len(truncated_response)} characters")
    print(f"Ends properly: {truncated_response.strip().endswith(']')}")
    print()
    
    # Test direct parsing (should fail)
    print("1. Testing direct JSON parsing on truncated...")
    try:
        result = json.loads(truncated_response)
        print(f"   ✅ Unexpected success: {result}")
    except json.JSONDecodeError as e:
        print(f"   ❌ Expected failure: {e}")
    
    # Test recovery strategy
    print("\n2. Testing recovery strategy...")
    try:
        # Strategy: Remove incomplete mask and try again
        if '"mask"' in truncated_response:
            # Find the last complete quote before truncation
            lines = truncated_response.split('\n')
            fixed_lines = []
            
            for line in lines:
                if '"mask"' in line and not line.strip().endswith('"') and not line.strip().endswith('",'):
                    # This line has an incomplete mask, remove it
                    print(f"   🔧 Removing incomplete mask line: {line.strip()[:50]}...")
                    continue
                fixed_lines.append(line)
            
            # Ensure proper JSON closure
            fixed_response = '\n'.join(fixed_lines)
            if not fixed_response.strip().endswith('}]'):
                # Add proper closure
                if fixed_response.strip().endswith(','):
                    fixed_response = fixed_response.strip()[:-1]  # Remove trailing comma
                if not fixed_response.strip().endswith('}'):
                    fixed_response += '\n  }'
                if not fixed_response.strip().endswith('}]'):
                    fixed_response += '\n]'
            
            print(f"   🔧 Fixed response length: {len(fixed_response)} characters")
            
            # Try parsing the fixed version
            result = json.loads(fixed_response)
            print(f"   ✅ Recovery SUCCESS: Parsed {len(result)} objects")
            for i, obj in enumerate(result):
                print(f"      Object {i+1}: {obj.get('label', 'Unknown')}")
                print(f"         Has box_2d: {'box_2d' in obj}")
                print(f"         Has mask: {'mask' in obj}")
            return result
            
    except Exception as e:
        print(f"   ❌ Recovery failed: {e}")
    
    return None

def test_parsing_strategies():
    """Test different parsing strategies."""
    print("\n🔍 Testing Enhanced Parsing Strategies")
    print("=" * 60)
    
    # Test cases that might occur
    test_cases = [
        {
            "name": "Complete valid JSON",
            "response": '[{"box_2d": [100, 200, 300, 400], "label": "test", "mask": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="}]'
        },
        {
            "name": "JSON without mask",
            "response": '[{"box_2d": [100, 200, 300, 400], "label": "test"}]'
        },
        {
            "name": "Truncated at mask",
            "response": '[{"box_2d": [100, 200, 300, 400], "label": "test", "mask": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"{i}. {case['name']}:")
        response = case['response']
        
        # Try direct parsing
        try:
            result = json.loads(response)
            print(f"   ✅ Direct parsing: SUCCESS")
            print(f"      Objects: {len(result)}")
        except json.JSONDecodeError:
            print(f"   ❌ Direct parsing: FAILED")
            
            # Try recovery
            try:
                # Simple recovery: remove incomplete fields and fix structure
                fixed = response
                
                # If it doesn't end with ], try to fix it
                if not fixed.strip().endswith(']'):
                    # Remove incomplete mask if present
                    if '"mask"' in fixed:
                        # Find the start of the incomplete mask
                        mask_start = fixed.rfind('"mask"')
                        if mask_start != -1:
                            # Remove everything from the mask onwards
                            fixed = fixed[:mask_start].rstrip(', ')
                    
                    # Add proper closure
                    if not fixed.strip().endswith('}'):
                        fixed += '}'
                    if not fixed.strip().endswith('}]'):
                        fixed += ']'
                
                result = json.loads(fixed)
                print(f"   ✅ Recovery parsing: SUCCESS")
                print(f"      Objects: {len(result)}")
                
            except Exception as e:
                print(f"   ❌ Recovery parsing: FAILED - {e}")
        
        print()

def main():
    """Main test function."""
    print("🔧 JSON Parsing Debug Test")
    print("=" * 50)
    print("Debugging the gemini-2.5-flash JSON parsing issue")
    print()
    
    # Test the actual response
    result1 = test_actual_response()
    
    # Test truncated scenario
    result2 = test_truncated_response()
    
    # Test various strategies
    test_parsing_strategies()
    
    print("\n📋 SUMMARY")
    print("=" * 30)
    if result1:
        print("✅ The actual response IS valid JSON")
        print("   The parsing error might be due to:")
        print("   - Response truncation by the system")
        print("   - Character encoding issues")
        print("   - Memory/length limits")
    else:
        print("❌ The actual response has JSON issues")
        print("   Need to implement recovery strategies")
    
    print("\n🔧 RECOMMENDED FIXES:")
    print("1. Add truncation detection and recovery")
    print("2. Implement progressive parsing strategies")
    print("3. Handle incomplete base64 mask data gracefully")
    print("4. Preserve bounding boxes even when masks fail")

if __name__ == "__main__":
    main()
