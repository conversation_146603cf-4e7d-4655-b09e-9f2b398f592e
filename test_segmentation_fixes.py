#!/usr/bin/env python3
"""
Test script to verify all segmentation mask fixes are working.
"""

import asyncio
import os
import sys
import json
from io import BytesIO
from PIL import Image, ImageDraw

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def create_test_image():
    """Create a simple test image with distinct objects."""
    img = Image.new('RGB', (640, 640), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # Draw two distinct pelican-like shapes
    # First pelican (left)
    draw.ellipse([100, 200, 200, 300], fill='black', outline='darkgray', width=2)  # Body
    draw.polygon([(80, 220), (100, 200), (90, 180)], fill='black')  # Wing
    
    # Second pelican (right)
    draw.ellipse([400, 150, 500, 250], fill='black', outline='darkgray', width=2)  # Body
    draw.polygon([(520, 170), (500, 150), (510, 130)], fill='black')  # Wing
    
    return img

async def test_model_processing(model_name):
    """Test a specific model for segmentation processing."""
    print(f"\n🧪 Testing {model_name}")
    print("=" * 60)
    
    try:
        # Import the AI function
        from backend.AI.GC_VertexAI_Simple import vertex_genai_bbox_mask_async
        
        # Create test image
        test_img = create_test_image()
        
        # Convert to OpenCV format (numpy array)
        import numpy as np
        import cv2
        img_array = np.array(test_img)
        img_cv2 = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        
        # Prepare data for AI function
        data_list = [
            ('text', 'Find all pelicans in this image'),
            ('image_cv2', img_cv2)
        ]
        
        # Test with segmentation masks requested
        print(f"🔍 Calling {model_name} with segmentation masks...")
        result = await vertex_genai_bbox_mask_async(
            data_list=data_list,
            model_name=model_name,
            include_masks=True
        )
        
        print(f"📋 Result type: {type(result)}")
        
        if isinstance(result, dict) and 'error' in result:
            print(f"❌ Error: {result['error']}")
            if 'details' in result:
                print(f"   Details: {result['details']}")
            return False
        elif isinstance(result, list):
            print(f"✅ Success: Found {len(result)} objects")
            
            bboxes_found = 0
            masks_found = 0
            
            for i, item in enumerate(result):
                if isinstance(item, dict):
                    label = item.get('label', 'Unknown')
                    has_bbox = 'box_2d' in item
                    has_mask = 'mask' in item
                    
                    print(f"   Object {i+1}: {label}")
                    print(f"      Bounding box: {'✅' if has_bbox else '❌'}")
                    print(f"      Segmentation mask: {'✅' if has_mask else '❌'}")
                    
                    if has_bbox:
                        bboxes_found += 1
                        bbox = item['box_2d']
                        print(f"         Coordinates: {bbox}")
                    
                    if has_mask:
                        masks_found += 1
                        mask_data = item['mask']
                        print(f"         Mask data length: {len(str(mask_data))}")
                        print(f"         Starts with data URI: {str(mask_data).startswith('data:image')}")
            
            print(f"📊 Summary: {bboxes_found} bounding boxes, {masks_found} segmentation masks")
            
            if bboxes_found == 0:
                print(f"⚠️  WARNING: {model_name} did not detect any objects!")
            elif masks_found == 0:
                print(f"⚠️  INFO: {model_name} detected objects but no segmentation masks")
            else:
                print(f"🎉 SUCCESS: {model_name} generated both bounding boxes and segmentation masks!")
            
            return bboxes_found > 0
        else:
            print(f"❓ Unexpected result format: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Exception testing {model_name}: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mask_validation():
    """Test mask validation logic."""
    print("\n🔍 Testing Mask Validation Logic")
    print("=" * 40)
    
    # Import validation function
    try:
        from backend.AI.GC_VertexAI_Simple import _validate_base64_image
        
        test_cases = [
            {
                "name": "Valid PNG base64",
                "data": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
                "expected": True
            },
            {
                "name": "Valid data URI",
                "data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
                "expected": True
            },
            {
                "name": "Invalid base64",
                "data": "invalid_base64_data",
                "expected": False
            },
            {
                "name": "Refusal message",
                "data": "I cannot generate pixel-level segmentation mask data",
                "expected": False
            },
            {
                "name": "RLE format",
                "data": "rle_mask",
                "expected": False
            },
            {
                "name": "Segmentation tokens",
                "data": "<start_of_mask><seg_41><seg_52>",
                "expected": False
            }
        ]
        
        for test in test_cases:
            result = _validate_base64_image(test["data"])
            status = "✅" if result == test["expected"] else "❌"
            print(f"   {status} {test['name']}: {result} (expected {test['expected']})")
        
        print("✅ Mask validation tests completed")
        
    except ImportError as e:
        print(f"❌ Could not import validation function: {e}")

async def main():
    """Main test function."""
    print("🔧 Comprehensive Segmentation Fixes Test")
    print("=" * 50)
    print("Testing all fixes for segmentation mask issues")
    print()
    
    # Test mask validation first
    test_mask_validation()
    
    # Test the models that were having issues
    test_models = [
        "gemini-2.5-pro",  # Was stuck on pending
        "gemini-2.5-flash",  # Was refusing to generate masks
        "gemini-2.5-flash-preview-05-20",  # Was generating blank masks
        "gemini-2.5-flash-lite-preview-06-17"  # Should still work
    ]
    
    results = {}
    for model in test_models:
        success = await test_model_processing(model)
        results[model] = success
    
    print(f"\n📋 FINAL SUMMARY")
    print("=" * 30)
    
    working_models = []
    failing_models = []
    
    for model, success in results.items():
        if success:
            working_models.append(model)
            print(f"✅ {model}: Working")
        else:
            failing_models.append(model)
            print(f"❌ {model}: Failed")
    
    print(f"\n📊 STATISTICS:")
    print(f"   Working models: {len(working_models)}/{len(test_models)}")
    print(f"   Failed models: {len(failing_models)}/{len(test_models)}")
    
    if len(working_models) == len(test_models):
        print(f"\n🎉 ALL FIXES SUCCESSFUL!")
        print("   All models are now working correctly")
    elif len(working_models) > 0:
        print(f"\n⚠️  PARTIAL SUCCESS")
        print("   Some models are working, others may need additional fixes")
    else:
        print(f"\n❌ FIXES NEED MORE WORK")
        print("   No models are working correctly")
    
    print(f"\n🔧 NEXT STEPS:")
    if failing_models:
        print("   1. Check backend logs for detailed error messages")
        print("   2. Verify API keys and model availability")
        print("   3. Test with actual web interface")
    else:
        print("   1. Test with actual web interface")
        print("   2. Create experiments with 'Bounding Box + Segmentation Task'")
        print("   3. Verify segmentation masks display correctly")

if __name__ == "__main__":
    asyncio.run(main())
