# backend/tasks/combined_scores.py
import logging
import uuid
from collections import defaultdict
from celery import shared_task
from sqlalchemy.orm import joinedload, Session # Keep Session for type hinting
from sqlalchemy import select, and_
from sqlalchemy.dialects.postgresql import insert as pg_insert
from sqlalchemy import func
from typing import List, Dict, Any

# Import retry utility
from backend.utils.retry_utils import execute_with_retry, DB_RETRYABLE_EXCEPTIONS

# Import actual models and session management from the project structure
from backend.database.models import (
    ModelTestsImage, ModelTestsComparisonResult, ModelTestsCombinedScoresConfig
)
from backend.extensions import db # Import shared db session
# Import bulk insert helper if refactored, otherwise define locally or use simple add
# from .comparison_execution import bulk_insert_results # Assuming it's moved/reusable

logger = logging.getLogger(__name__)

# --- Helper for Bulk Insert (if not imported) ---
# Reusing the logic from comparison_execution, adapted slightly
def bulk_insert_combined_results(db_session: Session, results_data: List[Dict[str, Any]]):
    """Performs bulk insert/upsert for combined score results."""
    if not results_data:
        return

    logger.info(f"Bulk inserting/upserting {len(results_data)} combined score results.")
    try:
        # Use PostgreSQL's ON CONFLICT DO UPDATE (upsert)
        stmt = pg_insert(ModelTestsComparisonResult).values(results_data)
        update_dict = {
            'similarity_score': stmt.excluded.similarity_score,
            'computed_at': stmt.excluded.computed_at
        }
        stmt = stmt.on_conflict_do_update(
            constraint='uq_comparison_result', # Use constraint name
            set_=update_dict
        )
        # Wrap the execute call with retry logic
        execute_with_retry(lambda: db_session.execute(stmt), DB_RETRYABLE_EXCEPTIONS, session=db_session, operation_name="bulk insert/upsert combined scores")
        logger.info("Bulk insert/upsert statement for combined scores executed.")
    except Exception as e:
        # Rollback is handled by the main task's exception block or if execute_with_retry fails internally
        logger.error(f"Error during bulk insert/upsert for combined scores: {e}", exc_info=True)
        db_session.rollback() # Rollback on error within this helper
        raise # Re-raise

# --- Celery Task ---

@shared_task(bind=True)
def combined_score_task(self, ip_category: str):
    """
    Celery task to calculate combined scores for a given IP category based on
    active configurations and existing comparison results. FR2.5.1
    """
    logger.info(f"Starting combined score task for IP category: {ip_category}")
    # Use db.session from Flask context, no manual session needed

    try:
        # 1. Query active combined score configurations for the category
        # No need for joinedload here as model_weights is JSONB
        configs_stmt = select(ModelTestsCombinedScoresConfig).where(
            ModelTestsCombinedScoresConfig.ip_category == ip_category,
            ModelTestsCombinedScoresConfig.is_active == True
        )
        # Wrap the execute call with retry logic
        configs = execute_with_retry(
            lambda: db.session.execute(configs_stmt).scalars().all(),
            DB_RETRYABLE_EXCEPTIONS,
            session=db.session, # Pass the session object
            operation_name="fetch combined score configs"
        )

        if not configs:
            logger.warning(f"No active combined score configurations found for category: {ip_category}")
            return f"No active combined score configs for {ip_category}."

        logger.info(f"Found {len(configs)} active combined score configurations for category '{ip_category}'.")

        # 2. Get all product and IP images for the category (needed for iteration)
        product_images_stmt = select(ModelTestsImage.image_id).where(
            ModelTestsImage.image_type == 'product',
            ModelTestsImage.ip_category == ip_category # Filter by explicit category
        )
        ip_images_stmt = select(ModelTestsImage.image_id).where(
            ModelTestsImage.image_type == 'ip',
            ModelTestsImage.ip_category == ip_category
        )
        # Wrap the execute calls with retry logic
        product_image_ids = execute_with_retry(
            lambda: db.session.execute(product_images_stmt).scalars().all(),
            DB_RETRYABLE_EXCEPTIONS,
            session=db.session, # Pass the session object
            operation_name="fetch product image IDs for combined scores"
        )
        ip_image_ids = execute_with_retry(
            lambda: db.session.execute(ip_images_stmt).scalars().all(),
            DB_RETRYABLE_EXCEPTIONS,
            session=db.session, # Pass the session object
            operation_name="fetch IP image IDs for combined scores"
        )

        if not product_image_ids or not ip_image_ids:
            logger.warning(f"No product or IP images found for category: {ip_category}")
            return f"No product or IP images for {ip_category}."

        logger.info(f"Processing {len(product_image_ids)} product images and {len(ip_image_ids)} IP images.")

        # 3. Pre-fetch all relevant comparison results for efficiency
        all_constituent_model_ids = set()
        for config in configs:
            if config.model_weights and isinstance(config.model_weights, dict):
                # Keys in model_weights JSONB are string UUIDs
                all_constituent_model_ids.update(uuid.UUID(key) for key in config.model_weights.keys())

        if not all_constituent_model_ids:
             logger.warning(f"No constituent models found in active configs for {ip_category}. Skipping.")
             return f"No constituent models in configs for {ip_category}."

        logger.info(f"Fetching comparison results for {len(all_constituent_model_ids)} constituent models.")
        comparison_results_query = select(ModelTestsComparisonResult).where(
            ModelTestsComparisonResult.product_image_id.in_(product_image_ids),
            ModelTestsComparisonResult.ip_image_id.in_(ip_image_ids),
            ModelTestsComparisonResult.model_id.in_(all_constituent_model_ids) # Filter by actual model results
        )
        # Wrap the execute call with retry logic
        all_comparison_results = execute_with_retry(
            lambda: db.session.execute(comparison_results_query).scalars().all(),
            DB_RETRYABLE_EXCEPTIONS,
            session=db.session, # Pass the session object
            operation_name="fetch comparison results for combined scores"
        )

        # Structure results for quick lookup: {(prod_id, ip_id): {model_id: score}}
        results_map = defaultdict(dict)
        for res in all_comparison_results:
            results_map[(res.product_image_id, res.ip_image_id)][res.model_id] = res.similarity_score
        logger.info(f"Fetched {len(all_comparison_results)} comparison results into map.")


        # 4. Iterate through configurations and calculate combined scores
        all_new_combined_results = []
        for config in configs:
            config_id = config.config_id
            logger.info(f"Processing combined score config ID: {config_id} ({config.config_name})")

            if not config.model_weights or not isinstance(config.model_weights, dict):
                 logger.warning(f"Config ID {config_id} has invalid model_weights. Skipping.")
                 continue

            # Convert string keys from JSONB to UUID for lookup
            component_weights = {uuid.UUID(model_id_str): weight
                                 for model_id_str, weight in config.model_weights.items()}
            component_model_ids = set(component_weights.keys())

            processed_pairs = 0
            config_results = []
            for prod_id in product_image_ids:
                for ip_id in ip_image_ids:
                    pair_key = (prod_id, ip_id)
                    available_scores = results_map.get(pair_key, {})

                    # Check if at least one constituent model has a score for this pair
                    relevant_scores = {model_id: score for model_id, score in available_scores.items() if model_id in component_model_ids}
                    if not relevant_scores:
                        continue # Skip if no relevant scores exist for this pair

                    # Calculate weighted average score (FR2.5.1)
                    weighted_sum = 0.0
                    current_total_weight = 0.0 # Use actual weight sum for available scores

                    for model_id, weight in component_weights.items():
                        score = relevant_scores.get(model_id)
                        if score is not None: # Only include models with a score for this pair
                            weighted_sum += score * weight
                            current_total_weight += weight

                    if current_total_weight > 0: # Avoid division by zero
                        combined_score = weighted_sum / current_total_weight
                        # Ensure score is within [0, 1]
                        combined_score = max(0.0, min(1.0, combined_score))
                        processed_pairs += 1

                        # Prepare result for bulk insert/upsert
                        config_results.append({
                            'product_image_id': prod_id,
                            'ip_image_id': ip_id,
                            'model_id': config_id, # Use config_id as the 'model' identifier
                            'similarity_score': combined_score,
                            'computed_at': func.now() # Use server timestamp
                        })
                    # else: # Optional log
                        # logger.debug(f"Pair ({prod_id}, {ip_id}) had no valid scores for config {config_id} components.")


            logger.info(f"Calculated {processed_pairs} combined scores for config ID: {config_id}")
            all_new_combined_results.extend(config_results)

        # 5. Bulk insert/upsert new combined scores
        if all_new_combined_results:
            bulk_insert_combined_results(db.session, all_new_combined_results)
        else:
            logger.info("No new combined scores were calculated.")
        # Commit transaction after processing all configs
        # Wrap the commit call with retry logic
        execute_with_retry(lambda: db.session.commit(), DB_RETRYABLE_EXCEPTIONS, session=db.session, operation_name="commit combined score results")
        logger.info(f"Committed combined score results for category: {ip_category}")


        logger.info(f"Successfully completed combined score task for IP category: {ip_category}")

    except Exception as e:
        logger.error(f"Error during combined score task for IP category {ip_category}: {e}", exc_info=True)
        db.session.rollback() # Use db.session
        # Handle task failure (e.g., raise retry, log specific error)
        raise self.retry(exc=e, countdown=60) # Example retry
    finally:
        # No manual db close needed when using db.session from Flask context
        pass

    return f"Combined score task completed for {ip_category}"