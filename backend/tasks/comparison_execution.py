# backend/tasks/comparison_execution.py
import logging, os, pickle, cv2, json, uuid
import numpy as np
import importlib # Added for dynamic model loading
import pickle # Ensure pickle is imported
from celery import shared_task
from sqlalchemy.orm import Session # Keep Session for type hinting
from sqlalchemy import select, func, tuple_, or_ # Added or_
from sqlalchemy.dialects.postgresql import insert as pg_insert
from qdrant_client import QdrantClient, models as qdrant_models
from typing import List, Dict, Tuple, Any, Optional
from pathlib import Path # Added import

# Import actual models and session management from the project structure
from backend.database.models import (
    ModelTestsImage, ModelTestsComparisonResult, ModelTestsGroundTruth,
    ModelTestsFeatureStorage, ModelTestsModel, ModelTestsCombinedScoresConfig
)
from backend.utils.vector_store import get_qdrant_client # Assuming this utility exists
from .combined_scores import combined_score_task # Import for chaining
from backend.extensions import db # Import shared db session
# --- Add retry utility import ---
from backend.utils.retry_utils import execute_with_retry, DB_RETRYABLE_EXCEPTIONS, QDRANT_RETRYABLE_EXCEPTIONS

logger = logging.getLogger(__name__)

# --- Model Instance Cache ---
# Cache for model instances to avoid reloading/reinitializing
_model_instance_cache = {}

def get_model_instance(model_id_uuid: uuid.UUID, model_config: Dict[str, Any]) -> Optional[Any]:
    """
    Dynamically loads and instantiates a model class based on configuration.
    Caches instances to avoid redundant loading.

    Args:
        model_id_uuid (uuid.UUID): The UUID of the model.
        model_config (Dict[str, Any]): The configuration dictionary for this model
                                         (from the global model_configs_dict, which is
                                          derived from models/config.json).

    Returns:
        Optional[Any]: An instance of the model class, or None if loading fails.
    """
    model_id_str = str(model_id_uuid)
    if model_id_str in _model_instance_cache:
        logger.debug(f"Returning cached model instance for {model_id_str}")
        return _model_instance_cache[model_id_str]

    if not model_config:
        logger.error(f"No configuration provided for model ID {model_id_str}. Cannot instantiate.")
        return None

    implementation_details = model_config.get('implementation')
    if not implementation_details:
        logger.error(f"Missing 'implementation' details in config for model ID {model_id_str}.")
        return None

    module_name = implementation_details.get('module')
    class_name = implementation_details.get('class')
    parameters = implementation_details.get('parameters', {}) # Pass parameters to constructor

    if not module_name or not class_name:
        logger.error(f"Missing 'module' or 'class' in implementation config for model ID {model_id_str}.")
        return None

    try:
        logger.info(f"Attempting to load model class: {module_name}.{class_name} for model ID {model_id_str}")
        module = importlib.import_module(module_name)
        ModelClass = getattr(module, class_name)
        # The model class __init__ expects model_id (string) and its specific parameters
        instance = ModelClass(model_id=model_id_str, config=parameters) # Pass parameters as 'config'
        # instance.load() # Ensure model's internal resources are loaded
        _model_instance_cache[model_id_str] = instance
        logger.info(f"Successfully loaded and cached model instance for {model_id_str} ({class_name})")
        return instance
    except (ImportError, AttributeError) as e:
        logger.error(f"Failed to import or find class {class_name} in module {module_name} for model ID {model_id_str}: {e}", exc_info=True)
    except Exception as e:
        logger.error(f"Failed to instantiate or load model {class_name} for model ID {model_id_str}: {e}", exc_info=True)
    
    return None


# --- Helper Functions ---

def compute_hamming_distance(hash1: np.ndarray, hash2: np.ndarray) -> int:
    """Computes the Hamming distance between two binary numpy arrays."""
    if not isinstance(hash1, np.ndarray) or not isinstance(hash2, np.ndarray):
        # Attempt conversion if not numpy array (e.g., from deserialized bytes)
        try:
            # Assuming hashes are stored/deserialized in a way that can be converted
            # This might need adjustment based on how hashes are actually stored/retrieved
            hash1 = np.frombuffer(hash1, dtype=np.uint8) if isinstance(hash1, bytes) else np.array(hash1)
            hash2 = np.frombuffer(hash2, dtype=np.uint8) if isinstance(hash2, bytes) else np.array(hash2)
        except Exception as e:
            raise ValueError(f"Could not convert hashes to numpy arrays: {e}")

    if hash1.shape != hash2.shape:
        raise ValueError(f"Hashes must have the same shape. Got {hash1.shape} and {hash2.shape}")
    # Ensure boolean or integer type suitable for bitwise XOR
    return np.count_nonzero(np.bitwise_xor(hash1.astype(np.uint8), hash2.astype(np.uint8)))

def deserialize_feature(feature_data: bytes, model_type: str) -> Any:
    """
    Deserializes feature data based on model type.
    Uses np.frombuffer for 'hash' type, pickle for others.
    """
    try:
        if model_type == 'hash': # Hashes are stored as raw bytes (np.uint8)
            # Assume hashes are stored as raw bytes (e.g., from ndarray.tobytes())
            # uint8 is typical for binary hashes after packing/unpacking bits if needed
            return np.frombuffer(feature_data, dtype=np.uint8)
        elif model_type == 'descriptor': # Descriptors are stored as pickled numpy arrays
            # Descriptors might be pickled numpy arrays or other structures
            # Using pickle is a safer default unless a specific format (like raw bytes) is guaranteed
            return pickle.loads(feature_data)
        else:
            # Fallback or handle other types if necessary
            logger.warning(f"Deserializing unknown model_type '{model_type}' using pickle.")
            # If other types are not pickled, this might fail.
            # Based on feature_computation, only hash and descriptor are stored in DB.
            # Removing this fallback to avoid confusion.
            logger.error(f"Attempted to deserialize feature data for unsupported model_type '{model_type}'.")
            return None
    except (pickle.UnpicklingError, EOFError) as e: # Catch pickle errors specifically
        logger.error(f"Failed to deserialize feature data (type: {model_type}) using pickle: {e}", exc_info=True)
        return None
    except ValueError as e: # Catch numpy errors specifically (primarily for hash)
        logger.error(f"Failed to deserialize feature data (type: {model_type}) using numpy: {e}", exc_info=True)
        return None
    except Exception as e: # Catch other potential errors
        logger.error(f"Unexpected error during feature deserialization (type: {model_type}): {e}", exc_info=True)
        return None

# Pass db session explicitly and use correct types (UUID)
# Add model_type parameter
def get_features_from_db(db_session: Session, model_id: uuid.UUID, model_type: str, image_ids: List[uuid.UUID]) -> Dict[uuid.UUID, Any]:
    """Retrieves and deserializes features from ModelTestsFeatureStorage based on model_type."""
    features = {}
    if not image_ids: # Handle empty list case
        return features
    stmt = select(ModelTestsFeatureStorage.image_id, ModelTestsFeatureStorage.features).where(
        ModelTestsFeatureStorage.model_id == model_id,
        ModelTestsFeatureStorage.image_id.in_(image_ids)
    )
    # --- Wrap DB call ---
    results = execute_with_retry(
        lambda: db_session.execute(stmt).fetchall(),
        DB_RETRYABLE_EXCEPTIONS,
        session=db_session, # Pass the session object
        operation_name="get features from DB"
    )
    for img_id, data in results:
        # Pass model_type to the deserialization function
        deserialized = deserialize_feature(data, model_type)
        if deserialized is not None:
            features[img_id] = deserialized
        else:
            # Log failure for specific image/model
            logger.warning(f"Failed to deserialize feature for image {img_id}, model {model_id} (type: {model_type}).")
    logger.info(f"Retrieved {len(features)} features for model {model_id} (type: {model_type}) from DB.")
    return features

# Update return type hint to use UUID keys
def get_all_vectors_from_qdrant(client: QdrantClient, collection_name: str, vector_size: int) -> Dict[uuid.UUID, List[float]]:
    """Retrieves all vectors from a Qdrant collection with scrolling."""
    vectors = {}
    offset = None
    limit = 1000 # Adjust scroll size as needed

    logger.info(f"Starting vector retrieval from Qdrant collection: {collection_name}")

    while True:
        try:
            # --- Wrap Qdrant call ---
            scroll_result, next_offset = execute_with_retry(
                lambda: client.scroll(
                    collection_name=collection_name,
                    limit=limit,
                offset=offset,
                    with_payload=False, # Only need IDs and vectors
                    with_vectors=True
                ),
                QDRANT_RETRYABLE_EXCEPTIONS
            )
            if not scroll_result:
                logger.info("Scroll result is empty, breaking loop.")
                break

            count = 0
            for point in scroll_result:
                 # Qdrant point IDs are string UUIDs, convert back to UUID
                try:
                    img_id = uuid.UUID(point.id)
                except ValueError:
                    logger.warning(f"Skipping point with invalid UUID ID ({point.id}) in {collection_name}")
                    continue

                if point.vector is not None:
                    # Ensure vector has the expected dimension, pad if necessary (though ideally shouldn't happen)
                    vec = point.vector
                    if len(vec) < vector_size:
                        logger.warning(f"Padding vector for image {img_id} in {collection_name}. Original size: {len(vec)}, Target: {vector_size}")
                        vec = np.pad(vec, (0, vector_size - len(vec))).tolist()
                    elif len(vec) > vector_size:
                         logger.warning(f"Truncating vector for image {img_id} in {collection_name}. Original size: {len(vec)}, Target: {vector_size}")
                         vec = vec[:vector_size]
                    vectors[img_id] = vec
                    count += 1
                else:
                    # Log if vector is missing, but don't skip the point ID if needed elsewhere
                    logger.warning(f"Point ID {img_id} has missing vector in {collection_name}")

            logger.debug(f"Retrieved {count} vectors in this scroll batch from {collection_name}.")

            if next_offset is None:
                logger.info(f"Reached end of scroll for {collection_name}.")
                break
            offset = next_offset

        except Exception as e: # Catch exceptions from execute_with_retry or other logic
            logger.error(f"Error scrolling Qdrant collection {collection_name}: {e}", exc_info=True)
            # Depending on the error, you might want to break or retry
            break # Stop retrieval on error

    logger.info(f"Retrieved a total of {len(vectors)} vectors from Qdrant collection: {collection_name}")
    return vectors


# Pass db session explicitly
def bulk_insert_results(db_session: Session, results_data: List[Dict[str, Any]]):
    """Performs bulk insert/upsert for comparison results."""
    if not results_data:
        return

    logger.info(f"Bulk inserting/upserting {len(results_data)} comparison results.")
    try:
        # Use PostgreSQL's ON CONFLICT DO UPDATE (upsert)
        stmt = pg_insert(ModelTestsComparisonResult).values(results_data)
        # Define the columns to update on conflict and how to update them
        # Assumes unique constraint on (product_image_id, ip_image_id, model_id)
        update_dict = {
            # Use correct column name 'similarity_score'
            'similarity_score': stmt.excluded.similarity_score,
            'computed_at': stmt.excluded.computed_at # Ensure computed_at is updated
        }
        stmt = stmt.on_conflict_do_update(
            constraint='uq_comparison_result', # Use constraint name for clarity
            set_=update_dict
        )
        # --- Wrap DB call ---
        execute_with_retry(
            lambda: db_session.execute(stmt),
            DB_RETRYABLE_EXCEPTIONS,
            session=db_session, # Pass the session object
            operation_name="bulk insert/upsert comparison results"
        )
        # Commit should happen at the end of the task or logical unit, not here
        # db_session.commit()
        logger.info("Bulk insert/upsert statement executed.")
    except Exception as e: # Catch exceptions from execute_with_retry or other logic
        logger.error(f"Error during bulk insert/upsert execution: {e}", exc_info=True)
        # Rollback is handled by the main task's exception block
        # db_session.rollback() # Rollback on error
        # Re-raise or handle as appropriate for the task
        raise

# --- Celery Task ---

@shared_task(bind=True, max_retries=3)
def comparison_task(self, ip_category: str):
    """
    Celery task to execute comparisons for a given IP category using applicable models.
    Triggers combined_score_task upon successful completion.
    """
    logger.info(f"Starting comparison task for IP category: {ip_category}")
    # Clear model instance cache at the start of each task run if desired,
    # or manage its lifecycle more granularly if models are very heavy.
    # For simplicity, let's clear it here to ensure fresh state for each task execution.
    global _model_instance_cache
    _model_instance_cache = {}
    logger.info("Cleared model instance cache for new task run.")

    qdrant_client = None # Initialize later if needed
    TOP_N = 200

    try:
        # --- Load Model Configuration ---
        config_path = Path('models/config.json')
        if not config_path.exists():
            logger.error(f"Model configuration file not found at {config_path}")
            raise FileNotFoundError(f"Model configuration file not found at {config_path}")
        try:
            with open(config_path, 'r') as f:
                all_model_configs_list = json.load(f)
            # Create a lookup dictionary by model_id (UUID string)
            model_configs_dict = {str(uuid.uuid5(uuid.NAMESPACE_DNS, cfg['model_id'])): cfg for cfg in all_model_configs_list}
            logger.info(f"Loaded {len(model_configs_dict)} model configurations from {config_path}")
        except (json.JSONDecodeError, IOError, KeyError) as e:
            logger.error(f"❌ Error loading or parsing model configuration file {config_path}: {e}", exc_info=True)
            raise # Re-raise to fail the task

        # Use db.session from Flask context
        # 1. Query applicable models and images
        models_query = select(ModelTestsModel).where(
            ModelTestsModel.is_active == True,
            or_(
                ModelTestsModel.applicable_ip_category.any(ip_category),
                ModelTestsModel.applicable_ip_category.any('all')
            )
        )
        # --- Wrap DB call ---
        all_models = execute_with_retry(
            lambda: db.session.execute(models_query).scalars().all(),
            DB_RETRYABLE_EXCEPTIONS,
            session=db.session, # Pass the session object
            operation_name="fetch applicable models"
        )

        # Filter models by type string
        embedding_models = [m for m in all_models if m.model_type == 'embedding']
        descriptor_models = [m for m in all_models if m.model_type == 'descriptor']
        hash_models = [m for m in all_models if m.model_type == 'hash']
        logger.info(f"Found {len(embedding_models)} embedding, {len(descriptor_models)} descriptor, {len(hash_models)} hash models for category '{ip_category}'.")

        # Use ModelTestsImage and filter by image_type and ip_category/relative_path
        product_images_stmt = select(ModelTestsImage.image_id).where(
            ModelTestsImage.image_type == 'product',
            ModelTestsImage.ip_category == ip_category # Filter directly by stored ip_category
        )
        ip_images_stmt = select(ModelTestsImage.image_id).where(
            ModelTestsImage.image_type == 'ip',
            ModelTestsImage.ip_category == ip_category
        )
        # --- Wrap DB calls ---
        product_image_ids = execute_with_retry(
            lambda: db.session.execute(product_images_stmt).scalars().all(),
            DB_RETRYABLE_EXCEPTIONS,
            session=db.session, # Pass the session object
            operation_name="fetch product image IDs"
        )
        ip_image_ids = execute_with_retry(
            lambda: db.session.execute(ip_images_stmt).scalars().all(),
            DB_RETRYABLE_EXCEPTIONS,
            session=db.session, # Pass the session object
            operation_name="fetch IP image IDs"
        )
        logger.info(f"Processing {len(product_image_ids)} product images and {len(ip_image_ids)} IP images.")

        if not product_image_ids or not ip_image_ids:
            logger.warning(f"No product or IP images found for category {ip_category}. Skipping comparison.")
            # db.close() # No manual close needed with db.session
            return f"No images to compare for {ip_category}"

        # --- Embedding Models Loop ---
        if embedding_models:
            qdrant_client = get_qdrant_client() # Initialize Qdrant client
            if not qdrant_client:
                 logger.error("Failed to initialize Qdrant client. Skipping embedding models.")
                 embedding_models = [] # Skip the loop

            # Define batch size for database inserts within the task scope
            BATCH_SIZE = 5000 # Can be adjusted

            for model in embedding_models:
                logger.info(f"\n\n🔨 Processing embedding model: {model.model_id} ({model.model_name})")
                model_id_str = str(model.model_id)
                model_config = model_configs_dict.get(model_id_str)
                if not model_config:
                    logger.error(f"Configuration not found for model ID {model_id_str} ({model.model_name}) in {config_path}. Skipping model.")
                    continue
                implementation_details = model_config.get('implementation', {})
                parameters = implementation_details.get('parameters', {})
                # Use workbench_ prefix for consistency with feature_computation
                # Clean model name for the collection name
                model_name_cleaned = model.model_name.replace(" ", "_").lower()
                collection_name = f"workbench_{ip_category}_{model_name_cleaned}"
                vector_size = parameters.get('vector_size') # Get vector_size from parameters within implementation

                if not vector_size:
                    logger.warning(f"Skipping embedding model {model.model_id} due to missing 'vector_size' in config parameters.")
                    continue

                # Prepare batch search requests
                query_requests = [] # Changed variable name for clarity

                for prod_id in product_image_ids:
                    query_requests.append(
                        qdrant_models.QueryRequest(
                            query=str(prod_id), # Use string representation of product ID for Query by ID
                            limit=TOP_N,
                            filter=qdrant_models.Filter(
                                must=[
                                    qdrant_models.FieldCondition(
                                        key="image_type",
                                        match=qdrant_models.MatchValue(value="ip")
                                    ) # Search only against points with image_type="ip"
                                ]
                            ),
                            params=qdrant_models.SearchParams(exact=False), # Use HNSW
                            score_threshold=None # Adjust if needed
                        )
                    )

                results_to_store = []
                try:
                    # Log against the number of IP IDs we are filtering by
                    logger.info(f"Performing batch query for {len(query_requests)} product image IDs against IP image (filter)...")
                    # --- Wrap Qdrant call ---
                    batch_results_response = execute_with_retry(
                        lambda: qdrant_client.query_batch_points(
                            collection_name=collection_name,
                            requests=query_requests
                        ),
                        QDRANT_RETRYABLE_EXCEPTIONS
                    )
                    logger.info("Batch search completed.")

                    # Process batch results and accumulate for batch insertion
                    for i, hits in enumerate(batch_results_response): # Batch_results is a list of X items, where X is the number of product pictures
                        prod_id = product_image_ids[i]
                        for hit in hits.points: # hits.points is a list of TOP_N results for 1 product picture
                            # Qdrant ID is string UUID, convert back to UUID for DB foreign key
                            try:
                                ip_id = uuid.UUID(hit.id)
                            except ValueError:
                                logger.warning(f"Could not convert Qdrant hit ID '{hit.id}' to UUID for model {model.model_id}. Skipping hit.")
                                continue

                            raw_score = hit.score # Qdrant Cosine similarity is 0-1, higher is better
                            
                            model_instance = get_model_instance(model.model_id, model_configs_dict.get(str(model.model_id)))
                            if model_instance and hasattr(model_instance, 'normalize_score'):
                                normalized_score = model_instance.normalize_score(raw_score)
                            else:
                                logger.warning(f"Could not get model instance or normalize_score for model {model.model_id}. Using raw score clamped to [0,1].")
                                normalized_score = max(0.0, min(1.0, float(raw_score))) # Fallback

                            results_to_store.append({
                                'product_image_id': prod_id,
                                'ip_image_id': ip_id,
                                'model_id': model.model_id,
                                'similarity_score': normalized_score,
                            })

                        # Check if batch size is reached after processing results for one product image
                        if len(results_to_store) >= BATCH_SIZE:
                            logger.info(f"Batch size {BATCH_SIZE} reached. Inserting batch for model {model.model_id}.")
                            bulk_insert_results(db.session, results_to_store) # Use db.session
                            results_to_store = [] # Clear the list after insertion
                            logger.info(f"Batch inserted and list cleared for model {model.model_id}.")

                    # Insert any remaining results after processing all batch responses for this model
                    if results_to_store:
                        logger.info(f"Inserting remaining {len(results_to_store)} results for model {model.model_id}.")
                        bulk_insert_results(db.session, results_to_store) # Use db.session
                        results_to_store = [] # Clear the list after final insertion
                        logger.info(f"Remaining results inserted and list cleared for model {model.model_id}.")


                except Exception as e: # Catch exceptions from execute_with_retry or other logic
                    logger.error(f"❌ Error during Qdrant batch search or result processing for model {model.model_id}: {e}", exc_info=True) # Use correct attribute
                    # Rollback handled by main task exception block
                    # db.session.rollback() # Rollback potential partial commits from bulk_insert if error occurs here
                    continue # Skip to next model

        # --- Descriptor Models Loop ---
        for model in descriptor_models:
            logger.info(f"\n\n🔨 Processing descriptor model: {model.model_id} ({model.model_name})")
            model_id_str = str(model.model_id)
            model_config = model_configs_dict.get(model_id_str)
            if not model_config:
                logger.error(f"Configuration not found for model ID {model_id_str} ({model.model_name}) in {config_path}. Skipping model.")
                continue
            implementation_details = model_config.get('implementation', {})
            parameters = implementation_details.get('parameters', {})
            # algorithm = parameters.get('algorithm', '').upper() # Algorithm isn't directly in parameters, infer from class/module if needed, or add to config
            # Infer based on class name from config
            algorithm = "SIFT" if "SiftModel" in implementation_details.get('class','') else "ORB"
            norm_type = cv2.NORM_L2 if algorithm == 'SIFT' else cv2.NORM_HAMMING # Default Hamming for others like ORB
            matcher = cv2.BFMatcher(norm_type)
            use_ratio_test = parameters.get('use_ratio_test', True) # Default to ratio test
            ratio_threshold = parameters.get('ratio_threshold', 0.75) # Lowe's ratio
            distance_threshold = parameters.get('distance_threshold', 50 if algorithm == 'SIFT' else None) # Example for SIFT

            # Retrieve features, passing model_type
            # get_features_from_db already uses retry internally
            product_features = get_features_from_db(db.session, model.model_id, model.model_type, product_image_ids) # Use db.session
            ip_features = get_features_from_db(db.session, model.model_id, model.model_type, ip_image_ids) # Use db.session
            logger.info(f"Loaded {len(product_features)} product features and {len(ip_features)} IP features for model {model.model_id}.") # Use correct attribute

            if not product_features or not ip_features:
                logger.warning(f"Missing product or IP features for descriptor model {model.model_id}. Skipping.") # Use correct attribute
                continue

            all_model_results = []
            # This nested loop can be very slow. Consider parallelization or optimization if needed.
            for prod_id, prod_desc in product_features.items():
                # Check for missing product features
                if prod_desc is None or not isinstance(prod_desc, np.ndarray) or prod_desc.size == 0:
                    logger.warning(f"Missing or invalid descriptor for product {prod_id}, model {model.model_id}. Skipping comparisons for this product.")
                    continue

                current_prod_scores = [] # Stores (normalized_score, raw_score, ip_id)
                for ip_id, ip_desc in ip_features.items():
                    # Check for missing IP features
                    if ip_desc is None or not isinstance(ip_desc, np.ndarray) or ip_desc.size == 0:
                        logger.warning(f"Missing or invalid descriptor for IP {ip_id}, model {model.model_id}. Skipping comparison with product {prod_id}.")
                        continue

                    try:
                        # Ensure descriptors are correct type based on norm
                        if norm_type == cv2.NORM_HAMMING:
                            if prod_desc.dtype != np.uint8: prod_desc = prod_desc.astype(np.uint8)
                            if ip_desc.dtype != np.uint8: ip_desc = ip_desc.astype(np.uint8)
                        elif norm_type == cv2.NORM_L2:
                            if prod_desc.dtype != np.float32: prod_desc = prod_desc.astype(np.float32)
                            if ip_desc.dtype != np.float32: ip_desc = ip_desc.astype(np.float32)

                        # Check for empty descriptors after type conversion (should be caught earlier, but safety check)
                        if prod_desc.shape[0] == 0 or ip_desc.shape[0] == 0:
                             logger.debug(f"Skipping pair ({prod_id}, {ip_id}) for model {model.model_id} due to empty descriptors after type conversion.") # Use correct attribute
                             continue

                        raw_score = 0
                        good_matches_count = 0
                        if use_ratio_test:
                            matches = matcher.knnMatch(prod_desc, ip_desc, k=2)
                            # Filter matches using the Lowe's ratio test, handle potential empty results from knnMatch
                            good_matches = []
                            for match_pair in matches:
                                if len(match_pair) == 2: # Ensure we have two neighbors
                                    m, n = match_pair
                                    if m.distance < ratio_threshold * n.distance:
                                        good_matches.append(m)
                            good_matches_count = len(good_matches)
                        elif distance_threshold is not None:
                            matches = matcher.match(prod_desc, ip_desc)
                            # Filter matches based on distance
                            good_matches = [m for m in matches if m.distance < distance_threshold]
                            good_matches_count = len(good_matches)
                        else:
                            # Fallback or simple match count if no specific filtering
                            matches = matcher.match(prod_desc, ip_desc)
                            good_matches_count = len(matches)

                        raw_score = float(good_matches_count)
                        
                        model_instance = get_model_instance(model.model_id, model_configs_dict.get(str(model.model_id)))
                        if model_instance and hasattr(model_instance, 'normalize_score'):
                            normalized_score = model_instance.normalize_score(raw_score)
                        else:
                            logger.warning(f"Could not get model instance or normalize_score for descriptor model {model.model_id}. Using placeholder normalization.")
                            # Fallback placeholder normalization
                            normalized_score = max(0.0, min(1.0, raw_score / 100.0))
                        current_prod_scores.append((normalized_score, raw_score, ip_id))

                    except cv2.error as e:
                        # Log OpenCV errors, potentially indicating incompatible descriptors or other issues
                        logger.warning(f"❌ OpenCV error matching prod {prod_id} vs ip {ip_id} for model {model.model_id} (Algorithm: {algorithm}, Norm: {norm_type}): {e}. Skipping pair.") # Use correct attribute
                    except Exception as e:
                         logger.error(f"❌ Unexpected error matching prod {prod_id} vs ip {ip_id} for model {model.model_id}: {e}", exc_info=True) # Use correct attribute


                # Sort by normalized score (descending) and keep top N
                current_prod_scores.sort(key=lambda x: x[0], reverse=True)
                top_n_for_prod = current_prod_scores[:TOP_N]

                # Prepare for bulk insert
                for norm_score, raw_score, ip_id in top_n_for_prod:
                    all_model_results.append({
                        'product_image_id': prod_id,
                        'ip_image_id': ip_id,
                        'model_id': model.model_id, # Use correct attribute
                        'similarity_score': norm_score, # Use correct column name
                        # 'raw_score': raw_score, # Not in DB model
                        # 'ip_category': ip_category # Not in DB model
                    })

            # Bulk insert results for this descriptor model
            # bulk_insert_results already uses retry internally
            bulk_insert_results(db.session, all_model_results) # Use db.session

        # --- Hash Models Loop ---
        for model in hash_models:
            logger.info(f"\n\n🔨 Processing hash model: {model.model_id} ({model.model_name})")
            model_id_str = str(model.model_id)
            model_config = model_configs_dict.get(model_id_str)
            if not model_config:
                logger.error(f"Configuration not found for model ID {model_id_str} ({model.model_name}) in {config_path}. Skipping model.")
                continue
            implementation_details = model_config.get('implementation', {})
            parameters = implementation_details.get('parameters', {})
            hash_size = parameters.get('hash_size')
            hash_length = hash_size * hash_size if hash_size else None # Calculate length

            if not hash_length:
                 logger.warning(f"Skipping hash model {model.model_id} due to missing 'hash_size' in implementation parameters.") # Use correct attribute
                 continue

            # Retrieve features (hashes), passing model_type
            # get_features_from_db already uses retry internally
            product_features = get_features_from_db(db.session, model.model_id, model.model_type, product_image_ids) # Use db.session
            ip_features = get_features_from_db(db.session, model.model_id, model.model_type, ip_image_ids) # Use db.session
            logger.info(f"Loaded {len(product_features)} product hashes and {len(ip_features)} IP hashes for model {model.model_id}.") # Use correct attribute


            if not product_features or not ip_features:
                logger.warning(f"Missing product or IP features for hash model {model.model_id}. Skipping.") # Use correct attribute
                continue

            all_model_results = []
            for prod_id, prod_hash in product_features.items():
                 # Check for missing product features
                 if prod_hash is None:
                     logger.warning(f"Missing hash for product {prod_id}, model {model.model_id}. Skipping comparisons for this product.")
                     continue
                 # Ensure hash is numpy array (or appropriate type for compute_hamming_distance)
                 # Assuming compute_hamming_distance handles the type from deserialize_feature
                 # try:
                 #     if not isinstance(prod_hash, np.ndarray): prod_hash = np.array(prod_hash)
                 # except Exception: logger.warning(f"Could not convert product hash {prod_id} for model {model.model_id}. Skipping."); continue

                 current_prod_scores = [] # Stores (normalized_score, raw_score, ip_id)
                 for ip_id, ip_hash in ip_features.items():
                     # Check for missing IP features
                     if ip_hash is None:
                         logger.warning(f"Missing hash for IP {ip_id}, model {model.model_id}. Skipping comparison with product {prod_id}.")
                         continue
                     # try:
                     #     if not isinstance(ip_hash, np.ndarray): ip_hash = np.array(ip_hash)
                     # except Exception: logger.warning(f"Could not convert IP hash {ip_id} for model {model.model_id}. Skipping pair ({prod_id}, {ip_id})."); continue

                     try:
                         # Compute Hamming distance (raw score)
                         # Ensure compute_hamming_distance handles the types correctly
                         distance = compute_hamming_distance(prod_hash, ip_hash)
                         raw_score = float(distance)

                         model_instance = get_model_instance(model.model_id, model_configs_dict.get(str(model.model_id)))
                         if model_instance and hasattr(model_instance, 'normalize_score'):
                             normalized_score = model_instance.normalize_score(raw_score)
                         else:
                             logger.warning(f"Could not get model instance or normalize_score for hash model {model.model_id}. Using manual normalization.")
                             # Fallback manual normalization
                             normalized_score = max(0.0, 1.0 - (raw_score / hash_length)) if hash_length and hash_length > 0 else 0.0
                         
                         current_prod_scores.append((normalized_score, raw_score, ip_id))
                     except ValueError as e:
                          logger.warning(f"❌ Error comparing hashes for prod {prod_id} vs ip {ip_id} (model {model.model_id}): {e}. Skipping pair.") # Use correct attribute
                     except Exception as e:
                          logger.error(f"❌ Unexpected error comparing hashes prod {prod_id} vs ip {ip_id} for model {model.model_id}: {e}", exc_info=True) # Use correct attribute


                 # Sort by normalized score (descending) and keep top N
                 current_prod_scores.sort(key=lambda x: x[0], reverse=True)
                 top_n_for_prod = current_prod_scores[:TOP_N]

                 # Prepare for bulk insert
                 for norm_score, raw_score, ip_id in top_n_for_prod:
                     all_model_results.append({
                         'product_image_id': prod_id,
                         'ip_image_id': ip_id,
                         'model_id': model.model_id, # Use correct attribute
                         'similarity_score': norm_score, # Use correct column name
                         # 'raw_score': raw_score, # Not in DB model
                         # 'ip_category': ip_category # Not in DB model
                     })
 
             # Bulk insert results for this hash model
            # bulk_insert_results already uses retry internally
            bulk_insert_results(db.session, all_model_results) # Use db.session


        # --- Ground Truth Score Persistence ---
        logger.info("Ensuring ground truth pairs are scored and persisted.")
        # Use correct class ModelTestsGroundTruth and filter based on product image IDs
        gt_stmt = select(ModelTestsGroundTruth).where(ModelTestsGroundTruth.product_image_id.in_(product_image_ids))
        # --- Wrap DB call ---
        ground_truths = execute_with_retry(
            lambda: db.session.execute(gt_stmt).scalars().all(),
            DB_RETRYABLE_EXCEPTIONS,
            session=db.session, # Pass the session object
            operation_name="fetch ground truths"
        )
        # Filter further to ensure IP image is also relevant (though less critical as comparison wouldn't have happened otherwise)
        gt_pairs_to_ensure = {(gt.product_image_id, gt.correct_ip_image_id) for gt in ground_truths
                              if gt.correct_ip_image_id in ip_image_ids} # Filter GT pairs to those relevant to current images
        logger.info(f"Found {len(gt_pairs_to_ensure)} relevant ground truth pairs for category {ip_category}.")

        if gt_pairs_to_ensure:
            for model in all_models: # Iterate through all models again
                logger.info(f"Checking ground truth persistence for model {model.model_id} ({model.model_name})") # Use correct attributes
                gt_results_to_upsert = []

                # Check which GT pairs are already in the results table for this model
                # Need tuple_ from sqlalchemy for composite IN clause
                existing_gt_stmt = select(ModelTestsComparisonResult.product_image_id, ModelTestsComparisonResult.ip_image_id).where( # Use correct class
                    ModelTestsComparisonResult.model_id == model.model_id, # Use correct attribute
                    # ComparisonResult.ip_category == ip_category, # Category not in this table
                    tuple_(ModelTestsComparisonResult.product_image_id, ModelTestsComparisonResult.ip_image_id).in_(gt_pairs_to_ensure)
                )
                # --- Wrap DB call ---
                existing_pairs = set(execute_with_retry(
                    lambda: db.session.execute(existing_gt_stmt).fetchall(),
                    DB_RETRYABLE_EXCEPTIONS,
                    session=db.session, # Pass the session object
                    operation_name="fetch existing GT comparison results"
                ))
                missing_gt_pairs = gt_pairs_to_ensure - existing_pairs
                logger.info(f"Model {model.model_id}: {len(missing_gt_pairs)} GT pairs missing from results, need to calculate/insert.") # Use correct attribute

                if not missing_gt_pairs:
                    continue # All GT pairs present for this model

                # --- Recalculate scores for missing GT pairs ---
                logger.warning(f"Recalculating scores for {len(missing_gt_pairs)} missing GT pairs for model {model.model_id}. This is inefficient!") # Use correct attribute

                missing_prod_ids = {p[0] for p in missing_gt_pairs} # These are UUIDs
                missing_ip_ids = {p[1] for p in missing_gt_pairs} # These are UUIDs

                # Fetch necessary features/vectors ONLY for missing pairs
                # **Optimization Note:** Caching features/vectors loaded earlier would significantly improve this.
                prod_data = {}
                ip_data = {}
                recalculation_possible = True
                try:
                    if model.model_type == 'embedding':
                        if qdrant_client:
                            model_id_str = str(model.model_id)
                            model_config = model_configs_dict.get(model_id_str)
                            if not model_config: recalculation_possible = False; logger.warning(f"Missing config for model {model_id_str}, cannot recalculate GT scores.")
                            else:
                                implementation_details = model_config.get('implementation', {})
                                parameters = implementation_details.get('parameters', {})
                                # Use workbench_ prefix and cleaned name for consistency
                                model_name_cleaned = model.model_name.replace(" ", "_").lower()
                                collection_name = f"workbench_{ip_category}_{model_name_cleaned}"
                                vector_size = parameters.get('vector_size')
                            if vector_size:
                                # Inefficiently retrieve individual points
                                points_to_fetch_ids = [str(pid) for pid in missing_prod_ids.union(missing_ip_ids)] # Qdrant uses string UUIDs
                                if points_to_fetch_ids: # Avoid empty retrieve list
                                    # --- Wrap Qdrant call ---
                                    retrieved_points = execute_with_retry(
                                        lambda: qdrant_client.retrieve(collection_name=collection_name, ids=points_to_fetch_ids, with_vectors=True),
                                        QDRANT_RETRYABLE_EXCEPTIONS
                                    )
                                    # Map back using string UUIDs
                                    retrieved_vectors = {p.id: p.vector for p in retrieved_points if p.vector}
                                    prod_data = {pid: retrieved_vectors.get(str(pid)) for pid in missing_prod_ids}
                                    ip_data = {ipid: retrieved_vectors.get(str(ipid)) for ipid in missing_ip_ids}
                                else:
                                    prod_data = {}
                                    ip_data = {}
                            else: recalculation_possible = False; logger.warning(f"Missing vector_size for model {model.model_id}, cannot recalculate GT scores.")
                        else: recalculation_possible = False; logger.warning(f"Qdrant client not available, cannot recalculate GT scores for embedding model {model.model_id}.")
                    # Correct indentation for elif/else
                    elif model.model_type in ['descriptor', 'hash']: # Compare string
                        # get_features_from_db already uses retry internally, pass model_type
                        prod_data = get_features_from_db(db.session, model.model_id, model.model_type, list(missing_prod_ids)) # Use db.session
                        ip_data = get_features_from_db(db.session, model.model_id, model.model_type, list(missing_ip_ids)) # Use db.session
                    else:
                        recalculation_possible = False # Unknown model type
                # Correct indentation for except
                except Exception as e: # Catch exceptions from execute_with_retry or other logic
                    logger.error(f"❌ Error fetching data for GT recalculation (model {model.model_id}): {e}", exc_info=True) # Use correct attribute
                    recalculation_possible = False

                # Correct indentation for the following block (outside try/except)
                if not recalculation_possible:
                    logger.warning(f"Skipping GT score recalculation for model {model.model_id} due to previous errors or missing info.") # Use correct attribute
                    continue # Skip to the next model in the outer loop

                # --- Perform Recalculation ---
                # Correct indentation for the loop (outside try/except)
                for prod_id, ip_id in missing_gt_pairs:
                    prod_feat = prod_data.get(prod_id)
                    ip_feat = ip_data.get(ip_id)

                    # Check for missing features during GT recalculation
                    if prod_feat is None:
                        logger.warning(f"Missing feature/vector for product {prod_id} during GT recalculation for model {model.model_id}. Cannot calculate score for pair ({prod_id}, {ip_id}).")
                        continue
                    if ip_feat is None:
                         logger.warning(f"Missing feature/vector for IP {ip_id} during GT recalculation for model {model.model_id}. Cannot calculate score for pair ({prod_id}, {ip_id}).")
                         continue

                    raw_score = None
                    normalized_score = None

                    try:
                        if model.model_type == 'embedding': # Compare string
                            # Cosine similarity calculation (assuming vectors are numpy arrays)
                            vec1 = np.array(prod_feat, dtype=np.float32)
                            vec2 = np.array(ip_feat, dtype=np.float32)
                            if vec1.shape == vec2.shape and np.linalg.norm(vec1) > 0 and np.linalg.norm(vec2) > 0:
                                raw_score = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
                                raw_score = float(np.clip(raw_score, 0.0, 1.0)) # Ensure score is in [0, 1]
                            else: logger.warning(f"Invalid vectors for GT pair ({prod_id}, {ip_id}), model {model.model_id}.")

                        elif model.model_type == 'descriptor':
                            # Reuse descriptor matching logic
                            model_id_str = str(model.model_id)
                            model_config = model_configs_dict.get(model_id_str)
                            if not model_config: recalculation_possible = False; logger.warning(f"Missing config for model {model_id_str}, cannot recalculate GT scores.")
                            else:
                                implementation_details = model_config.get('implementation', {})
                                parameters = implementation_details.get('parameters', {})
                                # algorithm = parameters.get('algorithm', '').upper() # Infer as before
                                algorithm = "SIFT" if "SiftModel" in implementation_details.get('class','') else "ORB"
                            norm_type = cv2.NORM_L2 if algorithm == 'SIFT' else cv2.NORM_HAMMING
                            matcher = cv2.BFMatcher(norm_type)
                            # Need to get these parameters correctly
                            use_ratio_test = parameters.get('use_ratio_test', True)
                            ratio_threshold = parameters.get('ratio_threshold', 0.75)
                            distance_threshold = parameters.get('distance_threshold', 50 if algorithm == 'SIFT' else None)

                            # Ensure types
                            if norm_type == cv2.NORM_HAMMING:
                                if prod_feat.dtype != np.uint8: prod_feat = prod_feat.astype(np.uint8)
                                if ip_feat.dtype != np.uint8: ip_feat = ip_feat.astype(np.uint8)
                            elif norm_type == cv2.NORM_L2:
                                if prod_feat.dtype != np.float32: prod_feat = prod_feat.astype(np.float32)
                                if ip_feat.dtype != np.float32: ip_feat = ip_feat.astype(np.float32)

                            if prod_feat.shape[0] > 0 and ip_feat.shape[0] > 0:
                                good_matches_count = 0
                                if use_ratio_test:
                                    matches = matcher.knnMatch(prod_feat, ip_feat, k=2)
                                    good_matches = []
                                    for match_pair in matches:
                                        if len(match_pair) == 2:
                                            m, n = match_pair
                                            if m.distance < ratio_threshold * n.distance: good_matches.append(m)
                                    good_matches_count = len(good_matches)
                                elif distance_threshold is not None:
                                    matches = matcher.match(prod_feat, ip_feat)
                                    good_matches = [m for m in matches if m.distance < distance_threshold]
                                    good_matches_count = len(good_matches)
                                else:
                                    matches = matcher.match(prod_feat, ip_feat)
                                    good_matches_count = len(matches)
                                raw_score = float(good_matches_count)
                            else: logger.warning(f"Empty descriptors for GT pair ({prod_id}, {ip_id}), model {model.model_id}.")


                        elif model.model_type == 'hash': # Compare string
                            # Reuse hash comparison logic
                            # Assuming prod_feat, ip_feat are appropriate types from get_features_from_db
                            distance = compute_hamming_distance(prod_feat, ip_feat)
                            raw_score = float(distance)

                        if raw_score is not None:
                            model_instance = get_model_instance(model.model_id, model_configs_dict.get(str(model.model_id)))
                            if model_instance and hasattr(model_instance, 'normalize_score'):
                                normalized_score = model_instance.normalize_score(raw_score)
                            else:
                                logger.warning(f"GT Recalc: Could not get model instance or normalize_score for model {model.model_id}. Using placeholder/manual normalization.")
                                # Fallback placeholder/manual normalization for GT recalculation
                                if model.model_type == 'hash':
                                    model_id_str_gt = str(model.model_id) # Use different var name to avoid conflict
                                    model_config_gt = model_configs_dict.get(model_id_str_gt)
                                    if not model_config_gt: hash_length_gt = None
                                    else:
                                        implementation_details_gt = model_config_gt.get('implementation', {})
                                        parameters_gt = implementation_details_gt.get('parameters', {})
                                        hash_size_gt = parameters_gt.get('hash_size')
                                        hash_length_gt = hash_size_gt * hash_size_gt if hash_size_gt else None
                                    normalized_score = max(0.0, 1.0 - (raw_score / hash_length_gt)) if hash_length_gt and hash_length_gt > 0 else 0.0
                                elif model.model_type == 'descriptor':
                                    normalized_score = max(0.0, min(1.0, raw_score / 100.0)) # Example
                                else: # embedding
                                    normalized_score = max(0.0, min(1.0, float(raw_score)))
                            
                            gt_results_to_upsert.append({
                                'product_image_id': prod_id,
                                'ip_image_id': ip_id,
                                'model_id': model.model_id, # Use correct attribute
                                'similarity_score': normalized_score, # Use correct column name
                                # 'raw_score': raw_score, # Not in DB model
                                # 'ip_category': ip_category # Not in DB model
                            })
                        else:
                             logger.warning(f"❌ Could not calculate score for GT pair ({prod_id}, {ip_id}), model {model.model_id}.")

                    except Exception as e:
                         logger.error(f"Error recalculating score for GT pair ({prod_id}, {ip_id}), model {model.model_id}: {e}", exc_info=True)


                # Bulk insert/upsert the missing GT scores for this model
                if gt_results_to_upsert:
                    logger.info(f"Inserting/upserting {len(gt_results_to_upsert)} missing GT scores for model {model.model_id}.") # Use correct attribute
                    # bulk_insert_results already uses retry internally
                    bulk_insert_results(db.session, gt_results_to_upsert) # Use db.session
                else:
                    logger.info(f"No missing GT scores to insert for model {model.model_id} after recalculation attempt.") # Use correct attribute


        # Commit all DB changes made during this task run
        # --- Wrap DB call ---
        execute_with_retry(
            lambda: db.session.commit(),
            DB_RETRYABLE_EXCEPTIONS,
            session=db.session, # Pass the session object
            operation_name="commit comparison task changes"
        )
        logger.info(f"Committed all DB changes for comparison task, category: {ip_category}")

        logger.info(f"\n✅ Successfully completed comparison task for IP category: {ip_category}\n\n")

        # Trigger the next task in the chain
        logger.info(f"Triggering combined_score_task for IP category: {ip_category}")
        # Check the environment variable to decide execution mode
        if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() == 'true':
            # Run the task synchronously in the current process
            # Optional: Log synchronous execution
            # from flask import current_app # Import if using logger
            # current_app.logger.info(f"Running task {combined_score_task.name} synchronously.")
            combined_score_task.run(ip_category=ip_category) # Run synchronously
            logger.info(f"Executed combined_score_task synchronously for '{ip_category}'.")
        else:
            # Queue the task asynchronously with Celery (original behavior)
            combined_score_task.delay(ip_category=ip_category)
            logger.info(f"Dispatched combined_score_task for '{ip_category}'.")

        # Return success message
        return f"✅ Comparison task completed successfully for {ip_category}"

    except Exception as e:
        logger.error(f"❌ Critical error during comparison task for IP category {ip_category}: {e}", exc_info=True)
        # --- Wrap DB call ---
        # Attempt rollback, but don't fail the whole task if rollback itself fails
        try:
            execute_with_retry(
                lambda: db.session.rollback(),
                DB_RETRYABLE_EXCEPTIONS,
                session=db.session, # Pass the session object
                operation_name="rollback comparison task changes"
            )
            logger.info(f"Rolled back DB session due to error in comparison task for {ip_category}.")
        except Exception as rollback_err:
            logger.error(f"Failed to rollback DB session for {ip_category} after error: {rollback_err}", exc_info=True)

        try:
            # Use Celery's retry mechanism with exponential backoff (e.g., 60s, 120s, 240s)
            countdown_seconds = 60 * (2 ** self.request.retries) # Exponential backoff
            logger.warning(f"Retrying comparison task for {ip_category} in {countdown_seconds} seconds due to error: {e}")
            # The `raise self.retry(...)` call will stop current execution and schedule a retry
            raise self.retry(exc=e, countdown=countdown_seconds, max_retries=3) # Limit retries
        except self.MaxRetriesExceededError:
            logger.critical(f"Max retries exceeded for comparison task on category {ip_category}. Task failed permanently after multiple attempts.")
            # Potentially send notification or log critical failure state
            # Return a failure message or raise a different exception if needed downstream
            return f"Comparison task failed permanently for {ip_category} after max retries."
    finally:
        # No manual db close needed when using db.session from Flask context
        pass # No specific cleanup needed here