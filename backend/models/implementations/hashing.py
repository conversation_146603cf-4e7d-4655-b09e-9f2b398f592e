import cv2
import numpy as np
import logging
from backend.models.base import ImageModelBase

logger = logging.getLogger(__name__)

class DHashModel(ImageModelBase):
    """
    Concrete implementation for the dHash (Difference Hash) model.
    """
    def __init__(self, model_id: str, config: dict):
        """
        Initializes the DHashModel.

        Args:
            model_id: The unique identifier for this model instance.
            config: Dictionary containing model configuration, expecting 'hash_size'.
        """
        super().__init__(config) # Correct: Pass only config to base
        self.model_id = model_id # Store model_id in subclass
        self.hash_size = config.get('hash_size', 8)
        if not isinstance(self.hash_size, int) or self.hash_size <= 0:
            logger.warning(f"Invalid hash_size {self.hash_size} for DHashModel {model_id}. Defaulting to 8.")
            self.hash_size = 8
        logger.info(f"Initializing DHashModel (ID: {self.model_id}) with hash_size={self.hash_size}")

    def load(self):
        """
        No specific model loading needed for dHash.
        """
        logger.info(f"DHashModel (ID: {self.model_id}) loaded (no specific resources required).")
        # No specific model object to load for dHash

    def unload(self):
        """
        No specific resources to unload for dHash.
        """
        logger.info(f"DHashModel (ID: {self.model_id}) unloaded (no specific resources to release).")
        # No specific model object to unload

    def get_model_type(self) -> str:
        """
        Returns the type of the model.
        """
        return 'hash'

    def preprocess(self, image_path: str) -> np.ndarray | None:
        """
        Loads an image, converts it to grayscale, and resizes it.

        Args:
            image_path: Path to the image file.

        Returns:
            Resized grayscale image as a NumPy array, or None if loading fails.
        """
        try:
            image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
            if image is None:
                logger.error(f"Failed to load image: {image_path}")
                return None
            # Resize to (hash_size + 1, hash_size)
            resized_image = cv2.resize(image, (self.hash_size + 1, self.hash_size), interpolation=cv2.INTER_AREA)
            return resized_image
        except Exception as e:
            logger.error(f"Error preprocessing image {image_path} for DHashModel (ID: {self.model_id}): {e}", exc_info=True)
            return None

    def compute_features(self, preprocessed_image: np.ndarray) -> str | None:
        """
        Computes the difference hash (dHash) for the preprocessed image.

        Args:
            preprocessed_image: Resized grayscale image as a NumPy array.

        Returns:
            The computed hash as a hexadecimal string, or None on error.
        """
        if preprocessed_image is None:
             logger.error(f"Cannot compute features on None input for DHashModel (ID: {self.model_id}).")
             return None

        try:
            # Calculate horizontal differences
            # Compare adjacent pixels (left > right)
            diff = preprocessed_image[:, 1:] > preprocessed_image[:, :-1]

            # Convert boolean differences to a single integer hash
            hash_value = sum([2 ** i for i, v in enumerate(diff.flatten()) if v])

            # Convert integer hash to a fixed-length hexadecimal string
            # The length depends on hash_size * hash_size bits
            hex_length = (self.hash_size * self.hash_size + 3) // 4 # Calculate needed hex chars
            hash_string = format(hash_value, f'0{hex_length}x')

            logger.debug(f"Computed dHash {hash_string} for model {self.model_id}.")
            return hash_string
        except Exception as e:
            logger.error(f"Error computing dHash for model {self.model_id}: {e}", exc_info=True)
            return None

    def normalize_score(self, raw_score: float) -> float:
        """
        Normalizes a raw comparison score (Hamming distance) to a similarity value (0.0 to 1.0).

        Args:
            raw_score: The raw Hamming distance between two hashes.

        Returns:
            Normalized similarity score (1.0 - normalized distance).
        """
        try:
            # raw_score is Hamming distance
            max_distance = self.hash_size * self.hash_size
            if max_distance == 0:
                return 1.0 # Avoid division by zero if hash_size is somehow 0
            normalized_distance = float(raw_score) / max_distance
            similarity = 1.0 - normalized_distance
            # Clamp the value between 0.0 and 1.0
            normalized_similarity = max(0.0, min(1.0, similarity))
            logger.debug(f"Normalizing raw score (Hamming distance) {raw_score} to similarity {normalized_similarity} for DHashModel (ID: {self.model_id})")
            return normalized_similarity
        except Exception as e:
            logger.error(f"Error normalizing score {raw_score} for DHashModel {self.model_id}: {e}", exc_info=True)
            return 0.0 # Return minimum similarity on error
