import json
import uuid
import os
from sqlalchemy.orm import Session
from sqlalchemy import update, select
from backend.database.models import ModelTestsModel # Use the correct model class name
import logging

# Define a consistent namespace for generating UUIDs from model_id strings
# Using DNS namespace is a common practice, but any valid UUID can be used.
MODEL_ID_NAMESPACE = uuid.NAMESPACE_DNS
CONFIG_FILE_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'models', 'config.json'))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def sync_models_from_config(db: Session):
    """
    Synchronizes the modeltests_models table with the models defined in models/config.json.

    - Reads models/config.json.
    - Generates a consistent UUID for each model based on its 'model_id' string.
    - Adds new models found in the config file to the database.
    - Updates existing models found in the config file and marks them as active.
    - Marks models present in the database but NOT in the config file as inactive.

    Args:
        db: The SQLAlchemy database session.
    """
    logger.info(f"Starting model synchronization from config file: {CONFIG_FILE_PATH}")
    try:
        with open(CONFIG_FILE_PATH, 'r') as f:
            config_models_data = json.load(f)
    except FileNotFoundError:
        logger.error(f"Model configuration file not found at {CONFIG_FILE_PATH}. Skipping synchronization.")
        return
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from {CONFIG_FILE_PATH}: {e}. Skipping synchronization.")
        return
    except Exception as e:
        logger.error(f"An unexpected error occurred while reading {CONFIG_FILE_PATH}: {e}. Skipping synchronization.")
        return

    config_model_uuids = set()

    try:
        for model_data in config_models_data:
            model_id_str = model_data.get('model_id')
            if not model_id_str:
                logger.warning(f"Skipping model entry due to missing 'model_id': {model_data}")
                continue

            # Generate a consistent UUID based on the model_id string
            generated_uuid = uuid.uuid5(MODEL_ID_NAMESPACE, model_id_str)
            config_model_uuids.add(generated_uuid)

            # Check if model exists
            existing_model = db.get(ModelTestsModel, generated_uuid) # Use db.get for primary key lookup

            applicable_categories = model_data.get('applicable_ip_category', [])
            # Ensure 'all' is handled correctly if needed, or store as is.
            # If your DB expects specific values, you might need conversion here.

            if existing_model:
                # Update existing model
                logger.info(f"Updating existing model: {model_data.get('model_name')} (UUID: {generated_uuid})")
                existing_model.model_name = model_data.get('model_name')
                existing_model.model_type = model_data.get('model_type')
                existing_model.applicable_ip_category = applicable_categories
                existing_model.description = model_data.get('description')
                existing_model.is_active = True
                # updated_at is likely handled by the DB model/trigger, otherwise set here
            else:
                # Create new model
                logger.info(f"Creating new model: {model_data.get('model_name')} (UUID: {generated_uuid})")
                new_model = ModelTestsModel(
                    model_id=generated_uuid,
                    model_name=model_data.get('model_name'),
                    model_type=model_data.get('model_type'),
                    applicable_ip_category=applicable_categories,
                    description=model_data.get('description'),
                    is_active=True
                )
                db.add(new_model)

        # Mark models not in the config file as inactive
        logger.info("Checking for models to deactivate...")
        stmt = select(ModelTestsModel).where(ModelTestsModel.model_id.notin_(config_model_uuids), ModelTestsModel.is_active == True)
        models_to_deactivate = db.scalars(stmt).all()

        if models_to_deactivate:
            for model in models_to_deactivate:
                logger.info(f"Deactivating model: {model.model_name} (UUID: {model.model_id})")
                model.is_active = False
        else:
            logger.info("No models found to deactivate.")

        db.commit()
        logger.info("Model synchronization completed successfully.")

    except Exception as e:
        logger.error(f"An error occurred during model synchronization: {e}")
        db.rollback() # Rollback changes on error
        raise # Re-raise the exception if needed for higher-level handling

# Example usage (for testing purposes, usually called during app startup)
if __name__ == '__main__':
    # This requires setting up a database session outside the Flask app context
    # For demonstration purposes only.
    print(f"Attempting to sync models using config: {CONFIG_FILE_PATH}")
    # from backend.database import SessionLocal # Assuming you have a SessionLocal factory
    # db = SessionLocal()
    # try:
    #     sync_models_from_config(db)
    # finally:
    #     db.close()
    pass