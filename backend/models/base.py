import abc
from typing import Any, Dict, List, Type

class ImageModelBase(abc.ABC):
    """
    Abstract base class for image models used in the ModelTests Workbench.

    Defines the common interface that all concrete model implementations must adhere to.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initializes the model with its configuration.

        Args:
            config: A dictionary containing model-specific configuration
                    (e.g., weights path, parameters).
        """
        self.config = config
        self._loaded = False

    @abc.abstractmethod
    def load(self) -> None:
        """
        Loads the model into memory (e.g., from a weights file).
        Sets the internal state to loaded.
        """
        pass

    @abc.abstractmethod
    def unload(self) -> None:
        """
        Unloads the model from memory to free up resources.
        Sets the internal state to unloaded.
        """
        pass

    @property
    def is_loaded(self) -> bool:
        """Returns True if the model is currently loaded, False otherwise."""
        return self._loaded

    @abc.abstractmethod
    def get_model_type(self) -> str:
        """
        Returns the type of the model (e.g., 'classification', 'feature_extraction').
        """
        pass

    @abc.abstractmethod
    def preprocess(self, image_path: str) -> Any:
        """
        Preprocesses an image from the given path for model input.

        Args:
            image_path: Path to the image file.

        Returns:
            The preprocessed image data in the format expected by compute_features.
        """
        pass

    @abc.abstractmethod
    def compute_features(self, preprocessed_image: Any) -> List[float]:
        """
        Computes features (e.g., embeddings, classification scores) for the preprocessed image.

        Args:
            preprocessed_image: The output from the preprocess method.

        Returns:
            A list of floating-point numbers representing the computed features or scores.
        """
        pass

    @abc.abstractmethod
    def normalize_score(self, raw_score: float) -> float:
        """
        Normalizes a raw comparison score (often distance-based) to a standard range,
        typically [0, 1], where 1 indicates a perfect match or highest similarity.

        Args:
            raw_score: The raw score obtained from comparing features.

        Returns:
            A normalized score.
        """
        pass

# Optional: Define a registry for concrete model implementations
# This could be used later to dynamically load models based on type from config
MODEL_REGISTRY: Dict[str, Type[ImageModelBase]] = {}

def register_model(model_type: str):
    """Decorator to register a concrete model implementation."""
    def decorator(cls: Type[ImageModelBase]):
        if not issubclass(cls, ImageModelBase):
            raise TypeError(f"Class {cls.__name__} is not a subclass of ImageModelBase")
        if model_type in MODEL_REGISTRY:
            # Consider warning or raising an error based on desired behavior
            print(f"Warning: Model type '{model_type}' is being redefined.")
        MODEL_REGISTRY[model_type] = cls
        return cls
    return decorator