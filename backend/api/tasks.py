# backend/api/tasks.py
import logging
import os # Added import
from flask import Blueprint, jsonify, current_app, request
from celery.result import AsyncResult
from backend.tasks.feature_computation import compute_features_task # Import the feature computation task
from backend.tasks.combined_scores import combined_score_task # Import the combined scores task

# Configure logging
logger = logging.getLogger(__name__)

# Define the blueprint
tasks_bp = Blueprint('tasks_bp', __name__)

@tasks_bp.route('/compute-features/<string:ip_category>', methods=['POST'])
def trigger_compute_features(ip_category):
    """
    Triggers the feature computation task for a given IP category.
    Returns the task ID.
    """
    # Basic validation for ip_category if needed (e.g., check against allowed values)
    allowed_categories = ['trademark', 'copyright', 'patent'] # Example list
    if ip_category not in allowed_categories:
        logger.warning(f"Invalid ip_category received: {ip_category}")
        return jsonify({"error": f"Invalid ip_category. Allowed categories: {allowed_categories}"}), 400

    logger.info(f"Received request to trigger feature computation for ip_category: {ip_category}")
    try:
        # Check the environment variable to decide execution mode
        if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() == 'true':
            # Run the task synchronously in the current process
            # Optional: Log synchronous execution
            # current_app.logger.info(f"Running task {compute_features_task.name} synchronously.")
            result = compute_features_task.run(ip_category=ip_category) # Run synchronously
            logger.info(f"Executed compute_features_task synchronously for '{ip_category}'.")
            # For synchronous execution, we might return the result directly or just a confirmation
            # Returning a task-like structure for consistency, but status is 'SUCCESS'
            return jsonify({"task_id": None, "status": "SUCCESS", "result": result}), 200 # OK, task completed synchronously
        else:
            # Queue the task asynchronously with Celery (original behavior)
            task = compute_features_task.delay(ip_category=ip_category)
            logger.info(f"Dispatched compute_features_task for '{ip_category}'. Task ID: {task.id}")
            return jsonify({"task_id": task.id}), 202 # 202 Accepted
    except Exception as e:
        logger.error(f"Error dispatching compute_features_task for '{ip_category}': {e}", exc_info=True)
        return jsonify({"error": "Failed to dispatch task"}), 500

@tasks_bp.route('/compute-combined-scores/<string:ip_category>', methods=['POST'])
def trigger_compute_combined_scores(ip_category):
    """
    Triggers the combined score computation task for a given IP category.
    Returns the task ID.
    """
    allowed_categories = ['trademark', 'copyright', 'patent']
    if ip_category not in allowed_categories:
        logger.warning(f"Invalid ip_category received for combined scores: {ip_category}")
        return jsonify({"error": f"Invalid ip_category. Allowed categories: {allowed_categories}"}), 400

    logger.info(f"Received request to trigger combined score computation for ip_category: {ip_category}")
    try:
        if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() == 'true':
            result = combined_score_task.run(ip_category=ip_category)
            logger.info(f"Executed combined_score_task synchronously for '{ip_category}'.")
            return jsonify({"task_id": None, "status": "SUCCESS", "result": result}), 200
        else:
            task = combined_score_task.delay(ip_category=ip_category)
            logger.info(f"Dispatched combined_score_task for '{ip_category}'. Task ID: {task.id}")
            return jsonify({"task_id": task.id}), 202
    except Exception as e:
        logger.error(f"Error dispatching combined_score_task for '{ip_category}': {e}", exc_info=True)
        return jsonify({"error": "Failed to dispatch task"}), 500


@tasks_bp.route('/status/<string:task_id>', methods=['GET'])
def get_task_status(task_id):
    """
    Retrieves the status and result of a Celery task by its ID.
    """
    logger.debug(f"Received request to check status for task ID: {task_id}")
    try:
        # Use the Celery app instance registered with Flask if available
        # celery_app = current_app.extensions.get('celery')
        # if not celery_app:
        #     logger.error("Celery app not found in Flask extensions.")
        #     return jsonify({"error": "Celery integration not found"}), 500
        # task_result = AsyncResult(task_id, app=celery_app)

        # Alternatively, create AsyncResult directly (requires backend to be configured)
        task_result = AsyncResult(task_id)

        response = {
            "task_id": task_id,
            "status": task_result.status,
            "result": None,
            "error": None
        }

        if task_result.successful():
            response["result"] = task_result.get()
        elif task_result.failed():
            # Access traceback or error information if stored
            # result = task_result.get(propagate=False) # Get result without raising exception
            try:
                # Attempt to get the result which might contain the error details
                # This depends on how errors are handled and returned by the task
                result = task_result.result
                if isinstance(result, Exception):
                     response["error"] = str(result)
                else:
                     response["error"] = "Task failed, check logs for details." # Generic error
                     # You might need to inspect task_result.traceback as well
            except Exception as e:
                 logger.error(f"Error retrieving failure details for task {task_id}: {e}", exc_info=True)
                 response["error"] = "Task failed, error retrieving details."

        elif task_result.status == 'PENDING':
            response["result"] = "Task is waiting to be executed or could not be found."
        elif task_result.status == 'STARTED':
            response["result"] = "Task has started processing."
        elif task_result.status == 'PROGRESS':
             # Access meta-information if provided by the task
             response["result"] = task_result.info # .info contains the meta dict
        else:
            # Handle other states like RETRY, REVOKED if necessary
            response["result"] = f"Task is in state: {task_result.status}"


        logger.debug(f"Task {task_id} status: {task_result.status}")
        return jsonify(response), 200

    except Exception as e:
        logger.error(f"Error retrieving status for task ID '{task_id}': {e}", exc_info=True)
        # Be cautious about revealing internal error details
        return jsonify({"error": "Failed to retrieve task status"}), 500