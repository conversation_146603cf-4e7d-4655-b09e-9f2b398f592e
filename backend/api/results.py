# backend/api/results.py
import uuid
from flask import Blueprint, request, jsonify, current_app
from backend.extensions import db
from backend.database.models import (
    ModelTestsImage,
    ModelTestsGroundTruth,
    ModelTestsComparisonResult, # Correct singular form
    ModelTestsModel, # Needed for checking model validity/activity later? Maybe not for this specific endpoint.
    ModelTestsCombinedScoresConfig # Needed for checking config validity/activity
)
from sqlalchemy import desc, func, case
from sqlalchemy.orm import aliased, joinedload # Add joinedload for potential optimization

results_bp = Blueprint('results', __name__, url_prefix='/api/results')

# --- Helper Function (Optional but Recommended) ---
def get_image_details(image_id):
    """Fetches basic details for an image."""
    image = db.session.get(ModelTestsImage, image_id)
    if not image:
        return None
    return {
        "id": str(image.image_id),
        "filename": image.original_filename,
        "relative_path": image.relative_path,
        "ip_category": image.ip_category,
        "ip_owner": image.ip_owner,
        "image_type": image.image_type
    }

# --- Results Endpoints ---

@results_bp.route('/by-model', methods=['GET'])
def get_results_by_model():
    """
    Fetches comparison results grouped by product image for a specific model/config.
    Requires query parameters: model_id (UUID), ip_category (string).
    Optional query parameters: page (int), per_page (int), limit (int for top N results per product).
    """
    # --- Parameter Extraction and Validation ---
    model_id_str = request.args.get('model_id')
    ip_category = request.args.get('ip_category')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int) # Default to 10 products per page
    # Limit for comparison results per product image
    limit = request.args.get('limit', 3, type=int) # Default to Top 3 suggestions

    if not model_id_str:
        return jsonify({"error": "Missing required query parameter: model_id"}), 400
    if not ip_category:
        return jsonify({"error": "Missing required query parameter: ip_category"}), 400

    valid_ip_categories = ['trademark', 'copyright', 'patent']
    if ip_category not in valid_ip_categories:
        return jsonify({"error": f"Invalid ip_category. Must be one of: {valid_ip_categories}"}), 400

    try:
        model_id = uuid.UUID(model_id_str)
    except ValueError:
        return jsonify({"error": "Invalid UUID format for model_id"}), 400

    if page <= 0: page = 1
    if per_page <= 0: per_page = 10
    if limit <= 0: limit = 3

    # --- Query Logic ---
    try:
        # 1. Fetch Paginated Product Images for the category
        #    Note: Product images have ip_category=NULL in the DB,
        #    but they are associated with a category contextually (e.g., via ground truth or upload context).
        #    We need to find products that have ground truth entries linking them to IP images of the *target* ip_category.
        #    Alternatively, if the UI knows the category context for products, we could filter differently,
        #    but linking via GT seems more robust based on the data model.
        #    Let's refine this: The request *provides* the ip_category context. We should fetch product images
        #    that are *intended* for comparison within this category. How is this stored?
        #    Assumption: We filter ModelTestsImage for image_type='product' and assume the UI sends the correct
        #    ip_category context based on the view the user is on (TrademarkPage, CopyrightPage etc.).
        #    This means we don't filter products based on their *own* ip_category (which is NULL).

        product_image_query = ModelTestsImage.query.filter(
            ModelTestsImage.image_type == 'product',
            ModelTestsImage.ip_category == ip_category # Filter by the explicit category stored on the product
        ).order_by(ModelTestsImage.created_at.desc()) # Or filename, etc.

        pagination = product_image_query.paginate(page=page, per_page=per_page, error_out=False)
        product_images = pagination.items

        results_list = []

        # Get IDs for efficient subqueries
        product_image_ids = [p.image_id for p in product_images]

        if not product_image_ids:
            # Return empty results if no products found for this page
             return jsonify({
                "results": [],
                "pagination": {
                    "total_items": pagination.total,
                    "total_pages": pagination.pages,
                    "current_page": pagination.page,
                    "per_page": per_page,
                    "next_page": pagination.next_num if pagination.has_next else None,
                    "prev_page": pagination.prev_num if pagination.has_prev else None
                }
            }), 200

        # 2. Fetch Ground Truth for these Product Images in bulk
        #    Only fetch GT where the IP image matches the requested ip_category
        ground_truth_matches = db.session.query(
                ModelTestsGroundTruth.product_image_id,
                ModelTestsGroundTruth.correct_ip_image_id,
                ModelTestsImage # Alias for the IP image details
            ).join(
                ModelTestsImage, ModelTestsGroundTruth.correct_ip_image_id == ModelTestsImage.image_id
            ).filter(
                ModelTestsGroundTruth.product_image_id.in_(product_image_ids),
                ModelTestsImage.ip_category == ip_category # Ensure GT IP image matches category
            ).all()

        # Organize GT by product_image_id
        ground_truth_map = {}
        for gt in ground_truth_matches:
            prod_id = str(gt.product_image_id)
            if prod_id not in ground_truth_map:
                ground_truth_map[prod_id] = []
            ground_truth_map[prod_id].append({
                "id": str(gt.correct_ip_image_id),
                "filename": gt.ModelTestsImage.original_filename,
                "relative_path": gt.ModelTestsImage.relative_path,
                "ip_owner": gt.ModelTestsImage.ip_owner
            })

        # Get all unique correct IP image IDs for efficient checking later
        correct_ip_ids_map = {str(prod_id): {ip['id'] for ip in ips}
                              for prod_id, ips in ground_truth_map.items()}


        # 3. Fetch Top N Comparison Results for these Product Images and the specific Model/Config
        #    Use a window function or subquery to get top N per product image efficiently.

        # Alias for IP image details in the comparison results join
        IpImage = aliased(ModelTestsImage)

        # Subquery to rank results per product image
        subquery = db.session.query(
            ModelTestsComparisonResult.product_image_id, # Correct class
            ModelTestsComparisonResult.ip_image_id, # Correct class
            ModelTestsComparisonResult.similarity_score, # Correct class
            IpImage.original_filename.label('ip_filename'),
            IpImage.relative_path.label('ip_relative_path'),
            IpImage.ip_owner.label('ip_owner'),
            func.row_number().over(
                partition_by=ModelTestsComparisonResult.product_image_id, # Correct class
                order_by=desc(ModelTestsComparisonResult.similarity_score) # Correct class
            ).label('rank')
        ).join(
            IpImage, ModelTestsComparisonResult.ip_image_id == IpImage.image_id # Correct class
        ).filter(
            ModelTestsComparisonResult.product_image_id.in_(product_image_ids), # Correct class
            ModelTestsComparisonResult.model_id == model_id, # Correct class
            IpImage.ip_category == ip_category # Ensure suggested IP image matches category
        ).subquery()

        # Main query to select ranked results within the limit
        top_results_query = db.session.query(subquery).filter(subquery.c.rank <= limit)
        top_results = top_results_query.all()

        # Organize results by product_image_id
        comparison_results_map = {}
        for res in top_results:
            prod_id = str(res.product_image_id)
            if prod_id not in comparison_results_map:
                comparison_results_map[prod_id] = []

            ip_id_str = str(res.ip_image_id)
            is_gt = ip_id_str in correct_ip_ids_map.get(prod_id, set())

            comparison_results_map[prod_id].append({
                "ip_image_id": ip_id_str,
                "ip_filename": res.ip_filename,
                "ip_relative_path": res.ip_relative_path,
                "ip_owner": res.ip_owner,
                "similarity_score": res.similarity_score,
                "is_ground_truth": is_gt
            })

        # 4. Structure the final response
        for prod_img in product_images:
            prod_id_str = str(prod_img.image_id)
            results_list.append({
                "product_image": {
                    "id": prod_id_str,
                    "filename": prod_img.original_filename,
                    "relative_path": prod_img.relative_path
                },
                "ground_truth_ips": ground_truth_map.get(prod_id_str, []),
                "model_suggestions": sorted(
                    comparison_results_map.get(prod_id_str, []),
                    key=lambda x: x['similarity_score'],
                    reverse=True # Ensure final sort order just in case
                )
            })

        return jsonify({
            "results": results_list,
            "pagination": {
                "total_items": pagination.total,
                "total_pages": pagination.pages,
                "current_page": pagination.page,
                "per_page": per_page,
                "next_page": pagination.next_num if pagination.has_next else None,
                "prev_page": pagination.prev_num if pagination.has_prev else None
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching results by model: {e}", exc_info=True)
        return jsonify({"error": f"An unexpected error occurred: {e}"}), 500


@results_bp.route('/by-product/<uuid:product_image_id>', methods=['GET'])
def get_results_by_product(product_image_id):
    """
    Fetches comparison results for a specific product image across all applicable models/configs.
    Optional query parameters: limit (int for top N results per model/config).
    """
    # --- Parameter Extraction and Validation ---
    limit = request.args.get('limit', 3, type=int) # Default to Top 3 suggestions per model
    if limit <= 0: limit = 3

    # --- Query Logic ---
    try:
        # 1. Fetch Product Image Details
        product_image = db.session.get(ModelTestsImage, product_image_id)
        if not product_image or product_image.image_type != 'product':
            return jsonify({"error": "Product image not found or invalid type"}), 404

        product_details = {
            "id": str(product_image.image_id),
            "filename": product_image.original_filename,
            "relative_path": product_image.relative_path
        }

        # Get the product's explicit IP category
        product_ip_category = product_image.ip_category
        if not product_ip_category:
            current_app.logger.error(f"Product image {product_image_id} is missing its ip_category.")
            return jsonify({"error": f"Product image {product_image_id} is missing required IP category information."}), 400

        # Add ip_category to product details response
        product_details["ip_category"] = product_ip_category

        # 2. Fetch Ground Truth for this Product Image
        #    Use joinedload to potentially fetch IP image details efficiently if needed often
        ground_truth_query = db.session.query(
                ModelTestsGroundTruth.correct_ip_image_id,
                ModelTestsImage # Alias for the IP image details
            ).join(
                ModelTestsImage, ModelTestsGroundTruth.correct_ip_image_id == ModelTestsImage.image_id
            ).filter(
                ModelTestsGroundTruth.product_image_id == product_image_id
            # ).options(joinedload(ModelTestsGroundTruth.correct_ip_image)) # Alternative way
            )

        ground_truth_matches = ground_truth_query.all()

        ground_truth_list = []
        correct_ip_ids = set()
        # Process Ground Truth matches (no inference needed)
        for gt in ground_truth_matches:
            ip_image_details = gt.ModelTestsImage
            gt_data = {
                "id": str(gt.correct_ip_image_id),
                "filename": ip_image_details.original_filename,
                "relative_path": ip_image_details.relative_path,
                "ip_owner": ip_image_details.ip_owner,
                "ip_category": ip_image_details.ip_category # Keep for consistency in GT object
            }
            ground_truth_list.append(gt_data)
            correct_ip_ids.add(str(gt.correct_ip_image_id))

        # 3. Find all *active* models and *active* combined score configs for the product's category
        #    Assuming 'is_active' boolean field exists on both models.
        active_models = db.session.query(ModelTestsModel).filter(
            ModelTestsModel.is_active == True,
            ModelTestsModel.applicable_ip_category.contains([product_ip_category]) # Use product's category
            # Or adapt filter if supported_ip_categories is stored differently
        ).all()

        active_configs = db.session.query(ModelTestsCombinedScoresConfig).filter(
            ModelTestsCombinedScoresConfig.is_active == True,
            ModelTestsCombinedScoresConfig.ip_category == product_ip_category # Use product's category
        ).all()

        applicable_models_and_configs = []
        for model in active_models:
            applicable_models_and_configs.append({
                "id": str(model.model_id),
                "name": model.model_name, # Assuming a name field exists
                "type": "model"
            })
        for config in active_configs:
             # Use config_id as the 'model_id' in comparison results table for combined scores
            applicable_models_and_configs.append({
                "id": str(config.config_id),
                "name": config.config_name, # Assuming a name field exists
                "type": "combined_score"
            })

        # 4. For each applicable model/config, fetch Top N results
        results_by_model = {}

        # Alias for IP image details in the comparison results join
        IpImage = aliased(ModelTestsImage)

        for item in applicable_models_and_configs:
            item_id = uuid.UUID(item['id'])
            item_name = item['name']

            # Subquery to rank results for this specific model/config
            subquery = db.session.query(
                ModelTestsComparisonResult.ip_image_id, # Correct class
                ModelTestsComparisonResult.similarity_score, # Correct class
                IpImage.original_filename.label('ip_filename'),
                IpImage.relative_path.label('ip_relative_path'),
                IpImage.ip_owner.label('ip_owner'),
                func.row_number().over(
                    # Partitioning by product_image_id is implicit as we query for only one
                    order_by=desc(ModelTestsComparisonResult.similarity_score) # Correct class
                ).label('rank')
            ).join(
                IpImage, ModelTestsComparisonResult.ip_image_id == IpImage.image_id # Correct class
            ).filter(
                ModelTestsComparisonResult.product_image_id == product_image_id, # Correct class
                ModelTestsComparisonResult.model_id == item_id, # Use item_id (model_id or config_id) # Correct class
                IpImage.ip_category == product_ip_category # Ensure suggested IP image matches product's category
            ).subquery()

            # Query to select ranked results within the limit
            top_results_query = db.session.query(subquery).filter(subquery.c.rank <= limit)
            top_results = top_results_query.all()

            suggestions = []
            for res in top_results:
                ip_id_str = str(res.ip_image_id)
                is_gt = ip_id_str in correct_ip_ids

                suggestions.append({
                    "ip_image_id": ip_id_str,
                    "ip_filename": res.ip_filename,
                    "ip_relative_path": res.ip_relative_path,
                    "ip_owner": res.ip_owner,
                    "similarity_score": res.similarity_score,
                    "is_ground_truth": is_gt
                })

            # Use item name as the key in the results dictionary
            results_by_model[item_name] = sorted(
                suggestions,
                key=lambda x: x['similarity_score'],
                reverse=True
            )


        # 5. Structure the final response
        return jsonify({
            "product_image": product_details,
            "ground_truth_ips": ground_truth_list,
            "results_by_model": results_by_model # Dict mapping name to suggestions
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching results by product {product_image_id}: {e}", exc_info=True)
        return jsonify({"error": f"An unexpected error occurred: {e}"}), 500