import uuid
import os
from datetime import datetime
# Add send_from_directory
from flask import Blueprint, request, jsonify, current_app, send_from_directory
from backend.extensions import db
from backend.database.models import ModelTestsImage, ModelTestsGroundTruth, ModelTestsModel # Import necessary models
# Import necessary utils, including delete_image_file and get_master_folder
from backend.utils.file_utils import save_uploaded_file, get_image_folder_path, delete_image_file, get_master_folder
from backend.utils.vector_store import get_qdrant_client # For Qdrant client
from qdrant_client import models as qdrant_models # For Qdrant operations
from sqlalchemy.exc import IntegrityError
from sqlalchemy import or_, func, select # Added for filtering and select
from pathlib import Path # Add pathlib for easier path manipulation

data_management_bp = Blueprint('data_management', __name__)

# --- Image Management ---

@data_management_bp.route('/images', methods=['GET'])
def list_images():
    """
    Lists images with filtering and pagination.
    Query Parameters:
        - ip_category (string): Filter by 'trademark', 'copyright', or 'patent'.
                                Filters the actual ip_category stored (NULL for products).
        - image_type (string): Filter by 'product' or 'ip'.
        - missing_ip_owner (boolean string e.g., 'true'): If 'true', filters for IP images
                                                          where ip_owner is NULL.
        - page (int): Page number for pagination (default: 1).
        - per_page (int): Items per page (default: 20).
        - sort_by (string): Column to sort by (e.g., 'original_filename', 'created_at').
        - sort_order (string): 'asc' or 'desc' (default: 'desc').
    """
    # Get query parameters
    ip_category = request.args.get('ip_category', type=str)
    image_type = request.args.get('image_type', type=str)
    missing_ip_owner_str = request.args.get('missing_ip_owner', 'false', type=str).lower()
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    original_filename_filter = request.args.get('original_filename', None, type=str)
    sort_by_param = request.args.get('sort_by', type=str)
    sort_order_param = request.args.get('sort_order', type=str, default='desc').lower()

    current_app.logger.info(
        f"list_images called with params: image_type='{image_type}', "
        f"ip_category='{ip_category}', page={page}, per_page={per_page}, "
        f"missing_ip_owner='{missing_ip_owner_str}', "
        f"original_filename='{original_filename_filter}', "
        f"sort_by='{sort_by_param}', sort_order='{sort_order_param}'"
    )

    # Validate parameters
    valid_ip_categories = ['trademark', 'copyright', 'patent']
    valid_image_types = ['product', 'ip']
    if ip_category and ip_category not in valid_ip_categories:
        return jsonify({"error": f"Invalid ip_category. Must be one of: {valid_ip_categories}"}), 400
    if image_type and image_type not in valid_image_types:
        return jsonify({"error": f"Invalid image_type. Must be one of: {valid_image_types}"}), 400
    if missing_ip_owner_str not in ['true', 'false']:
         return jsonify({"error": "Invalid missing_ip_owner value. Must be 'true' or 'false'."}), 400
    if sort_order_param not in ['asc', 'desc']:
        return jsonify({"error": "Invalid sort_order value. Must be 'asc' or 'desc'."}), 400

    missing_ip_owner = missing_ip_owner_str == 'true'

    # Define sortable columns to prevent arbitrary column sorting
    sortable_columns = {
        "original_filename": ModelTestsImage.original_filename,
        "image_type": ModelTestsImage.image_type,
        "ip_category": ModelTestsImage.ip_category,
        "ip_owner": ModelTestsImage.ip_owner,
        "created_at": ModelTestsImage.created_at,
        "file_last_modified": ModelTestsImage.file_last_modified, # Added as per schema
        # Add other columns if they become sortable in the future
    }

    # Build query
    query = ModelTestsImage.query

    if ip_category:
        # Note: This filters the actual DB value. Products have NULL ip_category.
        query = query.filter(ModelTestsImage.ip_category == ip_category)

    if image_type:
        query = query.filter(ModelTestsImage.image_type == image_type)

    if missing_ip_owner:
        # Filter for IP images specifically where ip_owner is NULL
        query = query.filter(
            ModelTestsImage.image_type == 'ip',
            ModelTestsImage.ip_owner == None # SQLAlchemy translates this to IS NULL
            # or use: ModelTestsImage.ip_owner.is_(None)
        )

    if original_filename_filter and original_filename_filter.strip():
        search_term = f"%{original_filename_filter.strip()}%"
        query = query.filter(ModelTestsImage.original_filename.ilike(search_term))

    # Apply dynamic sorting
    if sort_by_param and sort_by_param in sortable_columns:
        column_to_sort = sortable_columns[sort_by_param]
        if sort_order_param == "asc":
            query = query.order_by(column_to_sort.asc())
        else: # desc
            query = query.order_by(column_to_sort.desc())
    else:
        # Default sort if sort_by_param is not provided or invalid
        query = query.order_by(ModelTestsImage.created_at.desc())

    # Log count before pagination
    try:
        count_before_pagination = query.count()
        current_app.logger.info(f"Query found {count_before_pagination} images before pagination.")
    except Exception as e:
        current_app.logger.error(f"Error counting results before pagination: {e}", exc_info=True)
        # Decide if this is a fatal error or if we can proceed
        # For now, let's proceed but this might indicate an issue with the query

    # Paginate results
    try:
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    except Exception as e:
        current_app.logger.error(f"Error during pagination: {e}", exc_info=True)
        return jsonify({"error": "Failed to retrieve images due to pagination error."}), 500

    images = pagination.items
    total_items = pagination.total
    total_pages = pagination.pages
    current_page = pagination.page
    next_page = pagination.next_num if pagination.has_next else None
    prev_page = pagination.prev_num if pagination.has_prev else None

    # Format response
    image_list = [
        {
            "image_id": str(img.image_id), # Changed from img.id to img.image_id to match schema
            "original_filename": img.original_filename,
            "relative_path": img.relative_path,
            "image_type": img.image_type,
            "ip_category": img.ip_category, # Will be None for products
            "ip_owner": img.ip_owner,
            "created_at": img.created_at.isoformat() if img.created_at else None,
            "updated_at": img.updated_at.isoformat() if img.updated_at else None,
            "file_last_modified": img.file_last_modified.isoformat() if img.file_last_modified else None,
        } for img in images
    ]

    current_app.logger.info(f"Returning {len(image_list)} images in image_list.")
    if image_list:
        current_app.logger.info(f"Sample of image_list (first 3): {[{'id': img['image_id'], 'name': img['original_filename'], 'cat': img['ip_category']} for img in image_list[:3]]}")
    else:
        current_app.logger.info("image_list is empty.")

    return jsonify({
        "images": image_list,
        "pagination": {
            "total_items": total_items,
            "total_pages": total_pages,
            "current_page": current_page,
            "per_page": per_page,
            "next_page": next_page,
            "prev_page": prev_page
        }
    }), 200


@data_management_bp.route('/images', methods=['POST'])
def upload_images():
    """
    Handles image uploads based on FR2.1.2.
    Expects 'ip_category' and 'image_type' in form data.
    Expects one or more files in request.files using key 'files[]'.
    """
    if 'files[]' not in request.files:
        return jsonify({"error": "No file part in the request (expected key 'files[]')"}), 400

    files = request.files.getlist('files[]')
    ip_category_form = request.form.get('ip_category') # e.g., 'trademark'
    image_type_form = request.form.get('image_type') # 'product' or 'ip'
    ip_owner_form = request.form.get('ip_owner', None) # Optional for IP images

    if not image_type_form:
        return jsonify({"error": "Missing 'image_type' in form data"}), 400
    if image_type_form not in ['product', 'ip']:
         return jsonify({"error": "Invalid 'image_type'. Must be 'product' or 'ip'."}), 400

    # Determine the correct ip_category based on image_type
    db_ip_category = None
    target_ip_category_for_path = None # Category used for folder path

    if image_type_form == 'ip':
        if not ip_category_form:
            return jsonify({"error": "Missing 'ip_category' for image_type 'ip'"}), 400
        if ip_category_form not in ['trademark', 'copyright', 'patent']:
             return jsonify({"error": "Invalid 'ip_category'. Must be 'trademark', 'copyright', or 'patent'."}), 400
        db_ip_category = ip_category_form
        target_ip_category_for_path = ip_category_form
    elif image_type_form == 'product':
        # For products, ip_category in the DB should be NULL.
        # However, we still need an ip_category to determine the save *folder*.
        # This implies the UI must send the relevant category context even for products.
        if not ip_category_form:
             return jsonify({"error": "Missing 'ip_category' context for image_type 'product'"}), 400
        if ip_category_form not in ['trademark', 'copyright', 'patent']:
             return jsonify({"error": "Invalid 'ip_category' context for image_type 'product'."}), 400
        db_ip_category = ip_category_form # Store provided category for products too
        target_ip_category_for_path = ip_category_form # Used for directory structure

    if not files or all(f.filename == '' for f in files):
        return jsonify({"error": "No selected files"}), 400

    results = {"success": [], "errors": []}
    new_image_records = []

    for file in files:
        if file and file.filename:
            try:
                # Use target_ip_category_for_path for saving
                saved_path, original_filename = save_uploaded_file(file, target_ip_category_for_path, image_type_form)

                if saved_path:
                    # Get file modification time
                    file_mod_time_timestamp = os.path.getmtime(saved_path)
                    file_mod_time_dt = datetime.fromtimestamp(file_mod_time_timestamp)

                    # Calculate relative path for storage
                    relative_path = f"{target_ip_category_for_path}/{image_type_form}/{original_filename}"

                    # Create DB record
                    new_image = ModelTestsImage(
                        image_id=uuid.uuid4(),
                        original_filename=original_filename,
                        relative_path=relative_path, # Store the relative path
                        image_type=image_type_form,
                        ip_category=db_ip_category, # Use NULL for products
                        ip_owner=ip_owner_form if image_type_form == 'ip' else None, # Only for IP images
                        file_last_modified=file_mod_time_dt
                    )
                    new_image_records.append(new_image)
                    results["success"].append({
                        "filename": original_filename,
                        "image_id": str(new_image.image_id)
                    })
                else:
                    # save_uploaded_file returns None, error_message on failure
                    # The error message is already logged by save_uploaded_file
                    results["errors"].append({
                        "filename": file.filename,
                        "error": original_filename # original_filename holds the error message here
                    })

            except ValueError as ve: # Catch validation errors from get_image_folder_path
                 current_app.logger.error(f"Validation error during upload for {file.filename}: {ve}")
                 results["errors"].append({"filename": file.filename, "error": str(ve)})
            except Exception as e:
                current_app.logger.error(f"Unexpected error uploading file {file.filename}: {e}", exc_info=True)
                results["errors"].append({"filename": file.filename, "error": f"An unexpected error occurred: {e}"})

    if new_image_records:
        try:
            db.session.add_all(new_image_records)
            db.session.commit()
            current_app.logger.info(f"Successfully added {len(new_image_records)} image records to the database.")
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Database error committing new image records: {e}", exc_info=True)
            # Update results to reflect DB error - move successes to errors
            for success_item in results["success"]:
                 results["errors"].append({
                     "filename": success_item["filename"],
                     "error": f"File saved but database commit failed: {e}"
                 })
            results["success"] = []
            # Attempt to delete the files that were saved but not committed
            for record in new_image_records:
                 try:
                     # Reconstruct path to delete the orphaned file
                     path_to_delete = get_image_folder_path(target_ip_category_for_path, record.image_type) / record.original_filename
                     if path_to_delete.exists():
                         path_to_delete.unlink()
                         current_app.logger.warning(f"Deleted orphaned file due to DB commit failure: {path_to_delete}")
                 except Exception as delete_err:
                     current_app.logger.error(f"Failed to delete orphaned file {record.original_filename}: {delete_err}")


    status_code = 207 if results["errors"] else 201 # Multi-Status or Created
    return jsonify(results), status_code


# +++ NEW ENDPOINT +++
@data_management_bp.route('/images/file/<uuid:image_id>', methods=['GET'])
def get_image_file(image_id):
    """
    Serves the image file associated with the given image_id.
    """
    current_app.logger.debug(f"Request received for image file with ID: {image_id}")

    image = db.session.get(ModelTestsImage, image_id)

    if not image:
        current_app.logger.warning(f"Image record not found for ID: {image_id}")
        return jsonify({"error": "Image record not found"}), 404

    if not image.relative_path:
        current_app.logger.error(f"Image record {image_id} found but has no relative_path.")
        return jsonify({"error": "Image record is incomplete (missing path)"}), 500

    try:
        master_folder_path = get_master_folder()
        # Construct the full path using pathlib for robustness
        full_image_path = Path(master_folder_path) / image.relative_path

        # Ensure the path is absolute and resolves correctly
        full_image_path = full_image_path.resolve()

        # Security check: Ensure the resolved path is still within the master folder
        if not str(full_image_path).startswith(str(master_folder_path.resolve())):
             current_app.logger.error(f"Attempted path traversal detected for image {image_id}. Path: {full_image_path}")
             return jsonify({"error": "Invalid file path"}), 400

        directory = str(full_image_path.parent)
        filename = full_image_path.name

        current_app.logger.info(f"Attempting to serve image: directory='{directory}', filename='{filename}'")

        # Use send_from_directory for secure file serving
        return send_from_directory(directory, filename)

    except FileNotFoundError:
        current_app.logger.error(f"Image file not found on disk for ID {image_id} at path: {full_image_path}")
        return jsonify({"error": "Image file not found on server"}), 404
    except Exception as e:
        current_app.logger.error(f"Error serving image file {image_id}: {e}", exc_info=True)
        return jsonify({"error": f"An unexpected error occurred while serving the image: {e}"}), 500

# --- IP Owner Metadata Management ---

@data_management_bp.route('/images/<uuid:image_id>/ip_owner', methods=['PUT'])
def update_ip_owner(image_id):
    """
    Updates the ip_owner metadata for a specific IP image. FR2.1.3, FR2.1.7
    Expects JSON body: {"ip_owner": "New Owner Name"}
    """
    data = request.get_json()
    if not data or 'ip_owner' not in data:
        return jsonify({"error": "Missing 'ip_owner' in request body"}), 400

    new_ip_owner = data['ip_owner']
    if not isinstance(new_ip_owner, str) or not new_ip_owner.strip():
         # Allow empty string to clear owner? Requirement doesn't specify, assume non-empty for now.
         # Updated: Allow empty string to clear owner based on typical usage.
         new_ip_owner = None # Treat empty/whitespace as clearing the owner
    else:
        new_ip_owner = new_ip_owner.strip()


    image = db.session.get(ModelTestsImage, image_id)

    if not image:
        return jsonify({"error": "Image not found"}), 404

    if image.image_type != 'ip':
        return jsonify({"error": "Cannot set ip_owner for non-IP image"}), 400

    try:
        image.ip_owner = new_ip_owner # Assign stripped value or None
        image.updated_at = datetime.utcnow() # Manually update timestamp
        db.session.commit()
        current_app.logger.info(f"Updated ip_owner for image {image_id} to '{new_ip_owner}'")
        return jsonify({
            "message": "IP owner updated successfully",
            "image_id": str(image.image_id),
            "ip_owner": image.ip_owner
        }), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Database error updating ip_owner for image {image_id}: {e}", exc_info=True)
        return jsonify({"error": f"Database error updating IP owner: {e}"}), 500


# --- Image Deletion ---

@data_management_bp.route('/images/<uuid:image_id>', methods=['DELETE'])
def delete_image(image_id):
    """
    Deletes an image and all associated data based on FR2.1.4.
    - Deletes the physical image file.
    - Deletes the record from modeltests_images.
    - Cascades deletes to comparison_results, ground_truth, feature_storage, feature_status.
    - Includes placeholder for Qdrant feature deletion.
    """
    image = db.session.get(ModelTestsImage, image_id)

    if not image:
        return jsonify({"error": "Image not found"}), 404

    # Construct the full path to the physical file
    try:
        master_folder = get_master_folder()
        full_path = master_folder / image.relative_path
    except Exception as e:
         current_app.logger.error(f"Error constructing path for image {image_id} ({image.relative_path}): {e}")
         # Proceed with DB deletion even if path construction fails? Or return error?
         # Let's return an error for safety, as we can't confirm file deletion.
         return jsonify({"error": f"Could not determine file path: {e}"}), 500

    try:
        # 1. Delete the database record. Cascades should handle related tables.
        db.session.delete(image)
        current_app.logger.info(f"Marked image record {image_id} for deletion.")

        # 2. Implement Qdrant feature deletion
        qdrant_client = get_qdrant_client()
        if qdrant_client and image.ip_category: # Only proceed if client and category are available
            current_app.logger.info(f"Attempting Qdrant feature deletion for image {image_id} in category {image.ip_category}")
            
            # Find applicable models for this image's IP category
            stmt_models = select(ModelTestsModel).where(
                ModelTestsModel.is_active == True,
                ModelTestsModel.model_type == 'embedding', # Only embedding models store features in Qdrant currently
                or_(
                    ModelTestsModel.applicable_ip_category.any(image.ip_category),
                    ModelTestsModel.applicable_ip_category.any('all')
                )
            )
            applicable_models = db.session.execute(stmt_models).scalars().all()
            
            deleted_from_collections = []
            failed_collections = []

            for model_db_instance in applicable_models:
                model_name_cleaned = model_db_instance.model_name.replace(" ", "_").lower()
                collection_name = f"workbench_{image.ip_category}_{model_name_cleaned}"
                
                try:
                    current_app.logger.info(f"Checking Qdrant collection: {collection_name} for image {image_id}")
                    # Check if collection exists before attempting deletion to avoid errors on non-existent collections
                    # However, qdrant_client.delete might be idempotent or handle non-existent collections/points gracefully.
                    # For simplicity, we'll try to delete directly. If collection_exists check is preferred, add it here.
                    
                    qdrant_client.delete_points(
                        collection_name=collection_name,
                        points_selector=qdrant_models.PointIdsList(points=[str(image.image_id)])
                    )
                    current_app.logger.info(f"Successfully issued delete command for point {image.image_id} from Qdrant collection {collection_name}.")
                    # Note: Qdrant delete is async by default unless wait=True.
                    # For critical ops, consider wait=True or checking operation status.
                    deleted_from_collections.append(collection_name)
                except Exception as q_err:
                    # Log specific Qdrant errors, e.g., collection not found, point not found (which is fine)
                    # For now, broadly catch and log.
                    current_app.logger.error(f"Error deleting point {image.image_id} from Qdrant collection {collection_name}: {q_err}", exc_info=True)
                    failed_collections.append({"collection": collection_name, "error": str(q_err)})
            
            if deleted_from_collections:
                current_app.logger.info(f"Qdrant points for image {image_id} targeted for deletion from collections: {deleted_from_collections}")
            if failed_collections:
                current_app.logger.warning(f"Qdrant point deletion for image {image_id} failed for collections: {failed_collections}")
        elif not qdrant_client:
            current_app.logger.warning("Qdrant client not available. Skipping Qdrant feature deletion.")
        elif not image.ip_category:
            current_app.logger.warning(f"Image {image_id} has no ip_category. Skipping Qdrant feature deletion as collection names cannot be determined.")


        # 3. Delete the physical file *after* DB operations are staged
        file_deleted = delete_image_file(str(full_path))
        if not file_deleted:
            # Log the error but proceed with DB commit as per FR2.1.4 emphasis on data removal
            current_app.logger.error(f"Failed to delete physical file {full_path} but proceeding with DB deletion for image {image_id}.")
            # Optionally, could choose to rollback here if file deletion is critical path
            # db.session.rollback()
            # return jsonify({"error": f"Failed to delete physical file {full_path}"}), 500

        # 4. Commit the transaction (DB delete + cascades)
        db.session.commit()
        current_app.logger.info(f"Successfully deleted image {image_id} and associated DB records.")

        return jsonify({"message": f"Image {image_id} and associated data deleted successfully."}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error during deletion process for image {image_id}: {e}", exc_info=True)
        return jsonify({"error": f"An error occurred during deletion: {e}"}), 500


# --- Ground Truth Management ---

@data_management_bp.route('/ground_truth', methods=['POST'])
def add_ground_truth():
    """
    Adds a ground truth pairing between a product image and an IP image. FR2.1.5 (Marking)
    Expects JSON body: {"product_image_id": "uuid", "correct_ip_image_id": "uuid"}
    """
    data = request.get_json()
    if not data or 'product_image_id' not in data or 'correct_ip_image_id' not in data:
        return jsonify({"error": "Missing 'product_image_id' or 'correct_ip_image_id' in request body"}), 400

    try:
        product_image_id = uuid.UUID(data['product_image_id'])
        correct_ip_image_id = uuid.UUID(data['correct_ip_image_id'])
    except ValueError:
        return jsonify({"error": "Invalid UUID format"}), 400

    # Validate images exist and have correct types
    product_image = db.session.get(ModelTestsImage, product_image_id)
    ip_image = db.session.get(ModelTestsImage, correct_ip_image_id)

    if not product_image or product_image.image_type != 'product':
        return jsonify({"error": f"Product image not found or invalid type: {product_image_id}"}), 404
    if not ip_image or ip_image.image_type != 'ip':
        return jsonify({"error": f"IP image not found or invalid type: {correct_ip_image_id}"}), 404

    # Create new ground truth record
    new_gt = ModelTestsGroundTruth(
        product_image_id=product_image_id,
        correct_ip_image_id=correct_ip_image_id
    )

    try:
        db.session.add(new_gt)
        db.session.commit()
        current_app.logger.info(f"Added ground truth pair: Product {product_image_id} -> IP {correct_ip_image_id}")

        # TODO FR2.1.6: Trigger background task to ensure score exists for this pair across all models.
        # This can be added later when Celery is integrated.
        # Example: ensure_gt_score_task.delay(str(product_image_id), str(correct_ip_image_id))
        current_app.logger.warning(f"Placeholder: Background task trigger for FR2.1.6 (ensure score exists) needed for GT pair {new_gt.ground_truth_id}.")


        return jsonify({
            "message": "Ground truth added successfully",
            "ground_truth_id": new_gt.ground_truth_id,
            "product_image_id": str(product_image_id),
            "correct_ip_image_id": str(correct_ip_image_id)
        }), 201
    except IntegrityError: # Handles unique constraint violation
        db.session.rollback()
        current_app.logger.warning(f"Attempted to add duplicate ground truth pair: Product {product_image_id} -> IP {correct_ip_image_id}")
        # Check if it already exists to return a more informative message or just 200 OK
        existing_gt = db.session.query(ModelTestsGroundTruth).filter_by(
            product_image_id=product_image_id,
            correct_ip_image_id=correct_ip_image_id
        ).first()
        if existing_gt:
             return jsonify({
                "message": "Ground truth pair already exists",
                "ground_truth_id": existing_gt.ground_truth_id,
             }), 200 # Or 409 Conflict? 200 seems reasonable for idempotent-like UI action.
        else:
             # Should not happen if IntegrityError was due to constraint, but handle defensively
             return jsonify({"error": "Database integrity error adding ground truth"}), 500
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error adding ground truth pair {product_image_id} -> {correct_ip_image_id}: {e}", exc_info=True)
        return jsonify({"error": f"Database error adding ground truth: {e}"}), 500


@data_management_bp.route('/ground_truth', methods=['DELETE'])
def remove_ground_truth():
    """
    Removes a ground truth pairing. FR2.1.5 (Unmarking)
    Expects JSON body: {"product_image_id": "uuid", "correct_ip_image_id": "uuid"}
    """
    data = request.get_json()
    if not data or 'product_image_id' not in data or 'correct_ip_image_id' not in data:
        return jsonify({"error": "Missing 'product_image_id' or 'correct_ip_image_id' in request body"}), 400

    try:
        product_image_id = uuid.UUID(data['product_image_id'])
        correct_ip_image_id = uuid.UUID(data['correct_ip_image_id'])
    except ValueError:
        return jsonify({"error": "Invalid UUID format"}), 400

    # Find the ground truth record
    gt_record = db.session.query(ModelTestsGroundTruth).filter_by(
        product_image_id=product_image_id,
        correct_ip_image_id=correct_ip_image_id
    ).first()

    if not gt_record:
        return jsonify({"error": "Ground truth pair not found"}), 404

    try:
        db.session.delete(gt_record)
        db.session.commit()
        current_app.logger.info(f"Removed ground truth pair: Product {product_image_id} -> IP {correct_ip_image_id}")
        return jsonify({"message": "Ground truth removed successfully"}), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error removing ground truth pair {product_image_id} -> {correct_ip_image_id}: {e}", exc_info=True)
        return jsonify({"error": f"Database error removing ground truth: {e}"}), 500