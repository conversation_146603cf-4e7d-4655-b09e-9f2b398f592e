# Backend Data Structures Documentation

This document outlines the data structures for the backend, covering database models and API endpoints, to ensure the frontend can accurately consume the provided data.

## 1. Database Models (`backend/database/models.py`)

### `ModelTestsImage`

Stores image metadata.

* **Columns:**
  * `image_id`: UUID (Primary Key, default: `uuid.uuid4()`)
  * `original_filename`: Text (Not Nullable)
  * `relative_path`: Text (Not Nullable, Unique) - e.g., 'trademark/product/image.jpg'
  * `image_type`: String(10) (Not Nullable) - Constrained to 'product' or 'ip'.
  * `ip_category`: String(50) (Nullable) - Constrained to 'trademark', 'copyright', 'patent'. Stored for product images based on upload context, NULL if not provided during upload for products historically.
  * `ip_owner`: String(255) (Nullable)
  * `created_at`: TIMESTAMP with Timezone (Server Default: current time)
  * `updated_at`: TIMESTAMP with Timezone (Server Default: current time, On Update: current time)
  * `file_last_modified`: TIMESTAMP with Timezone (Nullable)
* **Relationships:**
  * `comparison_results_as_product`: One-to-Many with `ModelTestsComparisonResult` (foreign key: `ModelTestsComparisonResult.product_image_id`). Cascades delete.
  * `comparison_results_as_ip`: One-to-Many with `ModelTestsComparisonResult` (foreign key: `ModelTestsComparisonResult.ip_image_id`). Cascades delete.
  * `ground_truth_as_product`: One-to-Many with `ModelTestsGroundTruth` (foreign key: `ModelTestsGroundTruth.product_image_id`). Cascades delete.
  * `ground_truth_as_ip`: One-to-Many with `ModelTestsGroundTruth` (foreign key: `ModelTestsGroundTruth.correct_ip_image_id`). Cascades delete.
  * `feature_storage`: One-to-Many with `ModelTestsFeatureStorage`. Cascades delete.
  * `feature_status`: One-to-Many with `ModelTestsFeatureStatus`. Cascades delete.

### `ModelTestsModel`

Stores metadata about registered models.

* **Columns:**
  * `model_id`: UUID (Primary Key) - From `config.json`.
  * `model_name`: String(255) (Unique, Not Nullable)
  * `model_type`: String(20) (Not Nullable) - Constrained to 'embedding', 'descriptor', 'hash'.
  * `applicable_ip_category`: ARRAY of Text (Not Nullable) - e.g., `['trademark', 'copyright']` or `['all']`. (Note: `results.py` refers to `supported_ip_categories` which might be an alias or a different interpretation of this field).
  * `description`: Text (Nullable)
  * `is_active`: Boolean (Default: `True`)
  * `created_at`: TIMESTAMP with Timezone (Server Default: current time)
  * `updated_at`: TIMESTAMP with Timezone (Server Default: current time, On Update: current time)
* **Relationships:**
  * `feature_storage`: One-to-Many with `ModelTestsFeatureStorage`. Cascades delete.
  * `feature_status`: One-to-Many with `ModelTestsFeatureStatus`. Cascades delete.

### `ModelTestsComparisonResult`

Stores computed similarity scores (Top N + Ground Truth).

* **Columns:**
  * `result_id`: BIGINT (Primary Key)
  * `product_image_id`: UUID (Not Nullable, Foreign Key to `ModelTestsImage.image_id`, ON DELETE CASCADE)
  * `ip_image_id`: UUID (Not Nullable, Foreign Key to `ModelTestsImage.image_id`, ON DELETE CASCADE)
  * `model_id`: UUID (Not Nullable) - Can be a Foreign Key to `ModelTestsModel.model_id` or `ModelTestsCombinedScoresConfig.config_id`.
  * `similarity_score`: Float (Not Nullable) - Constrained between 0 and 1.
  * `computed_at`: TIMESTAMP with Timezone (Server Default: current time)
* **Relationships:**
  * `product_image`: Many-to-One with `ModelTestsImage` (via `product_image_id`).
  * `ip_image`: Many-to-One with `ModelTestsImage` (via `ip_image_id`).

### `ModelTestsGroundTruth`

Stores user-defined ground truth pairings.

* **Columns:**
  * `ground_truth_id`: BIGINT (Primary Key)
  * `product_image_id`: UUID (Not Nullable, Foreign Key to `ModelTestsImage.image_id`, ON DELETE CASCADE)
  * `correct_ip_image_id`: UUID (Not Nullable, Foreign Key to `ModelTestsImage.image_id`, ON DELETE CASCADE)
  * `created_at`: TIMESTAMP with Timezone (Server Default: current time)
* **Relationships:**
  * `product_image`: Many-to-One with `ModelTestsImage` (via `product_image_id`).
  * `correct_ip_image`: Many-to-One with `ModelTestsImage` (via `correct_ip_image_id`).

### `ModelTestsCombinedScoresConfig`

Stores combined score configurations.

* **Columns:**
  * `config_id`: UUID (Primary Key, default: `uuid.uuid4()`)
  * `config_name`: String(255) (Unique, Not Nullable)
  * `ip_category`: String(50) (Not Nullable) - Constrained to 'trademark', 'copyright', 'patent'.
  * `model_weights`: JSONB (Not Nullable) - e.g., `{ "model_id_UUID_1": weight1, ... }`.
  * `is_active`: Boolean (Default: `True`)
  * `created_at`: TIMESTAMP with Timezone (Server Default: current time)
  * `updated_at`: TIMESTAMP with Timezone (Server Default: current time, On Update: current time)
* **Relationships:** None directly defined, but `ModelTestsComparisonResult.model_id` can refer to `config_id`.

### `ModelTestsFeatureStorage`

Stores computed features for DESCRIPTOR and HASH models.

* **Columns:**
  * `feature_id`: BIGINT (Primary Key)
  * `image_id`: UUID (Not Nullable, Foreign Key to `ModelTestsImage.image_id`, ON DELETE CASCADE)
  * `model_id`: UUID (Not Nullable, Foreign Key to `ModelTestsModel.model_id`, ON DELETE CASCADE)
  * `features`: BYTEA (Not Nullable) - Stores descriptors (binary blob) or hash (binary/hex string).
  * `computed_at`: TIMESTAMP with Timezone (Server Default: current time)
* **Relationships:**
  * `image`: Many-to-One with `ModelTestsImage`.
  * `model`: Many-to-One with `ModelTestsModel`.

### `ModelTestsFeatureStatus`

Tracks the status of feature computation per image/model pair.

* **Columns:**
  * `status_id`: BIGINT (Primary Key)
  * `image_id`: UUID (Not Nullable, Foreign Key to `ModelTestsImage.image_id`, ON DELETE CASCADE)
  * `model_id`: UUID (Not Nullable, Foreign Key to `ModelTestsModel.model_id`, ON DELETE CASCADE)
  * `last_computed_at`: TIMESTAMP with Timezone (Nullable) - Timestamp of last successful computation.
  * `status`: String(20) (Nullable, Default: 'pending') - e.g., 'pending', 'processing', 'completed', 'failed'.
  * `error_message`: Text (Nullable) - Stores error details if status is 'failed'.
* **Relationships:**
  * `image`: Many-to-One with `ModelTestsImage`.
  * `model`: Many-to-One with `ModelTestsModel`.

## 2. API Endpoints

### `backend/api/results.py`

#### `GET /api/results/by-model`

Fetches comparison results grouped by product image for a specific model or combined score configuration.

* **Query Parameters:**

  * `model_id` (string, UUID, required): The ID of the `ModelTestsModel` or `ModelTestsCombinedScoresConfig`.
  * `ip_category` (string, required): The IP category context (e.g., 'trademark', 'copyright', 'patent'). Product images are filtered by this category.
  * `page` (int, optional, default: 1): For pagination of product images.
  * `per_page` (int, optional, default: 10): Number of product images per page.
  * `limit` (int, optional, default: 3): Number of top model suggestions to return per product image.
* **Response Structure:**

  ```json
  {
    "results": [
      {
        "product_image": {
          "id": "string (UUID)",        // from ModelTestsImage.image_id
          "filename": "string",       // from ModelTestsImage.original_filename
          "relative_path": "string"   // from ModelTestsImage.relative_path
        },
        "ground_truth_ips": [
          {
            "id": "string (UUID)",    // from ModelTestsImage.image_id (via ModelTestsGroundTruth.correct_ip_image_id)
            "filename": "string",   // from ModelTestsImage.original_filename (via ModelTestsGroundTruth.correct_ip_image_id)
            "relative_path": "string",// from ModelTestsImage.relative_path (via ModelTestsGroundTruth.correct_ip_image_id)
            "ip_owner": "string | null" // from ModelTestsImage.ip_owner (via ModelTestsGroundTruth.correct_ip_image_id)
          }
        ],
        "model_suggestions": [
          {
            "ip_image_id": "string (UUID)", // from ModelTestsComparisonResult.ip_image_id
            "ip_filename": "string",       // from ModelTestsImage.original_filename (joined from ModelTestsComparisonResult.ip_image_id, aliased as 'ip_filename')
            "ip_relative_path": "string",  // from ModelTestsImage.relative_path (joined from ModelTestsComparisonResult.ip_image_id, aliased as 'ip_relative_path')
            "ip_owner": "string | null",   // from ModelTestsImage.ip_owner (joined from ModelTestsComparisonResult.ip_image_id, aliased as 'ip_owner')
            "similarity_score": "float",   // from ModelTestsComparisonResult.similarity_score
            "is_ground_truth": "boolean"  // Derived: true if ip_image_id is in the product's ground_truth_ips
          }
        ]
      }
    ],
    "pagination": {
      "total_items": "integer",    // Total number of product images matching filters
      "total_pages": "integer",    // Total number of pages
      "current_page": "integer",   // Current page number
      "per_page": "integer",       // Number of items per page
      "next_page": "integer | null", // Next page number or null
      "prev_page": "integer | null"  // Previous page number or null
    }
  }
  ```
* **Notes:**

  * Product images are filtered based on `ModelTestsImage.image_type == 'product'` and `ModelTestsImage.ip_category == <query_param_ip_category>`.
  * Ground truth IP images and suggested IP images are also filtered to match the provided `ip_category`.
  * `model_suggestions` are ranked by `similarity_score` in descending order.

#### `GET /api/results/by-product/<uuid:product_image_id>`

Fetches comparison results for a specific product image across all applicable (active) models and combined score configurations relevant to the product's IP category.

* **Path Parameters:**

  * `product_image_id` (UUID, required): The ID of the `ModelTestsImage` (must be `image_type == 'product'`).
* **Query Parameters:**

  * `limit` (int, optional, default: 3): Number of top suggestions to return per model/configuration.
* **Response Structure:**

  ```json
  {
    "product_image": {
      "id": "string (UUID)",        // from ModelTestsImage.image_id
      "filename": "string",       // from ModelTestsImage.original_filename
      "relative_path": "string",   // from ModelTestsImage.relative_path
      "ip_category": "string"     // from ModelTestsImage.ip_category
    },
    "ground_truth_ips": [
      {
        "id": "string (UUID)",    // from ModelTestsImage.image_id (via ModelTestsGroundTruth.correct_ip_image_id)
        "filename": "string",   // from ModelTestsImage.original_filename (via ModelTestsGroundTruth.correct_ip_image_id)
        "relative_path": "string",// from ModelTestsImage.relative_path (via ModelTestsGroundTruth.correct_ip_image_id)
        "ip_owner": "string | null", // from ModelTestsImage.ip_owner (via ModelTestsGroundTruth.correct_ip_image_id)
        "ip_category": "string" // from ModelTestsImage.ip_category (of the ground truth IP image)
      }
    ],
    "results_by_model": {
      // Key is the model_name (from ModelTestsModel.model_name) or config_name (from ModelTestsCombinedScoresConfig.config_name)
      "Model Name A / Config Name X": [
        {
          "ip_image_id": "string (UUID)", // from ModelTestsComparisonResult.ip_image_id
          "ip_filename": "string",       // from ModelTestsImage.original_filename (joined from ModelTestsComparisonResult.ip_image_id, aliased as 'ip_filename')
          "ip_relative_path": "string",  // from ModelTestsImage.relative_path (joined from ModelTestsComparisonResult.ip_image_id, aliased as 'ip_relative_path')
          "ip_owner": "string | null",   // from ModelTestsImage.ip_owner (joined from ModelTestsComparisonResult.ip_image_id, aliased as 'ip_owner')
          "similarity_score": "float",   // from ModelTestsComparisonResult.similarity_score
          "is_ground_truth": "boolean"  // Derived: true if ip_image_id is in the product's ground_truth_ips
        }
      ],
      "Model Name B / Config Name Y": [
        // ... similar structure ...
      ]
    }
  }
  ```

  **Notes:**
* * The endpoint first fetches the specified product image and its `ip_category`.
  * It then finds all `ModelTestsModel` records where `is_active == True` and `applicable_ip_category` (referred to as `supported_ip_categories` in the endpoint code) contains the product's `ip_category`.
  * It also finds all `ModelTestsCombinedScoresConfig` records where `is_active == True` and `ip_category` matches the product's `ip_category`.
  * For each of these active models/configs, it fetches the top `limit` comparison results.
  * Suggested IP images are filtered to match the product's `ip_category`.
  * Suggestions within each model/config list are sorted by `similarity_score` in descending order.

### `backend/api/data_management.py`

(Blueprint registered without a prefix, so routes are relative to `/`)

#### `GET /images`

Lists images with filtering and pagination.

* **Query Parameters:**

  * `ip_category` (string, optional): Filters by 'trademark', 'copyright', or 'patent'. This filters the `ModelTestsImage.ip_category` column directly.
  * `image_type` (string, optional): Filters by 'product' or 'ip'.
  * `missing_ip_owner` (string, optional, 'true' or 'false', default: 'false'): If 'true', filters for IP images (`image_type == 'ip'`) where `ip_owner` is NULL.
  * `page` (int, optional, default: 1): Page number for pagination.
  * `per_page` (int, optional, default: 20): Items per page.
* **Response Structure:**

  ```json
  {
    "images": [
      {
        "image_id": "string (UUID)",           // from ModelTestsImage.image_id
        "original_filename": "string",        // from ModelTestsImage.original_filename
        "relative_path": "string",            // from ModelTestsImage.relative_path
        "image_type": "string",               // from ModelTestsImage.image_type
        "ip_category": "string | null",       // from ModelTestsImage.ip_category
        "ip_owner": "string | null",          // from ModelTestsImage.ip_owner
        "created_at": "string (ISO 8601)",    // from ModelTestsImage.created_at
        "updated_at": "string (ISO 8601)",    // from ModelTestsImage.updated_at
        "file_last_modified": "string (ISO 8601) | null" // from ModelTestsImage.file_last_modified
      }
    ],
    "pagination": {
      "total_items": "integer",
      "total_pages": "integer",
      "current_page": "integer",
      "per_page": "integer",
      "next_page": "integer | null",
      "prev_page": "integer | null"
    }
  }
  ```

Handles image uploads.

* **Request Body:** `multipart/form-data`

  * `files[]` (File, required): One or more image files.
  * `image_type` (string, required): 'product' or 'ip'.
  * `ip_category` (string, required): 'trademark', 'copyright', or 'patent'. This is used for folder path and stored in `ModelTestsImage.ip_category`.
  * `ip_owner` (string, optional): IP owner name, applicable if `image_type` is 'ip'.
* **Response Structure (Success: 201 Created or 207 Multi-Status):**

  ```json
  {
    "success": [
      {
        "filename": "string", // Original filename
        "image_id": "string (UUID)" // ID of ModelTestsImage record
      }
    ],
    "errors": [
      {
        "filename": "string",
        "error": "string"
      }
    ]
  }
  ```

#### `GET /images/file/<uuid:image_id>`

Serves the image file.

* **Path Parameters:**
  * `image_id` (UUID, required): The ID of the `ModelTestsImage`.
* **Response:** Raw image file. On error, JSON: `{"error": "message"}`.

#### `PUT /images/<uuid:image_id>/ip_owner`

Updates `ip_owner` for an IP image.

* **Path Parameters:**

  * `image_id` (UUID, required): ID of `ModelTestsImage` (type 'ip').
* **Request Body (JSON):**

  ```json
  {
    "ip_owner": "string | null" // New owner. Null/empty clears owner.
  }
  ```
* **Response Structure (Success: 200 OK):**

  ```json
  {
    "message": "IP owner updated successfully",
    "image_id": "string (UUID)",
    "ip_owner": "string | null" // Updated value
  }
  ```

#### `DELETE /images/<uuid:image_id>`

Deletes an image and associated data.

* **Path Parameters:**

  * `image_id` (UUID, required): ID of `ModelTestsImage`.
* **Response Structure (Success: 200 OK):**

  ```json
  {
    "message": "Image <image_id> and associated data deleted successfully."
  }
  ```

#### `POST /ground_truth`

Adds a ground truth pairing.

* **Request Body (JSON):**

  ```json
  {
    "product_image_id": "string (UUID)",
    "correct_ip_image_id": "string (UUID)"
  }
  ```
* **Response Structure (Success: 201 Created or 200 OK if exists):**

  ```json
  {
    "message": "Ground truth added successfully" / "Ground truth pair already exists",
    "ground_truth_id": "integer",
    "product_image_id": "string (UUID)",
    "correct_ip_image_id": "string (UUID)"
  }
  ```

#### `DELETE /ground_truth`

Removes a ground truth pairing.

* **Request Body (JSON):**

  ```json
  {
    "product_image_id": "string (UUID)",
    "correct_ip_image_id": "string (UUID)"
  }
  ```
* **Response Structure (Success: 200 OK):**

  ```json
  {
    "message": "Ground truth removed successfully"
  }
  ```

### `backend/api/model_management.py`

(Blueprint `model_management_bp` registered without `url_prefix` in snippet, assuming `/api/models` for routes)

#### `GET /api/models/` (or `/` relative to blueprint prefix)

Lists all active models.

* **Query Parameters:** None.
* **Response Structure:**

  ```json
  [
    {
      "model_id": "string (UUID)",
      "model_name": "string",
      "model_type": "string", // 'embedding', 'descriptor', 'hash'
      "applicable_ip_category": ["string"], // e.g., ["trademark"] or ["all"]
      "description": "string | null",
      "is_active": "boolean", // true
      "created_at": "string (ISO 8601) | null",
      "updated_at": "string (ISO 8601) | null"
    }
  ]
  ```

#### `POST /api/models/refresh` (or `/refresh` relative to blueprint prefix)

Triggers synchronization of models from `models/config.json` to the database.

* **Request Body:** None.
* **Response Structure (Success: 200 OK):** `{"message": "Model list refreshed successfully from config."}`
* **Response Structure (Error):** `{"error": "Error message"}`

### `backend/api/combined_scores.py`

All routes are prefixed with `/api/combined-scores`.

#### `GET /api/combined-scores`

Lists combined score configurations.

* **Query Parameters:**

  * `ip_category` (string, optional): 'trademark', 'copyright', or 'patent'.
  * `is_active` (string, optional, 'true' or 'false').
* **Response Structure:**

  ```json
  [
    {
      "config_id": "string (UUID)",
      "config_name": "string",
      "ip_category": "string",
      "model_weights": { "model_uuid_1": "float (weight)" }, // from ModelTestsCombinedScoresConfig.model_weights
      "is_active": "boolean",
      "created_at": "string (ISO 8601) | null",
      "updated_at": "string (ISO 8601) | null"
    }
  ]
  ```

#### `POST /api/combined-scores`

Creates a new combined score configuration.

* **Request Body (JSON):**

  ```json
  {
    "config_name": "string",
    "ip_category": "string", // 'trademark', 'copyright', 'patent'
    "model_weights": { "model_uuid_1": "float (weight)" } // Keys: active ModelTestsModel UUIDs for category. Values: 0-1, sum to 1.0.
  }
  ```
* **Response Structure (Success: 201 Created):** (Mirrors the GET response for a single item)

  ```json
  {
    "config_id": "string (UUID)",
    "config_name": "string",
    "ip_category": "string",
    "model_weights": { "model_uuid_1": "float (weight)" },
    "is_active": "boolean", // true
    "created_at": "string (ISO 8601)",
    "updated_at": "string (ISO 8601)"
  }
  ```

#### `PUT /api/combined-scores/<uuid:config_id>`

Updates an existing combined score configuration. IP category cannot be changed.

* **Path Parameters:** `config_id` (UUID).
* **Request Body (JSON):** (Fields to update)

  ```json
  {
    "config_name": "string",
    "model_weights": { "model_uuid_1": "float (new_weight)" },
    "is_active": "boolean"
  }
  ```
* **Response Structure (Success: 200 OK):** (Mirrors the GET response for a single item, reflecting updates)

#### `DELETE /api/combined-scores/<uuid:config_id>`

Deletes a combined score configuration.

* **Path Parameters:** `config_id` (UUID).
* **Response Structure (Success: 200 OK):** `{"message": "Configuration deleted successfully."}`

### `backend/api/dashboard.py`

All routes are prefixed with `/api/dashboard`.

#### `GET /api/dashboard/performance-summary`

Retrieves performance summary for active models/configs per IP category.

* **Query Parameters:**

  * `ip_category` (string, required): 'patent', 'trademark', 'copyright'.
* **Response Structure:**

  ```json
  [
    {
      "model_id": "string (UUID)", // ModelTestsModel.model_id or ModelTestsCombinedScoresConfig.config_id
      "model_name": "string",   // ModelTestsModel.model_name or ModelTestsCombinedScoresConfig.config_name
      "precision_avg_rank": "float | null"
    }
  ]
  ```

#### `GET /api/dashboard/score-distribution`

Retrieves score distribution for a model/config and IP category.

* **Query Parameters:**

  * `model_id` (string, UUID, required): ID of `ModelTestsModel` or `ModelTestsCombinedScoresConfig`.
  * `ip_category` (string, required).
* **Response Structure:**

  ```json
  {
    "gt_scores": ["float"],    // Scores for ground truth pairs
    "non_gt_scores": ["float"] // Scores for non-ground truth pairs
  }
  ```

#### `GET /api/dashboard/confusion-matrix`

Calculates confusion matrix data.

* **Query Parameters:**

  * `model_id` (string, UUID, required).
  * `ip_category` (string, required).
  * `threshold` (float, required): Score threshold.
* **Response Structure:**

  ```json
  {
    "tp": "integer", "fn": "integer", "fp": "integer", "tn": "integer"
  }
  ```

### `backend/api/tasks.py`

(Blueprint `tasks_bp` registered without `url_prefix` in snippet, assuming `/api/tasks`)

#### `POST /api/tasks/compute-features/<string:ip_category>`

Triggers feature computation task.

* **Path Parameters:** `ip_category` (string, required).
* **Response (Async):** `{"task_id": "string"}` (202 Accepted)
* **Response (Sync, if `RUN_TASKS_IN_FLASK` is true):** `{"task_id": null, "status": "SUCCESS", "result": "any"}` (200 OK)

#### `GET /api/tasks/status/<string:task_id>`

Retrieves Celery task status.

* **Path Parameters:** `task_id` (string, required).
* **Response Structure:**

  ```json
  {
    "task_id": "string",
    "status": "string", // PENDING, SUCCESS, FAILURE, etc.
    "result": "any | null", // Task result if successful or progress info
    "error": "string | null" // Error message if failed
  }
  ```

### `backend/api/qdrant_management.py`

All routes are prefixed with `/api/qdrant`.

#### `GET /api/qdrant/collections`

Lists workbench-related Qdrant collections and their status.

* **Query Parameters:** None.
* **Response Structure:**

  ```json
  [
    {
      "name": "string",             // Qdrant collection name (alias or original)
      "ip_category": "string | null", // Derived IP category
      "model_name": "string | null",  // Derived model name
      "is_active": "boolean",       // True if corresponds to an active model
      "needs_migration": "boolean"  // True if old naming pattern
    }
  ]
  ```

#### `DELETE /api/qdrant/collections/<path:collection_name>`

Deletes a Qdrant collection (must match allowed patterns).

* **Path Parameters:** `collection_name` (string, required).
* **Response (Success: 200 OK):** `{"message": "Collection '<collection_name>' deleted successfully."}`
* **Response (Error):** JSON with `error` field (403, 404, 500, etc.).
