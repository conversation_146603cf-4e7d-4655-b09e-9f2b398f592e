Flask>=2.0
transformers==4.51.3
python-dotenv>=0.19
SQLAlchemy>=1.4
psycopg2-binary>=2.9
Flask-SQLAlchemy>=2.5
Werkzeug>=2.0
celery>=5.0
redis>=4.0
opencv-python-headless>=4.5 # For descriptor model feature matching (cv2)
numpy>=1.20 # For numerical operations and feature serialization
qdrant-client>=1.7 # For vector database interaction
tensorflow-cpu>=2.8 # For EfficientNet embedding model (CPU version)
tensorflow_hub>=0.12 # For loading models from TF Hub
Flask-CORS
sentence-transformers>=2.2 # For CLIP model implementation
timm>=0.9 # For Swin Transformer and other PyTorch models
torch-cpu>=2.0 # PyTorch (CPU version) for TimmModel
sentencepiece