import sys
import os
import logging

# Add the project root to the Python path to allow imports like 'backend.tasks...'
# Assumes run_task.py is in the 'backend' directory
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
sys.path.insert(0, os.getcwd())

from backend.utils.vector_store import get_qdrant_client

client = get_qdrant_client()
# Delete all aliases in Qdrant, and the collection related to the alias
aliases = client.get_aliases()
for al in aliases.aliases:
    print(f"alias={al.alias_name} - collection={al.collection_name}")
    #  client.delete_collection(collection_name=al.collection_name, timeout=60)
   
# Delete all collections in Qdrant 
collections = client.get_collections()
for col in collections.collections:
    print(col.name)
    # client.delete_collection(collection_name=col.name, timeout=60)