import os
import re
from dotenv import load_dotenv

# loaded = load_dotenv()
# print(".env loaded: ", loaded)

TEXT_MODEL_FREE="gemini-2.0-flash-exp"
TEXT_MODEL_FREE_VERTEX=True
IMAGE_MODEL_FREE="gemini-2.0-flash-exp"
IMAGE_MODEL_FREE_VERTEX=True
SMART_MODEL_FREE="gemini-2.0-flash-exp"   #"gemini-2.5-pro-exp-03-25"
SMART_MODEL_FREE_VERTEX=True
IMAGE_GEN_MODEL_FREE="gemini-2.0-flash-exp"
IMAGE_GEN_MODEL_FREE_VERTEX=True

TEXT_MODEL_FREE_LIMITED="gemini-2.5-flash-preview-05-20"
TEXT_MODEL_FREE_LIMITED_VERTEX=False
IMAGE_MODEL_FREE_LIMITED="gemini-2.5-flash-preview-05-20"
IMAGE_MODEL_FREE_LIMITED_VERTEX=False
SMART_MODEL_FREE_LIMITED="gemini-2.5-flash-preview-05-20"
SMART_MODEL_FREE_LIMITED_VERTEX=False

TEXT_MODEL="gemini-2.5-flash-preview-05-20"
IMAGE_MODEL="gemini-2.5-flash-preview-05-20"
SMART_MODEL="gemini-2.5-pro-preview-05-06" 
IMAGE_GEN_MODEL="gemini-2.0-flash-001"

# Function to sanitize folder names by replacing prohibited characters
def sanitize_name(name):
    # Define a pattern for invalid characters
    invalid_chars_pattern = r'[<>:"/\\|?*]'
    # Replace invalid characters with an underscore
    sanitized_name = re.sub(invalid_chars_pattern, '_', name)
    
    # Additional handling for special Unicode characters that may cause issues with cv2
    # This includes registered trademark (®), trademark (™), copyright (©), etc.
    special_chars_pattern = r'[®™©℠§¶†‡±¿¡]'
    sanitized_name = re.sub(special_chars_pattern, '', sanitized_name)
    
    # Replace any other non-ASCII characters
    sanitized_name = ''.join(c if c.isascii() and c.isprintable() else '' for c in sanitized_name)
    
    # Optionally, strip trailing spaces and periods
    sanitized_name = sanitized_name.rstrip(' .')
    return sanitized_name