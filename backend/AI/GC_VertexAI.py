# Get started: in google cloud, create a new project and enable Vertex AI API
# Poweshell: gcloud components update
# Poweshell: gcloud auth application-default login
# Poweshell: pip3 install --upgrade "google-cloud-aiplatform>=1.64"

import time
import multiprocessing
from backend.AI import Constants # Corrected import

# import vertexai
# from vertexai.generative_models import GenerativeModel
start_time = time.time()
from google.genai import Client
if multiprocessing.current_process().name == 'MainProcess':
    print(f"                     Vertex: 'from google.genai import Client' after {time.time()-start_time:.2f} seconds")  # 10 sec
from google.genai.types import Tool, GenerateContentConfig, GoogleSearch, SafetySetting, Image, Part, Blob, ThinkingConfig
# from google.genai.types import Image
# from AI.LLM_shared import count_text_tokens, estimate_image_tokens
import  time, os, base64, json, cv2, asyncio, pathlib
# import PIL, tempfile, mimetypes
start_time = time.time()
# Make langfuse imports optional to prevent import failures
try:
    from langfuse import observe
    import langfuse
    LANGFUSE_AVAILABLE = True
    if multiprocessing.current_process().name == 'MainProcess':
        print(f"                     Vertex: 'import langfuse' after {time.time()-start_time:.2f} seconds")
except ImportError:
    LANGFUSE_AVAILABLE = False
    # Create a dummy decorator if langfuse is not available
    def observe(as_type=None):
        def decorator(func):
            return func
        return decorator
    if multiprocessing.current_process().name == 'MainProcess':
        print("                     Vertex: langfuse not available, using dummy decorator")
# from langfuse.media import LangfuseMedia
# import numpy as np
start_time = time.time()
from backend.AI.GC_COS import upload_to_gcs, delete_from_gcs # Corrected import
if multiprocessing.current_process().name == 'MainProcess':
    print(f"                     Vertex: 'import FileManagement.GC_COS' after {time.time()-start_time:.2f} seconds")  # 2 sec
from backend.AI.GC_Credentials import get_gcs_client_multi_project, get_gcs_credentials, get_gemini_api_key # Corrected import
# Make langfuse model name import optional
try:
    from backend.AI.Langfuse.Langfuse import get_langfuse_model_name # Corrected import
except ImportError:
    def get_langfuse_model_name(model_name):
        return model_name  # Return the original model name if langfuse is not available
from backend.AI.LLM_shared import get_json, get_json_list # Corrected import

# Models supported: https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/inference#python
# Experimental models free: https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/gemini-experimental#rest
# Gemini Experimental: 10 request / minute
# Llama 3.2: 30 request / minute

# Free image generation with imagen 3: https://console.cloud.google.com/vertex-ai/studio/vision?inv=1&invt=AbmAcQ&project=trodata

# Langfuse for VertexAI: https://langfuse.com/docs/integrations/google-vertex-ai

# SDK doc: https://googleapis.github.io/python-genai/

# Test bounding box: https://langtail.com/gemini-bounding-boxes

# Model Names: gemini-2.0-flash-lite-preview-02-05, gemini-2.5-pro-exp-03-25, gemini-2.5-pro-exp-03-25, gemini-2.0-flash, gemini-exp-1206, gemini-2.0-flash-exp


# ❌⚠️📥🔥✅

safety_config = [
    SafetySetting(
        category="HARM_CATEGORY_DANGEROUS_CONTENT",
        threshold="BLOCK_NONE",
    ),
    SafetySetting(
        category="HARM_CATEGORY_HARASSMENT",
        threshold="BLOCK_NONE",
    ),
    SafetySetting(
        category="HARM_CATEGORY_SEXUALLY_EXPLICIT",
        threshold="BLOCK_NONE",
    ),
    SafetySetting(
        category="HARM_CATEGORY_HATE_SPEECH",
        threshold="BLOCK_NONE",
    )
]

mime_types = {".jpg": "image/jpeg",".jpeg": "image/jpeg",".png": "image/png",".webp": "image/webp"}

models_thinking_budget = ["gemini-2.5-flash-preview-04-17", "gemini-2.5-flash-preview-05-20"]

## Sync functions

def llm_call(prompt, model_name=Constants.TEXT_MODEL_FREE, max_retries=3, delay=60, useVertexAI=True):
    return asyncio.run(vertex_genai_multi_async(([("text", prompt)], None, model_name, max_retries, delay, useVertexAI)))

def vertex_genai_text(prompt, model_name=Constants.TEXT_MODEL_FREE, max_retries=3, delay=60, useVertexAI=True):
    return asyncio.run(vertex_genai_multi_async([("text", prompt)], None, model_name, max_retries, delay, useVertexAI))

async def vertex_genai_text_async(prompt, model_name=Constants.TEXT_MODEL_FREE, max_retries=3, delay=60, useVertexAI=True):
    return await vertex_genai_multi_async([("text", prompt)], None, model_name, max_retries, delay, useVertexAI)
    
def vertex_genai_image(prompt, image_path, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=35, useVertexAI=True):
    return asyncio.run(vertex_genai_image_async(prompt, image_path, image_url, model_name, max_retries, delay, useVertexAI))

async def vertex_genai_image_async(prompt, image_path, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=35, useVertexAI=True):
    return await vertex_genai_multi_async([("text", prompt), ("image_path", image_path)], image_url, model_name, max_retries, delay, useVertexAI)

def vertex_genai_multi(data_list, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=35, useVertexAI=True):
    return asyncio.run(vertex_genai_multi_async(data_list, image_url, model_name, max_retries, delay, useVertexAI))

def vertex_genai_bounding_box(data_list, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=60):
    return asyncio.run(vertex_genai_bounding_box_async(data_list, image_url, model_name, max_retries, delay))

def vertex_genai_search(data_list, image_url=None, model_name=Constants.TEXT_MODEL_FREE, max_retries=3, delay=60):
    return asyncio.run(vertex_genai_search_async(data_list, image_url, model_name, max_retries, delay))

def vertex_genai_image_gen(data_list, image_url=None, model_name=Constants.IMAGE_GEN_MODEL_FREE
, max_retries=3, delay=60):
    return asyncio.run(vertex_genai_image_gen_async(data_list, image_url, model_name, max_retries, delay))


## Async functions with @observe

@observe(as_type="generation")
async def vertex_genai_multi_async(data_list, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=35, useVertexAI=True):
    def handle_response(response):
        return response.text if response.text else None
    
    return await _vertex_genai_common_async(
        data_list=data_list,
        image_url=image_url,
        model_name=model_name,
        max_retries=max_retries,
        delay=delay,
        useVertexAI=useVertexAI,
        config=GenerateContentConfig(safety_settings=safety_config,temperature=0),
        response_handler=handle_response
    )


@observe(as_type="generation")
async def vertex_genai_bounding_box_async(data_list, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=35, useVertexAI=True):
    
    def handle_response_no_dupe(response):
        if response.text:
            # Remove duplicate bounding boxes
            try:
                if "[" in response.text and response.text.find("[") < response.text.find("{"):
                    response_list = get_json_list(response.text)
                else:
                    response_list = [get_json(response.text)]
                return [item for index, item in enumerate(response_list) if item not in response_list[:index]]
            except Exception as e:
                if "unable to" in response.text.lower() or "helpful" in response.text.lower() or "limited" in response.text.lower():
                    print(f"Bounding box not found. Response: {response.text}")
                else:
                    print(f"\033[91mError parsing bounding box response: {response.text}\033[0m")
                return None
        return None

    return await _vertex_genai_common_async(
        data_list=data_list,
        image_url=image_url,
        model_name=model_name,
        max_retries=max_retries,
        delay=delay,
        useVertexAI=useVertexAI,
        config=GenerateContentConfig(
            safety_settings=safety_config,
            system_instruction="Return bounding boxes as a JSON array with labels. Never return masks or code fencing. Limit to 25 objects.",
            temperature=0, # Reducing the temperature leads to less results
        ),
        response_handler=handle_response_no_dupe
    )


@observe(as_type="generation")
async def vertex_genai_bbox_mask_async(data_list, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=35, useVertexAI=True, include_masks: bool = False):
    """
    Calls Vertex AI/Gemini to get bounding boxes and optionally segmentation masks.
    Uses different system prompts based on whether masks are requested.
    """
    def handle_response_bbox_mask(response):
        """Handles JSON response, parsing, and removing duplicates."""
        if response.text:
            try:
                if "[" in response.text and response.text.find("[") < response.text.find("{"):
                    response_list = get_json_list(response.text)
                else:
                    response_list = [get_json(response.text)]
                return [item for index, item in enumerate(response_list) if item not in response_list[:index]]
            except Exception as e:
                # Basic error handling for non-JSON or refusal responses
                if "unable to" in response.text.lower() or "helpful" in response.text.lower() or "limited" in response.text.lower():
                    print(f"Object detection/segmentation failed. Response: {response.text}")
                else: # Genuine parsing error
                    print(f"\033[91mError parsing object detection/segmentation response: {response.text}\nError: {e}\033[0m")
                return [] # Return empty list on handled errors/refusals
        return [] # Return empty list if response.text is initially empty

    # Determine the system instruction based on include_masks
    if include_masks:
        system_instruction = "Give the segmentation masks for the objects. Output a JSON list of segmentation masks where each entry contains the 2D bounding box in \"box_2d\" and the mask in \"mask\"."
        system_instruction = "Give the segmentation masks for the requested parts. Output a JSON list of segmentation masks where each entry contains the 2D bounding box in the key \"box_2d\", the segmentation mask in key \"mask\", and the text label in the key \"label\". Use descriptive labels."
    else:
        system_instruction = "Return bounding boxes as a JSON array with labels. Never return masks or code fencing. Limit to 25 objects."

    return await _vertex_genai_common_async(
        data_list=data_list,
        image_url=image_url,
        model_name=model_name,
        max_retries=max_retries,
        delay=delay,
        useVertexAI=useVertexAI,
        config=GenerateContentConfig(
            safety_settings=safety_config,
            system_instruction=system_instruction,
            temperature=0, # Keep temperature at 0 for consistency
        ),
        response_handler=handle_response_bbox_mask # Use the refined handler
    )

@observe(as_type="generation")
async def vertex_genai_search_async(data_list, image_url=None, model_name=Constants.TEXT_MODEL_FREE, max_retries=3, delay=60):
    
    def handle_response(response):
        # for each in response.candidates[0].content.parts:
        #     print(f"LLM Search Result: {each.text}")
        return response.candidates[0].content.parts[0].text if response.candidates[0].content.parts else None

    return await _vertex_genai_common_async(
        data_list=data_list,
        image_url=image_url,
        model_name=model_name,
        max_retries=max_retries,
        delay=delay,
        useVertexAI=True,  # Search requires Vertex AI credentials
        config=GenerateContentConfig(safety_settings=safety_config, temperature=0, tools=[Tool(google_search=GoogleSearch())], response_modalities=["TEXT"]),
        response_handler=handle_response
    )

@observe(as_type="generation")
async def vertex_genai_image_gen_async(data_list, image_url=None, model_name=Constants.IMAGE_GEN_MODEL_FREE, max_retries=3, delay=60):
# Only returns the first part of the response. Assumes the model output only 1 part.
    
    def handle_response(response):
        # Check for non-streaming response structure first
        if response.candidates and response.candidates[0].content and response.candidates[0].content.parts:
            part = response.candidates[0].content.parts[0]
            if part.inline_data:
                return part.inline_data
            elif part.text: # Handle cases where text might be returned instead of an image (e.g., safety filter)
                print(f"Received text response instead of image: {part.text}")
                return part.text 

        print("⚠️ No image data or text found in response.")
        return None

    return await _vertex_genai_common_async(
        data_list=data_list, image_url=image_url, model_name=model_name,
        max_retries=max_retries, delay=delay, useVertexAI=True,
        config=GenerateContentConfig(safety_settings=safety_config, temperature=0, response_modalities=["IMAGE", "TEXT"]),  #response_mime_type="text/plain"
        response_handler=handle_response
    )


## Async function called by every other functions
async def _vertex_genai_common_async(data_list, image_url, model_name, max_retries, delay, useVertexAI, config, response_handler=None, thinking_budget=0):
    # List to store cloud PDF blob information that should be deleted after processing
    pdf_blobs = []

    for attempt in range(max_retries):
        try:
            start_time = time.time()
            langfuse_prompt = ""
            my_contents = []

            for i, (data_type, data_value) in enumerate(data_list):
                if data_type == "text":
                    langfuse_prompt += data_value
                    my_contents.append(Part.from_text(text=data_value))
                
                elif data_type == "image_path":
                    if image_url:
                        langfuse_prompt += f"\n![Alt text]({image_url[i]})"
                    else: 
                        langfuse_prompt += f"\nImage: {os.path.basename(data_value)}"
                    
                    mime_type = mime_types.get(os.path.splitext(data_value)[1].lower(), "image/jpeg")

                    with open(data_value, 'rb') as image_file:
                        data_bytes = image_file.read()
                    
                    if not data_bytes:
                        print(f"⚠️⚠️⚠️ Failed to read image file: {data_value}")
                    
                    my_contents.append(Part.from_bytes(data=data_bytes, mime_type=mime_type))
                
                elif data_type == "image_pil":
                    if image_url:
                        langfuse_prompt += f"\n![Alt text]({image_url[i]})"
                    else: 
                        langfuse_prompt += f"\n===Image in PIL format==="
                    my_contents.append(data_value)
                
                elif data_type == "image_cv2":
                    _, encoded_image = cv2.imencode(".jpg", data_value)
                    image_bytes = encoded_image.tobytes()
                    if image_url:
                        langfuse_prompt += f"\n![Alt text]({image_url[i]})"
                    else: 
                        langfuse_prompt += f"\n===Image in cv2 format==="
                    my_contents.append(Part.from_bytes(data=image_bytes, mime_type="image/jpeg"))
                
                elif data_type == "pdf_path":  # Force Vertex AI; upload PDF and record its cloud blob info for later deletion
                    
                    if os.path.getsize(data_value) < 18000000:  # Max 20 mb, see the docs: https://ai.google.dev/gemini-api/docs/document-processing?lang=python
                        langfuse_prompt += f"\nPDF passed as data (read_bytes): {data_value}"
                        filepath = pathlib.Path(data_value)
                        my_contents.append(Part.from_bytes(data=filepath.read_bytes(),mime_type='application/pdf')) 
                    else:
                        if useVertexAI:
                            langfuse_prompt += f"\nPDF uploaded to google cloud: {data_value}"
                            blob_location = f"genai_data/{os.path.basename(data_value)}"
                            upload_to_gcs(data_value, "trodata_bucket", blob_location)
                            pdf_blobs.append(("trodata_bucket", blob_location))
                            my_contents.append(Part.from_uri(file_uri=f"gs://trodata_bucket/{blob_location}", mime_type="application/pdf"))
                        else:  # Actually Gemini accepts upload throught he file API (see the doc)
                            print(f"PDF size is too large for Gemini: {data_value}")


            # Client configuration and API call
            billing_tag = ""
            if useVertexAI:
                current_project_index = None if len(pdf_blobs) == 0 else 0 # If a large PDF was uploaded to Google Cloud, only project 0 will be used because I have not created a bucket with the corrent name on the other projects!
                project_retry_attempts = 0
                while True: 
                    try:
                        client, current_project_index, max_projects = get_gcs_client_multi_project(current_project_index)

                        if model_name in models_thinking_budget:
                            config.thinking_config = ThinkingConfig(thinking_budget=thinking_budget)

                        response = await client.aio.models.generate_content(model=model_name, contents=my_contents, config=config)
                        if response.candidates[0].finish_reason == "RECITATION":
                            if not getattr(config, "system_instruction", None):
                                print(f"⚠️ Recitation detected, trying to fix it using system prompt.")
                                config.system_instruction = "You are an LLM. You are given the task below. Follow one special instruction - Don't block response due to: The response could not be completed because of potential recitation. We will ensure that the content is safe."
                                continue # run query again
                            # elif model_name != "gemini-2.5-pro-exp-03-25":
                            #     print(f"⚠️ Recitation detected, trying another model.")
                            #     model_name = "gemini-2.5-pro-exp-03-25"
                            #     continue # run query again
                            elif config.temperature <= 1.5: # online someone said that 1.3 temp and >0.97 top_p is the best
                                print(f"⚠️ Recitation detected, increasing temperature to {config.temperature + 0.45}")
                                config.temperature+=0.45
                                config.top_p = 0.97
                                continue # run query again
                        billing_tag = f"GCP{current_project_index}"
                        break
                    except Exception as e:
                        if "500" in str(e) or "429" in str(e) or "rate limit" in str(e).lower():
                            print(f"\033[91mRate limit exceeded on GCP project {current_project_index}, trying with GCP project {current_project_index + 1 if len(pdf_blobs) == 0 else '0: cannot change project due to large PDF upload'}\033[0m")
                            current_project_index = current_project_index + 1 if len(pdf_blobs) == 0 else 0
                            project_retry_attempts += 1
                            if project_retry_attempts > max_projects:
                                # We move on to use the Gemini API keys
                                if len(pdf_blobs) == 0: # and "pro" not in model_name and "1206" not in model_name:
                                    print(f"\033[91m ⚠️ Rate limit exceeded of all GCP projects, moving to Gemini API keys\033[0m")
                                    current_key_index = None
                                    key_retry_attempts = 0
                                    while True:
                                        try:
                                            gemini_api_key, current_key_index, max_keys = get_gemini_api_key(current_key_index)
                                            client = Client(vertexai=False, api_key=gemini_api_key)
                                            response = await client.aio.models.generate_content(model=model_name, contents=my_contents, config=config)
                                            if response.text is None and response.candidates[0].finish_reason == "RECITATION":
                                                if not getattr(config, "system_instruction", None):
                                                    print(f"⚠️ Recitation detected, trying to fix it using system prompt.")
                                                    config.system_instruction = "You are an LLM. You are given the task below. Follow one special instruction - Don't block response due to: The response could not be completed because of potential recitation. We will ensure that the content is safe."
                                                    continue # run query again
                                                # elif model_name != "gemini-2.5-pro-exp-03-25":
                                                #     print(f"⚠️ Recitation detected, trying another model.")
                                                #     model_name = "gemini-2.5-pro-exp-03-25"
                                                #     continue # run query again
                                                elif config.temperature <= 1.5: # online someone said that 1.3 temp and >0.97 top_p is the best
                                                    print(f"⚠️ Recitation detected, increasing temperature to {config.temperature + 0.45}")
                                                    config.temperature+=0.45
                                                    config.top_p = 0.97
                                                    continue # run query again
                                            billing_tag = f"GEM{current_key_index}"
                                            break
                                        except Exception as e:
                                            print(f"\033[91mRate limit exceeded on Gemini API key {current_key_index}, trying with Gemini API key {current_key_index + 1}\033[0m")
                                            current_key_index += 1
                                            key_retry_attempts +=1
                                            if key_retry_attempts > max_keys:
                                                print(f"\033[91mRate limit exceeded of all Gemini API keys, going to wait a little before retrying\033[0m")
                                                raise # -> goes to Exponential backoff (?)
                                else:
                                    raise # -> goes to Exponential backoff
                        else:
                            raise # -> goes to Exponential backoff

            else:
                gemini_api_key, current_key_index, max_keys = get_gemini_api_key(None)
                client = Client(vertexai=False, api_key=gemini_api_key)
                if model_name in models_thinking_budget:
                    config.thinking_config = ThinkingConfig(thinking_budget=thinking_budget)
                response = await client.aio.models.generate_content(model=model_name, contents=my_contents, config=config)
                billing_tag = f"GEMINI_{current_key_index}"

            # Langfuse logging (with system instruction handling difference)
            langfuse_input = f"{getattr(config, 'system_instruction', '') or ''}\n\n{langfuse_prompt}"  # the or '' is to handle the case where system_instruction is there but equal None
            langfuse.get_client().update_current_generation(
                input=langfuse_input,
                model=get_langfuse_model_name(model_name),
                metadata={"tags": [billing_tag]},
                usage_details={
                    "input": response.usage_metadata.prompt_token_count,
                    "output": response.usage_metadata.candidates_token_count
                }
            )

            # Delete PDF blob(s) from GCS after receiving the LLM response
            for bucket, blob_location in pdf_blobs:
                try:
                    delete_from_gcs(bucket, blob_location)
                except Exception as del_error:
                    print(f"Failed to delete blob {blob_location} from bucket {bucket}: {del_error}")

            if response_handler:
                return response_handler(response)
            else:
                return response

        except Exception as e:
            if attempt < max_retries - 1:
                print(f"_vertex_genai_common_async with prompt [{langfuse_prompt[:50]}...] call failed (attempt {attempt + 1}/{max_retries}): {e}")
                elapsed_time = time.time() - start_time
                remaining_delay = max(0, ((attempt + 1) * delay) - elapsed_time)
                print(f"Waiting {remaining_delay:.1f} seconds before retrying...")
                await asyncio.sleep(remaining_delay)
                start_time = time.time()
            else:
                print(f"_vertex_genai_common_async with prompt [{langfuse_prompt[:50]}...] call failed after {max_retries} attempts: {e}")
                raise



def encode_file_uri(image_path):
    # Get the file extension and map it to MIME type
    mime_type = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif'
    }.get(os.path.splitext(image_path)[1].lower(), 'application/octet-stream')
    
    # Read and encode the file
    with open(image_path, "rb") as file:
        base64_content = base64.b64encode(file.read()).decode("utf-8")
        return f"data:{mime_type};base64,{base64_content}"



        