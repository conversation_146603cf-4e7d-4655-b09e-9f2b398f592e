import os

import cv2
import numpy as np
import pandas as pd

from backend.AI.GC_VertexAI import vertex_genai_bounding_box_async
import asyncio
from backend.AI import Constants


def image_to_sqaureImage(img: np.ndarray, impute_color) -> np.ndarray:
    """Takes an image and an RGB color array, and returns a square image with the specified color filling the empty space.

    Args:
        img (np.ndarray): The input image.
        impute_color (array): An array of length 3 representing the RGB color.

    Returns:
        np.ndarray: A transformed square image.
    """
    height, width, _ = img.shape
    square_size = int(max(height, width))

    square_image = np.full((square_size, square_size, 3), impute_color, dtype=np.uint8)

    # Find offsets to center the original image on top of the square image
    x_offset = (square_size - width) // 2
    y_offset = (square_size - height) // 2

    square_image[y_offset : y_offset + height, x_offset : x_offset + width] = img
    return square_image


def image_parts_based_on_gemini_output(gemini_outputs: list, gemini_input_img: np.ndarray, original_img: np.ndarray) -> np.ndarray:
    """Takes in a list of outputs of the geini Flash 2.0 model and extracts the location in the image. The image will be
    cropped and saved in the specified location.

    Args:
        gemini_outputs (list): A list of dictionaries where each dictionary represents a defined location of the image.
            Each dictionary is going to have two keys: box_2d and label. The box_2d is always going to be in 
            the following format [y_min, x_min, y_max, x_max]. Each value is normalized to 0 - 1000 for every image.
        gemini_input_img (np.ndarray): The input image used for the gemini model.
        original_img (np.ndarray): The original image where parts must be extracted.

    Returns:
        a list of np.ndarray: The parts of the image.
    """

    norm_scaler = 1000
    original_img_height, original_img_width, _ = original_img.shape
    scaled_img_height, scaled_img_width, _ = gemini_input_img.shape

    scaled_factor_height = original_img_height / scaled_img_height
    scaled_factor_width = original_img_width / scaled_img_width


    parts = []
    if gemini_outputs is None:
        return parts
    
    for genimi_coordinates_dict in gemini_outputs:
        # load scaler
        scaler_y_min, scaler_x_min, scaler_y_max, scaler_x_max = (np.array(genimi_coordinates_dict["box_2d"]) / norm_scaler)

        # conver scaler to fit the image size
        x_min, y_min = (
            int(scaled_img_width * scaler_x_min * scaled_factor_width),
            int(scaled_img_height * scaler_y_min * scaled_factor_height),
        )
        x_max, y_max = (
            int(scaled_img_width * scaler_x_max * scaled_factor_width),
            int(scaled_img_height * scaler_y_max * scaled_factor_height),
        )

        # extract part of the image
        part_img = original_img[y_min:y_max, x_min:x_max]
        parts.append(part_img)

    return parts


def get_image_parts(prompt: str, img_path: str, image_url=None, image_format='jpg'):
    return asyncio.run(get_image_parts_async(prompt, img_path, image_url, image_format))

async def get_image_parts_async(prompt: str, img_path: str, image_url=None, image_format='jpg', model_name=Constants.IMAGE_MODEL_FREE, useVertexAI=True):
    """Crop the region of interest using Gemini Flash 2.0 API.
    Args:   
        img_path (str): The path to the image that needs to be cropped.

    Returns:
        list: Paths to the cropped image parts.
    """
    # Transform the image
    img = cv2.imread(img_path)
    square_img = image_to_sqaureImage(img, [255, 255, 255])

    # Resize the image to 640 x 640
    resized_square_img = cv2.resize(square_img, (640, 640))

    # Load and resize image (from google notebook cookbook)
    # from PIL import Image
    # im = Image.open(img_path) 
    # im.thumbnail([640,640], Image.Resampling.LANCZOS)
    

    # Call the AI bounding box detection
    # PROMPT = "Find possible copyrighted images"  # or "Possible copyrighted images"
    parts_details = await vertex_genai_bounding_box_async([("text", prompt), ("image_cv2", resized_square_img)], image_url=image_url, model_name=model_name, useVertexAI=useVertexAI) 
    if parts_details is None:
        return []

    # Filter out detections whose bounding boxes are too small.
    # Each bounding box is provided as "box_2d" in the format [y_min, x_min, y_max, x_max] on a normalized scale from 0 to 1000. Hence, if either (y_max - y_min) or (x_max - x_min) is less than 50, we skip that detection.
    parts_details = [
        part for part in parts_details
        if "box_2d" in part and (part["box_2d"][2] - part["box_2d"][0] >= 50 and part["box_2d"][3] - part["box_2d"][1] >= 50)
    ]

    # Transform the image back and extract the parts using the filtered details
    images_parts = image_parts_based_on_gemini_output(parts_details, resized_square_img, square_img)

    # Save the extracted parts
    images_parts_with_label = []
    for i, part in enumerate(images_parts):
        part_path = f"{img_path.split('.')[0]}_part_{i}.{image_format}"
        if image_format == 'webp':
            params = [cv2.IMWRITE_WEBP_QUALITY, 80]
        else: # Default to jpg
            params = [int(cv2.IMWRITE_JPEG_QUALITY), 80]
        
        cv2.imwrite(part_path, part, params)
        images_parts_with_label.append({"label": parts_details[i]["label"], "path": part_path, "width": part.shape[1], "height": part.shape[0]})

    return images_parts_with_label
