import cv2
import numpy as np
from PIL import Image
import io

def resize_square(image_pil, target_size):
    """Resizes an image to a square, padding if necessary."""
    original_width, original_height = image_pil.size
    ratio = float(target_size) / max(original_width, original_height)
    new_width = int(original_width * ratio)
    new_height = int(original_height * ratio)
    image_pil = image_pil.resize((new_width, new_height), Image.Resampling.LANCZOS)

    new_image = Image.new("RGB", (target_size, target_size), (255, 255, 255))
    new_image.paste(image_pil, ((target_size - new_width) // 2, (target_size - new_height) // 2))
    return new_image

def plot_boxes(image_pil, bboxes, output_format="Bounding Box"):
    """Plots bounding boxes (and optionally masks) on an image."""
    img_cv = np.array(image_pil.convert('RGB'))
    img_cv = cv2.cvtColor(img_cv, cv2.COLOR_RGB2BGR)

    for bbox_data in bboxes:
        label = bbox_data.get('label', 'N/A')
        box = bbox_data.get('box') # [ymin, xmin, ymax, xmax]
        mask_encoded = bbox_data.get('mask', None) # Expected to be hex string of bytes

        if not box or len(box) != 4:
            # print(f"Skipping invalid box: {box}") # Optional logging
            continue

        # Draw bounding box
        ymin, xmin, ymax, xmax = [int(c) for c in box]
        cv2.rectangle(img_cv, (xmin, ymin), (xmax, ymax), (0, 255, 0), 2)
        cv2.putText(img_cv, label, (xmin, ymin - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)

        if output_format == 'Bounding Box + Segmentation Mask' and mask_encoded:
            try:
                # Assuming mask_encoded is a hex string of the binary mask file (e.g., PNG bytes)
                mask_bytes = bytes.fromhex(mask_encoded)
                mask_np_array = np.frombuffer(mask_bytes, np.uint8)
                mask = cv2.imdecode(mask_np_array, cv2.IMREAD_UNCHANGED) # Use IMREAD_UNCHANGED for alpha

                if mask is not None:
                    # Resize mask to bbox size. If mask has alpha, use it. Otherwise, assume grayscale.
                    mask_target_height, mask_target_width = ymax - ymin, xmax - xmin

                    # Handle mask channels (e.g. grayscale, with alpha)
                    if len(mask.shape) == 3 and mask.shape[2] == 4: # has alpha channel
                        alpha_mask = cv2.resize(mask[:,:,3], (mask_target_width, mask_target_height), interpolation=cv2.INTER_NEAREST)
                        color_mask_part = cv2.resize(mask[:,:,:3], (mask_target_width, mask_target_height), interpolation=cv2.INTER_NEAREST)
                    elif len(mask.shape) == 2: # grayscale mask
                        alpha_mask = cv2.resize(mask, (mask_target_width, mask_target_height), interpolation=cv2.INTER_NEAREST)
                        color_mask_part = cv2.cvtColor(alpha_mask, cv2.COLOR_GRAY2BGR) # Default color to mask itself
                    else: # 3-channel mask without alpha - treat as color
                        alpha_mask = None # No separate alpha, or make one if needed
                        color_mask_part = cv2.resize(mask, (mask_target_width, mask_target_height), interpolation=cv2.INTER_NEAREST)


                    # Create an overlay for the mask region
                    roi = img_cv[ymin:ymax, xmin:xmax]

                    # If mask has its own color (e.g. from color_mask_part)
                    # For simplicity, using a fixed color overlay if only alpha_mask is present
                    mask_color_overlay = np.zeros_like(roi)
                    mask_color_chosen = [255, 0, 0] # Blue for segmentation

                    for i in range(3): # Apply color
                        if alpha_mask is not None:
                             mask_color_overlay[:,:,i] = np.where(alpha_mask > 0, mask_color_chosen[i], 0)
                        else: # if no alpha, use color_mask_part directly if it's BGR
                             mask_color_overlay[:,:,i] = np.where(cv2.cvtColor(color_mask_part, cv2.COLOR_BGR2GRAY) > 0, mask_color_chosen[i], 0)


                    # Blend overlay with the bounding box region
                    alpha_blend = 0.4 # Transparency factor
                    cv2.addWeighted(mask_color_overlay, alpha_blend, roi, 1 - alpha_blend, 0, roi)
                    img_cv[ymin:ymax, xmin:xmax] = roi
                else:
                    # print("Failed to decode mask.") # Optional logging
                    pass
            except Exception as e:
                # print(f"Error processing mask: {e}") # Optional logging
                pass

    return Image.fromarray(cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB))

def preprocess_image_for_ai(image_bytes, target_size=1024):
    """Converts image bytes to PIL, resizes to square, converts to PNG bytes for Vertex AI."""
    try:
        image_pil = Image.open(io.BytesIO(image_bytes)).convert('RGB')
    except Exception as e:
        # print(f"Error opening image bytes: {e}") # Optional logging
        raise ValueError(f"Could not open image bytes: {e}")

    image_pil_resized = resize_square(image_pil, target_size)

    img_byte_arr = io.BytesIO()
    image_pil_resized.save(img_byte_arr, format='PNG') # Vertex AI often prefers PNG
    # Return both the PIL image (for plotting) and the bytes (for AI)
    return image_pil_resized, img_byte_arr.getvalue()
