import asyncio
import io
import json
import os
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
# Import functions from GC_VertexAI.py only (no fallback to GC_VertexAI_Simple.py)
try:
    from backend.AI.GC_VertexAI import vertex_genai_bbox_mask_async, vertex_genai_bounding_box_async
    AI_AVAILABLE = True
    print("Google Generative AI integration available from GC_VertexAI.py")
except ImportError as e:
    AI_AVAILABLE = False
    print(f"GC_VertexAI.py not available: {e}")
    print("ERROR: Cannot proceed without GC_VertexAI.py - no fallback allowed")
    vertex_genai_bbox_mask_async = None
    vertex_genai_bounding_box_async = None


def get_bounding_box_storage_path():
    """Get the base directory for bounding box image storage."""
    if os.name == 'nt':  # Windows
        base_dir = os.environ.get("WIN_MAIN_DIR")
    else:  # Linux/Unix
        base_dir = os.environ.get("LINUX_MAIN_DIR")

    if not base_dir:
        # Fallback if environment variables not set
        return os.path.join(os.getcwd(), "BoundingBox")
    return os.path.join(base_dir, "BoundingBox")

def get_output_images_path():
    """Get the directory for output (model-generated) images."""
    return os.path.join(get_bounding_box_storage_path(), "Output")

def save_output_image(image_bytes: bytes, filename: str) -> str:
    """Save output image to the proper directory and return the file path."""
    output_dir = get_output_images_path()
    os.makedirs(output_dir, exist_ok=True)

    file_path = os.path.join(output_dir, filename)
    with open(file_path, 'wb') as f:
        f.write(image_bytes)

    return file_path


def resize_and_square_image(img: np.ndarray, target_size_x: int, target_size_y: int, impute_color=[255, 255, 255]) -> tuple[np.ndarray, tuple[int, int]]:
    """
    Resizes an image to target dimensions and then pads it to be square,
    maintaining the larger dimension of the target size.

    Args:
        img (np.ndarray): The input image (OpenCV format).
        target_size_x (int): Target width for resizing.
        target_size_y (int): Target height for resizing.
        impute_color (list): RGB color for padding.

    Returns:
        tuple[np.ndarray, tuple[int, int]]: A tuple containing the transformed square image
                                             and the dimensions (height, width) of the image *before* squaring.
    """
    # 1. Resize the image first
    resized_img = cv2.resize(img, (target_size_x, target_size_y), interpolation=cv2.INTER_AREA)
    resized_height, resized_width, _ = resized_img.shape

    # 2. Pad the resized image to be square
    square_size = max(resized_height, resized_width)
    square_image = np.full((square_size, square_size, 3), impute_color, dtype=np.uint8)

    # Calculate offsets to center the resized image
    x_offset = (square_size - resized_width) // 2
    y_offset = (square_size - resized_height) // 2

    square_image[y_offset : y_offset + resized_height, x_offset : x_offset + resized_width] = resized_img

    return square_image, (resized_height, resized_width) # Return squared image and original resized dimensions


def plot_boxes_on_image(image_np: np.ndarray, boxes_data: list, original_resized_dims: tuple[int, int]) -> Image.Image:
    """
    Draws bounding boxes and labels on a NumPy image array using PIL.
    Coordinates are scaled from Gemini's 0-1000 range to the image dimensions *before* squaring.

    Args:
        image_np (np.ndarray): The image (potentially squared) on which boxes were detected (OpenCV format).
        boxes_data (list): List of dictionaries from Gemini, each with 'box_2d' and 'label'.
        original_resized_dims (tuple[int, int]): The (height, width) of the image *after* resizing but *before* squaring.

    Returns:
        Image.Image: PIL Image object with boxes and labels drawn.
    """
    # Convert OpenCV image (BGR) to PIL image (RGB)
    # We draw on the potentially squared image provided
    image_pil = Image.fromarray(cv2.cvtColor(image_np, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(image_pil)

    # Dimensions for coordinate scaling are from the image *before* squaring
    target_height, target_width = original_resized_dims
    norm_scaler = 1000.0  # Gemini uses 0-1000 scale

    # Dimensions of the canvas (potentially squared image)
    canvas_height, canvas_width = image_np.shape[:2]
    # Calculate offsets if the image was squared
    x_offset = (canvas_width - target_width) // 2
    y_offset = (canvas_height - target_height) // 2


    try:
        # Attempt to load a specific font (adjust path if needed)
        # font = ImageFont.truetype("NotoSansCJK-Regular.ttc", 15)
        # Fallback to default font
        font = ImageFont.load_default()
    except IOError:
        print("Warning: Specified font not found. Using default font.")
        font = ImageFont.load_default()

    if boxes_data is None:
        print("No bounding box data received.")
        return image_pil # Return original image if no boxes

    for box_info in boxes_data:
        if "box_2d" not in box_info or "label" not in box_info:
            print(f"Skipping incomplete box data: {box_info}")
            continue # Skip if data is incomplete

        coords = box_info["box_2d"] # [y_min, x_min, y_max, x_max]
        label = box_info["label"]

        # Scale coordinates from 0-1000 to the *original resized* image dimensions
        y_min_scaled = coords[0] * target_height / norm_scaler
        x_min_scaled = coords[1] * target_width / norm_scaler
        y_max_scaled = coords[2] * target_height / norm_scaler
        x_max_scaled = coords[3] * target_width / norm_scaler

        # Adjust coordinates by the offset to place them correctly on the squared canvas
        x_min = int(x_min_scaled + x_offset)
        y_min = int(y_min_scaled + y_offset)
        x_max = int(x_max_scaled + x_offset)
        y_max = int(y_max_scaled + y_offset)

        # Draw rectangle (outline)
        draw.rectangle([x_min, y_min, x_max, y_max], outline="red", width=3)

        # Draw label background and text
        try:
            text_bbox = draw.textbbox((0, 0), label, font=font) # Use (0,0) for size calculation
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]

            # Position background rectangle above the bounding box
            bg_y0 = y_min - text_height - 4 # Small gap
            bg_y1 = y_min - 2

            # Adjust if background goes off the top edge
            if bg_y0 < 0:
                bg_y0 = y_max + 2
                bg_y1 = y_max + text_height + 4

            draw.rectangle([x_min, bg_y0, x_min + text_width + 4, bg_y1], fill="red")
            draw.text((x_min + 2, bg_y0 + 1), label, fill="white", font=font)
        except Exception as e:
            print(f"Error drawing text/label '{label}': {e}")


    return image_pil


async def get_image_with_bounding_boxes_async(
    image_bytes: bytes,
    prompt: str,
    model_name: str,
    resize_x: int = 640,
    resize_y: int = 640,
    image_format: str = 'png', # Default output format
    include_masks: bool = False, # New parameter to request segmentation masks
    save_output: bool = True, # Whether to save output image to disk
    output_filename: str = None, # Optional filename for output image
    min_box_size: int = 10 # Minimum box size in pixels (adjustable)
) -> tuple[bytes | None, dict | None, list[dict] | None, str | None]: # Updated return type to include file path
    """
    Processes an image to find objects based on a prompt using a Gemini model,
    draws bounding boxes, optionally extracts segmentation masks, and returns the results.

    Args:
        image_bytes (bytes): Raw bytes of the uploaded image.
        prompt (str): User prompt for object detection.
        model_name (str): Name of the Gemini model to use.
        resize_x (int): Target width for resizing. Defaults to 640.
        resize_y (int): Target height for resizing. Defaults to 640.
        image_format (str): Original image format (e.g., 'jpg', 'png', 'webp'). Used for output encoding.
        include_masks (bool): If True, attempt to extract segmentation masks from the AI response. Defaults to False.
        save_output (bool): If True, save the output image to disk. Defaults to True.
        output_filename (str): Optional filename for the output image. If None, generates a unique name.

    Returns:
        tuple[bytes | None, dict | None, list[dict] | None, str | None]: A tuple containing:
            - bytes: The processed image bytes with bounding boxes (in the specified format, default PNG). None on failure.
            - dict: The raw JSON response from the Gemini model. None on failure.
            - list[dict] | None: A list of segmentation masks if requested and available, otherwise None.
                                Each dict contains {'label': str, 'mask_base64': str}.
            - str | None: The file path where the output image was saved, if save_output is True. None otherwise.
    """
    processed_image_bytes = None
    gemini_json_output = None
    segmentation_masks = None # Initialize mask data
    output_file_path = None # Initialize output file path

    try:
        # 1. Read image bytes using OpenCV
        image_np_array = np.frombuffer(image_bytes, np.uint8)
        # Use IMREAD_UNCHANGED to handle transparency (e.g., PNG) and potentially other formats
        img_original = cv2.imdecode(image_np_array, cv2.IMREAD_UNCHANGED)

        if img_original is None:
            raise ValueError("Could not decode image bytes. Check image format/integrity.")

        # Handle transparency: If 4 channels, convert to BGR for processing
        if img_original.shape[2] == 4:
            # Simple conversion: replace transparency with white background
            # Create a white background
            white_bg = np.full((img_original.shape[0], img_original.shape[1], 3), 255, dtype=np.uint8)
            # Extract alpha channel and normalize
            alpha = img_original[:, :, 3] / 255.0
            # Blend foreground (BGR) with white background
            img_bgr = np.zeros_like(white_bg)
            for i in range(3):
                img_bgr[:, :, i] = img_original[:, :, i] * alpha + white_bg[:, :, i] * (1 - alpha)
            img_to_process = img_bgr
        elif img_original.shape[2] == 3:
             img_to_process = img_original
        else:
            raise ValueError(f"Unsupported number of channels: {img_original.shape[2]}")


        # 2. Resize and square the image (if needed, adapt logic based on model requirements)
        # Following the pattern from GCV_GetImageParts: resize then square
        # The squaring might not be strictly necessary for all models, but provides consistency
        img_for_gemini, original_resized_dims = resize_and_square_image(img_to_process, resize_x, resize_y)
        print(f"Image resized to {original_resized_dims} then squared to {img_for_gemini.shape[:2]}")

        # 3. Interact with AI Model
        # Use fallback model if provided name is invalid or empty
        effective_model_name = model_name if model_name else "gemini-2.0-flash-exp" # Fallback model
        print(f"Using Gemini model: {effective_model_name}")

        # Prepare input for the AI function (assuming it takes text and cv2 image)
        # The exact format might depend on the vertex_genai_bounding_box_async implementation
        ai_input = [("text", prompt), ("image_cv2", img_for_gemini)]

        # Choose which function to call based on output type
        # Use vertex_genai_bounding_box_async for "bounding box" and vertex_genai_bbox_mask_async for "bounding box + segmentation mask"
        if include_masks:
            # For "bounding box + segmentation mask", use vertex_genai_bbox_mask_async from GC_VertexAI.py
            if AI_AVAILABLE:
                print("Using vertex_genai_bbox_mask_async for segmentation masks")
                gemini_json_output = await vertex_genai_bbox_mask_async(
                    ai_input,
                    model_name=effective_model_name,
                    include_masks=include_masks
                )
            else:
                raise ImportError("AI integration not available for segmentation masks")
        else:
            # For "bounding box", use vertex_genai_bounding_box_async from GC_VertexAI.py
            if AI_AVAILABLE:
                print("Using vertex_genai_bounding_box_async for bounding boxes")
                gemini_json_output = await vertex_genai_bounding_box_async(
                    ai_input,
                    model_name=effective_model_name
                )
            else:
                raise ImportError("AI integration not available for bounding boxes")

        if gemini_json_output is None:
            print("Gemini API call returned None.")
            # Return the original (resized) image without boxes? Or None? Returning None for now.
            return None, {"error": "AI processing returned no response"}, None, None # Indicate failure, include None for masks

        # Check if the response is an error object
        if isinstance(gemini_json_output, dict) and "error" in gemini_json_output:
            print(f"Gemini API returned error: {gemini_json_output}")
            return None, gemini_json_output, None, None # Return the error details

        # Filter out very small boxes using configurable threshold
        if isinstance(gemini_json_output, list):
            original_count = len(gemini_json_output)
            gemini_json_output = [
                part for part in gemini_json_output
                if "box_2d" in part and len(part["box_2d"]) == 4 and (
                    part["box_2d"][2] - part["box_2d"][0] >= min_box_size and  # Use configurable threshold
                    part["box_2d"][3] - part["box_2d"][1] >= min_box_size      # Use configurable threshold
                )
            ]
            print(f"Received {original_count} bounding boxes, {len(gemini_json_output)} after filtering (removed {original_count - len(gemini_json_output)} boxes smaller than {min_box_size}x{min_box_size} pixels).")

            # If no bounding boxes remain after filtering, provide more helpful error message
            if len(gemini_json_output) == 0:
                if original_count > 0:
                    print(f"All {original_count} detected boxes were too small (< {min_box_size}x{min_box_size} pixels). Consider using a different prompt or model.")
                    return None, {"error": "All detected bounding boxes were too small", "details": f"Found {original_count} boxes but all were smaller than {min_box_size}x{min_box_size} pixels"}, None, None
                else:
                    print("No bounding boxes detected by AI model. This could be due to image content, prompt specificity, or model limitations.")
                    return None, {"error": "No objects detected in image", "details": "The AI model did not detect any objects matching the prompt. Try a more specific prompt or different image."}, None, None

        else:
             print(f"Warning: Unexpected AI output format: {type(gemini_json_output)}")
             # Attempt to proceed if it's dict-like, otherwise fail
             if not isinstance(gemini_json_output, (dict, list)):
                 return None, gemini_json_output, None # Return raw output if not list/dict, include None for masks


        # 4. Plot Bounding Boxes
        # Draw on the image that was sent to Gemini (img_for_gemini)
        # Pass the original resized dimensions for correct coordinate scaling
        processed_pil_image = plot_boxes_on_image(img_for_gemini, gemini_json_output, original_resized_dims)

        # 4.5 Extract Segmentation Masks (if requested and available)
        if include_masks and isinstance(gemini_json_output, list):
            masks_list = []
            for item in gemini_json_output:
                # Check for the 'mask' key as specified in the new prompt's expected output
                if isinstance(item, dict) and 'mask' in item:
                    # The mask prompt might not return 'label'. Handle its potential absence.
                    label = item.get('label', 'Object') # Use 'Object' as default if label is missing
                    mask_base64 = item['mask']

                    # Validate the mask data before processing
                    if mask_base64 and isinstance(mask_base64, str) and len(mask_base64) > 10:
                        # Basic check if mask_base64 looks like base64 data URI, prepend if not
                        if not mask_base64.startswith('data:image'):
                             # Assuming PNG if not specified, adjust if needed
                            mask_base64 = f'data:image/png;base64,{mask_base64}'

                        # Additional validation: check if it's valid base64 and valid image
                        try:
                            import base64
                            from PIL import Image
                            from io import BytesIO

                            # Extract base64 part
                            if ',' in mask_base64:
                                base64_part = mask_base64.split(',')[1]
                            else:
                                base64_part = mask_base64

                            # Try to decode and validate as image
                            decoded_data = base64.b64decode(base64_part)

                            # Try to open as image to validate
                            test_image = Image.open(BytesIO(decoded_data))
                            width, height = test_image.size

                            # Check if image is not empty/blank
                            if width > 0 and height > 0:
                                # Additional check: see if image has any non-transparent pixels
                                if test_image.mode in ('RGBA', 'LA'):
                                    # Check alpha channel
                                    alpha_data = test_image.getchannel('A') if test_image.mode == 'RGBA' else test_image.getchannel(1)
                                    alpha_values = list(alpha_data.getdata())
                                    has_content = any(alpha > 0 for alpha in alpha_values)
                                else:
                                    # For non-transparent images, check if not all pixels are the same
                                    pixel_data = list(test_image.getdata())
                                    has_content = len(set(pixel_data)) > 1

                                if has_content:
                                    masks_list.append({
                                        'label': label,
                                        'mask_base64': mask_base64
                                    })
                                    print(f"✅ Valid segmentation mask extracted for: {label} ({width}x{height})")
                                else:
                                    print(f"⚠️ Blank/empty mask detected for {label} - skipping")
                            else:
                                print(f"⚠️ Zero-size mask detected for {label} - skipping")

                        except Exception as e:
                            print(f"⚠️ Invalid mask data for {label}: {e}")
                            print(f"   Mask data preview: {str(mask_base64)[:100]}...")
                    else:
                        print(f"⚠️ Empty or invalid mask data for: {label}")

            if masks_list:
                segmentation_masks = masks_list
                print(f"✅ Successfully extracted {len(segmentation_masks)} valid segmentation masks.")
            else:
                # This message might appear if the AI response format is unexpected or doesn't contain 'mask'
                print("⚠️ No valid segmentation masks found in the response")
                print("   Proceeding with bounding boxes only (fallback behavior)")
                print("   This may indicate the model doesn't support segmentation or returned invalid mask data.")

        # 5. Convert final PIL image to bytes
        output_buffer = io.BytesIO()
        output_format = image_format.upper() if image_format.upper() in ['JPEG', 'PNG', 'WEBP'] else 'PNG'
        if output_format == 'JPEG':
            # Convert RGBA to RGB for JPEG saving if needed
            if processed_pil_image.mode == 'RGBA':
                processed_pil_image = processed_pil_image.convert('RGB')
            processed_pil_image.save(output_buffer, format='JPEG', quality=90)
        elif output_format == 'WEBP':
             processed_pil_image.save(output_buffer, format='WEBP', quality=90)
        else: # Default to PNG
            processed_pil_image.save(output_buffer, format='PNG')

        processed_image_bytes = output_buffer.getvalue()

        # 6. Save output image if requested
        if save_output and processed_image_bytes:
            if output_filename is None:
                import time
                timestamp = int(time.time() * 1000)  # milliseconds
                output_filename = f"bounding_box_output_{timestamp}.{image_format.lower()}"

            try:
                output_file_path = save_output_image(processed_image_bytes, output_filename)
                print(f"Output image saved to: {output_file_path}")
            except Exception as save_error:
                print(f"Failed to save output image: {save_error}")
                # Continue without failing the entire function

    except ValueError as ve:
        print(f"Value Error processing image: {ve}")
        # Optionally return original image bytes or None
        return None, {"error": str(ve)}, None, None
    except ImportError as ie:
         print(f"Import Error: {ie}")
         return None, {"error": str(ie)}, None, None
    except Exception as e:
        print(f"An unexpected error occurred in get_image_with_bounding_boxes_async: {e}")
        import traceback
        traceback.print_exc()
        # Return None and potentially the error message in the JSON part
        return None, {"error": f"Unexpected error: {e}"}, None, None

    # 7. Return results
    return processed_image_bytes, gemini_json_output, segmentation_masks, output_file_path


# Example Usage (for testing purposes)
async def main_test():
    # Create a dummy image (e.g., 800x600 blue image)
    width, height = 800, 600
    dummy_img_np = np.zeros((height, width, 3), dtype=np.uint8)
    dummy_img_np[:, :] = (255, 0, 0) # Blue in BGR

    # Encode it to bytes (e.g., PNG)
    is_success, buffer = cv2.imencode(".png", dummy_img_np)
    if not is_success:
        print("Failed to encode dummy image")
        return
    image_bytes = buffer.tobytes()

    prompt = "Find the blue area"
    model = "gemini-2.0-flash-exp" # Example model

    # --- Mocking the AI call ---
    original_vertex_func = None
    if AI_AVAILABLE:
        original_vertex_func = vertex_genai_bounding_box_async

    async def mock_vertex_genai_bounding_box_async(input_data, model_name=None, request_masks=False, **kwargs):
        print(f"Mock AI call with model: {model_name}, request_masks: {request_masks}")
        # Simulate finding one box covering roughly the middle
        mock_response = [{"box_2d": [250, 250, 750, 750], "label": "Blue Area"}]
        if request_masks:
            # Add mock mask data if requested
            mock_response[0]['segmentation_mask_base64'] = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=' # 1x1 black pixel
        return mock_response

    # Replace the actual function with the mock
    if AI_AVAILABLE:
        globals()['vertex_genai_bounding_box_async'] = mock_vertex_genai_bounding_box_async
    # --- End Mocking ---


    print("Running test...")
    processed_bytes, json_out, mask_data = await get_image_with_bounding_boxes_async(
        image_bytes, prompt, model, resize_x=300, resize_y=300, image_format='png', include_masks=True # Test with masks enabled
    )

    # Restore original function if it existed
    if original_vertex_func:
        globals()['vertex_genai_bounding_box_async'] = original_vertex_func


    if processed_bytes:
        print("Test completed. Processed image generated.")
        # Save the output image for inspection
        try:
            with open("test_output_bounding_box.png", "wb") as f:
                f.write(processed_bytes)
            print("Saved output to test_output_bounding_box.png")
        except Exception as e:
            print(f"Error saving test output: {e}")
        print("JSON Output:", json_out)
        if mask_data:
            print(f"Received {len(mask_data)} masks.")
            # print("Mask Data:", mask_data) # Optionally print full mask data
        else:
            print("No mask data received.")
    else:
        print("Test failed. No processed image returned.")
        print("JSON Output:", json_out) # Also print JSON on failure


if __name__ == "__main__":
    # To run the test: python -m AI.BoundingBox
    # Note: Running asyncio top-level functions might require asyncio.run()
    # Check if an event loop is already running (e.g., in Jupyter)
    try:
        loop = asyncio.get_running_loop()
        if loop.is_running():
            print("Asyncio loop already running. Creating task.")
            loop.create_task(main_test())
        else:
             asyncio.run(main_test())
    except RuntimeError: # No running event loop
        print("No running asyncio loop. Starting new one.")
        asyncio.run(main_test())