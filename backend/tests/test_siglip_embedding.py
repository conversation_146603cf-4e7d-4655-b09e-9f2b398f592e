import torch
from transformers import AutoModel, AutoProcessor
from transformers.image_utils import load_image
import os
from sklearn.decomposition import PCA
import numpy as np

# load the model and processor
ckpt = "google/siglip2-large-patch16-512"
# Use device_map="auto" to automatically handle device placement
model = AutoModel.from_pretrained(ckpt).eval()
processor = AutoProcessor.from_pretrained(ckpt, use_fast=True)

# load the image
# Use the provided local path
image_path = "/home/<USER>/Desktop/localModelTestData/ModelTestsWorkbenchData/pictures/copyright/product/67_A_<PERSON><PERSON>.jpg"
# Check if the file exists before attempting to load
if not os.path.exists(image_path):
    print(f"Error: Image file not found at {image_path}")
else:
    image = load_image(image_path)
    inputs = processor(images=[image], return_tensors="pt").to(model.device)

    # run inference
    with torch.no_grad():
        image_embeddings = model.get_image_features(**inputs)

    print(f"Siglip image embedding shape: {image_embeddings.shape}")
    print("Siglip image embedding values:")
    print(image_embeddings)

    # For a simple 2D visualization, take the first two components
    # Ensure the tensor is on CPU and convert to numpy
    image_embeddings_np = image_embeddings.cpu().numpy()
    x_coord = image_embeddings_np[0, 0]
    y_coord = image_embeddings_np[0, 1]

    print(f"\nSiglip image embedding (Simple 2D): X={x_coord}, Y={y_coord}")