# backend/models/implementations/embedding.py
import logging
import torch # Keep for TimmModel and potentially ClipModel if GPU is used
from sentence_transformers import SentenceTransformer # Added for ClipModel



device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = SentenceTransformer(
    "jinaai/jina-clip-v2",
    trust_remote_code=True # Add this based on user feedback
)
# clip_model = ClipModel(model_id, config)
print(f"ClipModel initialized successfully")