# backend/tests/test_single_image_embeddings.py

import os
import platform
import json
import importlib
import logging
from pathlib import Path
from typing import List, Optional, Type, Dict, Any
import numpy as np
from PIL import Image # Needed for ClipModel and potentially others

# Assuming tensorflow, torch, timm, sentence-transformers are installed as per requirements.txt
import tensorflow as tf
import torch
# import timm # TimmModel might be used
# from sentence_transformers import SentenceTransformer # ClipModel might be used

# Project specific imports
try:
    # Attempt relative import if run as part of the package
    from ..utils.file_utils import get_master_folder, ALLOWED_EXTENSIONS
    from ..models.base import ImageModelBase
except ImportError:
    # Fallback for running the script directly (adjust paths as needed)
    import sys
    # Add the project root to the Python path
    PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent
    sys.path.insert(0, str(PROJECT_ROOT))
    from backend.utils.file_utils import get_master_folder, ALLOWED_EXTENSIONS
    from backend.models.base import ImageModelBase


# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
CONFIG_FILE_PATH = Path(__file__).resolve().parent.parent.parent / 'models' / 'config.json'


# --- Helper Functions ---

def find_first_image(directory: Path) -> Optional[Path]:
    """
    Recursively finds the first image file in the given directory.

    Args:
        directory: The directory to search within.

    Returns:
        The Path object of the first image found, or None if no image is found.
    """
    logger.info(f"Searching for the first image file in: {directory}")
    if not directory.is_dir():
        logger.error(f"Directory not found: {directory}")
        return None

    for item in directory.rglob('*'):
        if item.is_file() and item.suffix.lower().lstrip('.') in ALLOWED_EXTENSIONS:
            logger.info(f"Found first image file: {item}")
            return item

    logger.warning(f"No image files found in directory: {directory}")
    return None

def load_all_models(config_path: Path) -> List[ImageModelBase]:
    """
    Loads ALL models defined in the config file.

    Args:
        config_path: Path to the models/config.json file.

    Returns:
        A list of instantiated model objects (embedding, descriptor, hash, etc.).
    """
    models = []
    logger.info(f"Loading ALL models from config: {config_path}")
    try:
        with open(config_path, 'r') as f:
            config_data = json.load(f)
    except FileNotFoundError:
        logger.error(f"Model configuration file not found: {config_path}")
        return models
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from {config_path}: {e}")
        return models
    except Exception as e:
        logger.error(f"An unexpected error occurred reading {config_path}: {e}")
        return models

    for model_config in config_data:
        # Remove the filter for 'embedding' type
        model_id = model_config.get('model_id')
        model_type = model_config.get('model_type') # Get type for logging
        implementation_details = model_config.get('implementation')
        if not model_id or not implementation_details or not model_type:
            logger.warning(f"Skipping invalid model config entry (missing id, type, or implementation): {model_config}")
            continue

        module_name = implementation_details.get('module')
        class_name = implementation_details.get('class')
        parameters = implementation_details.get('parameters', {})

        if not module_name or not class_name:
            logger.warning(f"Skipping model '{model_id}' due to missing module/class name.")
            continue

        logger.info(f"Attempting to load model: {model_id} (Type: {model_type}, Class: {class_name})")
        try:
            module = importlib.import_module(module_name)
            ModelClass = getattr(module, class_name)

            # Check if it's a subclass of ImageModelBase before instantiation
            if not issubclass(ModelClass, ImageModelBase):
                 logger.error(f"Class {class_name} from {module_name} is not a subclass of ImageModelBase. Skipping.")
                 continue

            # Instantiate the model
            # Pass both model_id and parameters as expected by revised base/implementations
            model_instance = ModelClass(model_id=model_id, config=parameters) # Use 'config' keyword arg
            models.append(model_instance)
            logger.info(f"Successfully prepared model instance for: {model_id}")

        except ImportError:
            logger.error(f"Failed to import module: {module_name} for model {model_id}", exc_info=True)
        except AttributeError:
            logger.error(f"Failed to find class: {class_name} in module {module_name} for model {model_id}", exc_info=True)
        except Exception as e:
            logger.error(f"Failed to instantiate model {model_id} ({class_name}): {e}", exc_info=True)

    logger.info(f"Loaded {len(models)} models.")
    return models

# --- Main Execution Logic ---

def main():
    """
    Main function to find an image and compute features one model at a time.
    """
    logger.info("--- Starting Single Image Feature Computation Test ---")

    # 1. Get Master Folder based on OS
    try:
        master_folder = get_master_folder()
        logger.info(f"Using master folder: {master_folder}")
    except ValueError as e:
        logger.error(f"Environment variable error: {e}")
        logger.error("Cannot proceed without a valid master folder path (WIN_MAIN_DIR or LINUX_MAIN_DIR).")
        return
    except OSError as e:
        logger.error(f"OS error related to master folder: {e}")
        return

    # 2. Find the first image file
    image_path = find_first_image(master_folder)
    if not image_path:
        logger.error(f"Could not find any image file in {master_folder} or its subdirectories. Exiting.")
        return

    logger.info(f"Will compute features for image: {image_path}")

    # 3. Get model configurations without loading models
    try:
        with open(CONFIG_FILE_PATH, 'r') as f:
            config_data = json.load(f)
    except FileNotFoundError:
        logger.error(f"Model configuration file not found: {CONFIG_FILE_PATH}")
        return
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from {CONFIG_FILE_PATH}: {e}")
        return

    # 4. Process each model configuration sequentially
    logger.info("\n--- Computing Features ---")
    results = {}

    for model_config in config_data:
        model_id = model_config.get('model_id')
        model_type = model_config.get('model_type')
        implementation_details = model_config.get('implementation')

        if not all([model_id, model_type, implementation_details]):
            logger.warning(f"Skipping invalid model config entry: {model_config}")
            continue

        module_name = implementation_details.get('module')
        class_name = implementation_details.get('class')
        parameters = implementation_details.get('parameters', {})

        if not all([module_name, class_name]):
            logger.warning(f"Skipping model '{model_id}' due to missing module/class name.")
            continue

        logger.info(f"\nProcessing model: {model_id} (Type: {model_type})")

        try:
            # Load model class
            module = importlib.import_module(module_name)
            ModelClass = getattr(module, class_name)

            if not issubclass(ModelClass, ImageModelBase):
                logger.error(f"Class {class_name} is not a subclass of ImageModelBase. Skipping.")
                continue

            # Instantiate and load single model
            logger.info(f"Loading model {model_id}...")
            model = ModelClass(model_id=model_id, config=parameters)
            model.load()
            logger.info(f"Model {model_id} loaded successfully.")

            # Process image with current model
            try:
                # Preprocess the image
                logger.info(f"Preprocessing image for {model_id}...")
                preprocessed_data = model.preprocess(str(image_path))
                logger.info(f"Image preprocessed successfully for {model_id}.")

                # Compute features
                logger.info(f"Computing features with {model_id}...")
                features = model.compute_features(preprocessed_data)
                logger.info(f"Features computed successfully for {model_id}.")

                # Store and print result info - handle different types
                if isinstance(features, np.ndarray):
                    result_info = f"NumPy array, Shape: {features.shape}, dtype: {features.dtype}"
                    preview = f", Preview: {features.flatten()[:5]}..." if features.size > 0 and features.size < 20 else ""
                    results[model_id] = {"type": "numpy", "shape": features.shape, "dtype": str(features.dtype)}
                    logger.info(f"Result for {model_id}: {result_info}{preview}")
                elif isinstance(features, str):
                    result_info = f"String, Length: {len(features)}"
                    preview = f", Value: {features}" if len(features) < 100 else f", Value (start): {features[:100]}..."
                    results[model_id] = {"type": "string", "length": len(features)}
                    logger.info(f"Result for {model_id}: {result_info}{preview}")
                elif isinstance(features, (int, float)):
                    result_info = f"Number ({type(features).__name__}), Value: {features}"
                    results[model_id] = {"type": type(features).__name__, "value": features}
                    logger.info(f"Result for {model_id}: {result_info}")
                elif isinstance(features, tuple) and model_type == 'descriptor':
                    kp_info = f"Keypoints: {len(features[0])}" if len(features) > 0 and hasattr(features[0], '__len__') else "Keypoints: N/A"
                    desc_info = f"Descriptors Shape: {features[1].shape}" if len(features) > 1 and isinstance(features[1], np.ndarray) else "Descriptors: N/A"
                    result_info = f"Descriptor Tuple - {kp_info}, {desc_info}"
                    results[model_id] = {"type": "descriptor_tuple", "keypoints_count": len(features[0]) if len(features) > 0 else 0, "descriptors_shape": features[1].shape if len(features) > 1 and isinstance(features[1], np.ndarray) else None}
                    logger.info(f"Result for {model_id}: {result_info}")
                elif isinstance(features, list):
                    try:
                        features_np = np.array(features)
                        result_info = f"List converted to NumPy array, Shape: {features_np.shape}, dtype: {features_np.dtype}"
                        results[model_id] = {"type": "list_to_numpy", "shape": features_np.shape, "dtype": str(features_np.dtype)}
                    except Exception:
                        result_info = f"List, Length: {len(features)}, Type of first element: {type(features[0]) if features else 'N/A'}"
                        results[model_id] = {"type": "list", "length": len(features), "element_type": str(type(features[0])) if features else 'N/A'}
                    logger.info(f"Result for {model_id}: {result_info}")
                else:
                    result_info = f"Type: {type(features)}"
                    try:
                        size_info = f", Size/Length: {len(features)}" if hasattr(features, '__len__') else ""
                        result_info += size_info
                    except TypeError:
                        pass
                    results[model_id] = {"type": str(type(features))}
                    logger.info(f"Result for {model_id}: {result_info}")

            finally:
                # Always unload the model after processing
                logger.info(f"Unloading model {model_id}...")
                model.unload()
                logger.info(f"Model {model_id} unloaded.")

        except ImportError:
            logger.error(f"Failed to import module: {module_name} for model {model_id}", exc_info=True)
            results[model_id] = f"Error: Module import failed"
        except AttributeError:
            logger.error(f"Failed to find class: {class_name} in module {module_name} for model {model_id}", exc_info=True)
            results[model_id] = f"Error: Class not found"
        except Exception as e:
            logger.error(f"An error occurred while processing with model {model_id}: {e}", exc_info=True)
            results[model_id] = f"Error: {str(e)}"

    logger.info("\n--- Feature Computation Summary ---")
    for name, result in results.items():
        logger.info(f"- {name}: {result}")

    logger.info("\n--- Test Script Finished ---")


if __name__ == "__main__":
    main()