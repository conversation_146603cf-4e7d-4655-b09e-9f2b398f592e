"""Me<PERSON> heads

Revision ID: 2a6eb6eeec0f
Revises: 3b2c1d0e6f7a, remove_gcp_model_name_and_file_path
Create Date: 2025-06-24 22:18:16.700954

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2a6eb6eeec0f'
down_revision: Union[str, None] = ('3b2c1d0e6f7a', 'remove_gcp_model_name_and_file_path')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
