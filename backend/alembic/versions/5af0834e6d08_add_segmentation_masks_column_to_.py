"""Add segmentation_masks column to bounding_box_results

Revision ID: 5af0834e6d08
Revises: 2a6eb6eeec0f
Create Date: 2025-06-24 22:18:24.452690

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5af0834e6d08'
down_revision: Union[str, None] = '2a6eb6eeec0f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add segmentation_masks column to bounding_box_results table
    op.add_column('bounding_box_results', sa.Column('segmentation_masks', sa.Text(), nullable=True))


def downgrade() -> None:
    # Remove segmentation_masks column from bounding_box_results table
    op.drop_column('bounding_box_results', 'segmentation_masks')
