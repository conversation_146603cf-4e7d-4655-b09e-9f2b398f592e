"""add_bounding_box_tables

Revision ID: 2a1b3c4d5e6f
Revises: 8f1926ed4ce5
Create Date: YYYY-MM-DD HH:MM:SS.MS

"""
from alembic import op
import sqlalchemy as sa
import datetime

# revision identifiers, used by Alembic.
revision = '2a1b3c4d5e6f'
down_revision = '8f1926ed4ce5'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table('bounding_box_models',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('is_active', sa.<PERSON>(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=True, default=datetime.datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=True, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bounding_box_models_id'), 'bounding_box_models', ['id'], unique=False)
    op.create_index(op.f('ix_bounding_box_models_name'), 'bounding_box_models', ['name'], unique=True)

    op.create_table('bounding_box_pictures',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('filename', sa.String(), nullable=False),
        sa.Column('image_data', sa.Text(), nullable=True), # Base64 encoded image
        sa.Column('file_path', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True, default=datetime.datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=True, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bounding_box_pictures_id'), 'bounding_box_pictures', ['id'], unique=False)
    op.create_index(op.f('ix_bounding_box_pictures_filename'), 'bounding_box_pictures', ['filename'], unique=True)
    op.create_index(op.f('ix_bounding_box_pictures_file_path'), 'bounding_box_pictures', ['file_path'], unique=True)


    op.create_table('bounding_box_experiments',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('picture_id', sa.Integer(), nullable=False),
        sa.Column('prompt', sa.Text(), nullable=False),
        sa.Column('resize_height', sa.Integer(), nullable=False),
        sa.Column('resize_width', sa.Integer(), nullable=False),
        sa.Column('output_type', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True, default=datetime.datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=True, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow),
        sa.ForeignKeyConstraint(['picture_id'], ['bounding_box_pictures.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    # Note: Removed model_id from experiments table as one experiment can run against multiple models,
    # results table will link experiment and model.
    op.create_index(op.f('ix_bounding_box_experiments_id'), 'bounding_box_experiments', ['id'], unique=False)

    op.create_table('bounding_box_results',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('experiment_id', sa.Integer(), nullable=False),
        sa.Column('model_id', sa.Integer(), nullable=False),
        sa.Column('status', sa.String(), nullable=False, default='pending'),
        sa.Column('output_image_path', sa.String(), nullable=True),
        sa.Column('score', sa.Integer(), nullable=True),
        sa.Column('bounding_boxes', sa.Text(), nullable=True), # JSON string
        sa.Column('inference_time', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True, default=datetime.datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=True, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow),
        sa.ForeignKeyConstraint(['experiment_id'], ['bounding_box_experiments.id'], ),
        sa.ForeignKeyConstraint(['model_id'], ['bounding_box_models.id'], ),
        sa.PrimaryKeyConstraint('id')
        # Removed UniqueConstraint on experiment_id to allow multiple results (one per model) for an experiment
    )
    op.create_index(op.f('ix_bounding_box_results_id'), 'bounding_box_results', ['id'], unique=False)
    op.create_index(op.f('ix_bounding_box_results_experiment_id'), 'bounding_box_results', ['experiment_id'], unique=False)
    op.create_index(op.f('ix_bounding_box_results_model_id'), 'bounding_box_results', ['model_id'], unique=False)


def downgrade():
    op.drop_index(op.f('ix_bounding_box_results_model_id'), table_name='bounding_box_results')
    op.drop_index(op.f('ix_bounding_box_results_experiment_id'), table_name='bounding_box_results')
    op.drop_index(op.f('ix_bounding_box_results_id'), table_name='bounding_box_results')
    op.drop_table('bounding_box_results')

    op.drop_index(op.f('ix_bounding_box_experiments_id'), table_name='bounding_box_experiments')
    op.drop_table('bounding_box_experiments')

    op.drop_index(op.f('ix_bounding_box_pictures_file_path'), table_name='bounding_box_pictures')
    op.drop_index(op.f('ix_bounding_box_pictures_filename'), table_name='bounding_box_pictures')
    op.drop_index(op.f('ix_bounding_box_pictures_id'), table_name='bounding_box_pictures')
    op.drop_table('bounding_box_pictures')

    op.drop_index(op.f('ix_bounding_box_models_name'), table_name='bounding_box_models')
    op.drop_index(op.f('ix_bounding_box_models_id'), table_name='bounding_box_models')
    op.drop_table('bounding_box_models')
