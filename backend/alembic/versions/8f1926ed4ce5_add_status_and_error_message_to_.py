"""Add status and error_message to modeltests_feature_status

Revision ID: 8f1926ed4ce5
Revises: 47d4789fa691
Create Date: 2025-05-03 23:52:02.308530

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8f1926ed4ce5'
down_revision: Union[str, None] = '47d4789fa691'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
