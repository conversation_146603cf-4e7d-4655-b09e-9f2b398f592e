"""Initial schema creation based on current models

Revision ID: 47d4789fa691
Revises: 
Create Date: 2025-05-03 01:25:03.124354 # Keep original date for consistency

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '47d4789fa691'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Add pgcrypto extension needed for UUID generation
    op.execute("CREATE EXTENSION IF NOT EXISTS pgcrypto;")

    # Create modeltests_images table
    op.create_table('modeltests_images',
        sa.Column('image_id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text("gen_random_uuid()")),
        sa.Column('original_filename', sa.Text(), nullable=False),
        sa.Column('relative_path', sa.Text(), nullable=False, unique=True),
        sa.Column('image_type', sa.String(length=10), nullable=False),
        sa.Column('ip_category', sa.String(length=50), nullable=True),
        sa.Column('ip_owner', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), onupdate=sa.text('now()'), nullable=False),
        sa.Column('file_last_modified', sa.TIMESTAMP(timezone=True), nullable=True),
        sa.CheckConstraint("image_type IN ('product', 'ip')", name='check_image_type'),
        sa.CheckConstraint("ip_category IN ('trademark', 'copyright', 'patent')", name='check_ip_category'),
        sa.CheckConstraint("(image_type != 'ip') OR (ip_category IS NOT NULL)", name='check_ip_category_consistency')
    )
    op.create_index('idx_image_type_category', 'modeltests_images', ['image_type', 'ip_category'], unique=False)

    # Create modeltests_models table
    op.create_table('modeltests_models',
        sa.Column('model_id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('model_name', sa.String(length=255), nullable=False, unique=True),
        sa.Column('model_type', sa.String(length=20), nullable=False),
        sa.Column('applicable_ip_category', postgresql.ARRAY(sa.Text()), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), server_default=sa.true(), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), onupdate=sa.text('now()'), nullable=False),
        sa.CheckConstraint("model_type IN ('embedding', 'descriptor', 'hash')", name='check_model_type')
    )

    # Create modeltests_combined_scores_config table
    op.create_table('modeltests_combined_scores_config',
        sa.Column('config_id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text("gen_random_uuid()")),
        sa.Column('config_name', sa.String(length=255), nullable=False, unique=True),
        sa.Column('ip_category', sa.String(length=50), nullable=False),
        sa.Column('model_weights', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('is_active', sa.Boolean(), server_default=sa.true(), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), onupdate=sa.text('now()'), nullable=False),
        sa.CheckConstraint("ip_category IN ('trademark', 'copyright', 'patent')", name='check_config_ip_category')
    )

    # Create modeltests_comparison_results table
    op.create_table('modeltests_comparison_results',
        sa.Column('result_id', sa.BIGINT(), autoincrement=True, primary_key=True),
        sa.Column('product_image_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('ip_image_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('model_id', postgresql.UUID(as_uuid=True), nullable=False), # Refers to modeltests_models OR modeltests_combined_scores_config
        sa.Column('similarity_score', sa.Float(), nullable=False),
        sa.Column('computed_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['ip_image_id'], ['modeltests_images.image_id'], name='fk_comparison_results_ip_image', ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['product_image_id'], ['modeltests_images.image_id'], name='fk_comparison_results_product_image', ondelete='CASCADE'),
        # Cannot add FK for model_id due to dual nature
        sa.CheckConstraint('similarity_score >= 0 AND similarity_score <= 1', name='check_similarity_score_range'),
        sa.UniqueConstraint('product_image_id', 'ip_image_id', 'model_id', name='uq_comparison_result')
    )
    op.create_index('idx_comparison_results_ip', 'modeltests_comparison_results', ['ip_image_id'], unique=False)
    op.create_index('idx_comparison_results_model', 'modeltests_comparison_results', ['model_id'], unique=False)
    op.create_index('idx_comparison_results_product_model_score', 'modeltests_comparison_results', ['product_image_id', 'model_id', sa.text('similarity_score DESC')], unique=False)


    # Create modeltests_ground_truth table
    op.create_table('modeltests_ground_truth',
        sa.Column('ground_truth_id', sa.BIGINT(), autoincrement=True, primary_key=True),
        sa.Column('product_image_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('correct_ip_image_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['correct_ip_image_id'], ['modeltests_images.image_id'], name='fk_ground_truth_correct_ip_image', ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['product_image_id'], ['modeltests_images.image_id'], name='fk_ground_truth_product_image', ondelete='CASCADE'),
        sa.UniqueConstraint('product_image_id', 'correct_ip_image_id', name='uq_ground_truth')
    )
    op.create_index('idx_ground_truth_product', 'modeltests_ground_truth', ['product_image_id'], unique=False)

    # Create modeltests_feature_storage table
    op.create_table('modeltests_feature_storage',
        sa.Column('feature_id', sa.BIGINT(), autoincrement=True, primary_key=True),
        sa.Column('image_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('model_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('features', postgresql.BYTEA(), nullable=False),
        sa.Column('computed_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['image_id'], ['modeltests_images.image_id'], name='fk_feature_storage_image', ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['model_id'], ['modeltests_models.model_id'], name='fk_feature_storage_model', ondelete='CASCADE'),
        sa.UniqueConstraint('image_id', 'model_id', name='uq_feature_storage')
    )
    op.create_index('idx_feature_storage_lookup', 'modeltests_feature_storage', ['model_id', 'image_id'], unique=False)

    # Create modeltests_feature_status table
    op.create_table('modeltests_feature_status',
        sa.Column('status_id', sa.BIGINT(), autoincrement=True, primary_key=True),
        sa.Column('image_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('model_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('last_computed_at', sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column('status', sa.String(length=20), server_default='pending', nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['image_id'], ['modeltests_images.image_id'], name='fk_feature_status_image', ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['model_id'], ['modeltests_models.model_id'], name='fk_feature_status_model', ondelete='CASCADE'),
        sa.UniqueConstraint('image_id', 'model_id', name='uq_feature_status')
    )
    op.create_index('idx_feature_status_model', 'modeltests_feature_status', ['model_id'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop tables and indexes in reverse order of creation

    op.drop_index('idx_feature_status_model', table_name='modeltests_feature_status')
    op.drop_table('modeltests_feature_status')

    op.drop_index('idx_feature_storage_lookup', table_name='modeltests_feature_storage')
    op.drop_table('modeltests_feature_storage')

    op.drop_index('idx_ground_truth_product', table_name='modeltests_ground_truth')
    op.drop_table('modeltests_ground_truth')

    op.drop_index('idx_comparison_results_product_model_score', table_name='modeltests_comparison_results')
    op.drop_index('idx_comparison_results_model', table_name='modeltests_comparison_results')
    op.drop_index('idx_comparison_results_ip', table_name='modeltests_comparison_results')
    op.drop_table('modeltests_comparison_results')

    op.drop_table('modeltests_combined_scores_config')

    op.drop_table('modeltests_models')

    op.drop_index('idx_image_type_category', table_name='modeltests_images')
    op.drop_table('modeltests_images')

    # Optionally drop the extension
    # op.execute("DROP EXTENSION IF EXISTS pgcrypto;")

    # ### end Alembic commands ###
