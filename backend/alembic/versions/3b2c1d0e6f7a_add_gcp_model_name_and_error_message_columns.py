"""add_gcp_model_name_and_error_message_columns

Revision ID: 3b2c1d0e6f7a
Revises: 2a1b3c4d5e6f
Create Date: YYYY-MM-DD HH:MM:SS.MS

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3b2c1d0e6f7a'
down_revision = '2a1b3c4d5e6f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bounding_box_models', schema=None) as batch_op:
        batch_op.add_column(sa.Column('gcp_model_name', sa.String(length=255), nullable=True))

    with op.batch_alter_table('bounding_box_results', schema=None) as batch_op:
        batch_op.add_column(sa.Column('error_message', sa.Text(), nullable=True))
        batch_op.alter_column('status',
               existing_type=sa.VARCHAR(), # Assuming previous type was String without length or default VARCHAR
               type_=sa.String(length=50), # New type with length
               existing_nullable=False,
               server_default='pending') # Ensure server_default is maintained if it was set
        batch_op.alter_column('output_image_path',
               existing_type=sa.VARCHAR(), # Assuming previous type was String without length
               type_=sa.String(length=1024), # New type with length
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bounding_box_results', schema=None) as batch_op:
        batch_op.alter_column('output_image_path',
               existing_type=sa.String(length=1024),
               type_=sa.VARCHAR(), # Revert to original type (assuming String/VARCHAR without length)
               existing_nullable=True)
        batch_op.alter_column('status',
               existing_type=sa.String(length=50),
               type_=sa.VARCHAR(), # Revert to original type
               existing_nullable=False,
               server_default='pending')
        batch_op.drop_column('error_message')

    with op.batch_alter_table('bounding_box_models', schema=None) as batch_op:
        batch_op.drop_column('gcp_model_name')
    # ### end Alembic commands ###
