import os
import time
import logging

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

# --- Configuration from Environment Variables ---
# Read retry parameters from environment variables with sensible defaults
MAX_RETRIES = 5
INITIAL_DELAY = 1.0  # seconds
BACKOFF_FACTOR = 2.0

# --- Logger Setup ---
logger = logging.getLogger(__name__)

# --- Exception Imports and Definitions ---
# import specific exception types: These are needed to define the exception tuples below.

import psycopg2
from sqlalchemy.exc import OperationalError as SQLAlchemyOperationalError, PendingRollbackError
# Include both the base psycopg2 error and the SQLAlchemy wrapper
# Note: PendingRollbackError is handled separately in the function, not listed here.
DB_RETRYABLE_EXCEPTIONS = (
    psycopg2.OperationalError,
    SQLAlchemyOperationalError,
)


import httpx
from qdrant_client.http import exceptions as qdrant_exceptions
# Add other relevant httpx or Qdrant exceptions if needed
QDRANT_RETRYABLE_EXCEPTIONS = (
    httpx.RemoteProtocolError,
    httpx.ReadTimeout, # Example: Add other relevant httpx exceptions
    qdrant_exceptions.ResponseHandlingException,
    qdrant_exceptions.UnexpectedResponse # Example: Add other relevant Qdrant exceptions
)


# Combine common retryable exceptions if needed, or define others
# Example: ALL_RETRYABLE_EXCEPTIONS = DB_RETRYABLE_EXCEPTIONS + QDRANT_RETRYABLE_EXCEPTIONS


# --- Retry Utility Function ---
def execute_with_retry(
    func,
    retryable_exceptions,
    max_retries=MAX_RETRIES,
    initial_delay=INITIAL_DELAY,
    backoff_factor=BACKOFF_FACTOR,
    session=None, # Optional SQLAlchemy session for rollback handling
    operation_name=None, # Optional name for logging clarity
    *args,
    **kwargs
):
    """
    Executes a function with retry logic for specified exceptions,
    including handling for SQLAlchemy's PendingRollbackError if a session is provided.

    Args:
        func (callable): The function to execute.
        retryable_exceptions (tuple): A tuple of exception classes that should trigger a retry.
        max_retries (int): Maximum number of retry attempts.
        initial_delay (float): Initial delay in seconds before the first retry.
        backoff_factor (float): Multiplier for the delay in subsequent retries (exponential backoff).
        session (sqlalchemy.orm.Session, optional): An SQLAlchemy session object. If provided,
            `rollback()` will be called on this session when a `PendingRollbackError` occurs
            before retrying. Defaults to None.
        operation_name (str, optional): A descriptive name for the operation being retried,
            used for clearer logging. Defaults to None.
        *args: Positional arguments to pass to the function.
        **kwargs: Keyword arguments to pass to the function.

    Returns:
        The result of the function call if successful. None if retries fail but don't raise.

    Raises:
        The caught exception if retries are exhausted or if a non-retryable exception occurs.
    """
    attempt_number = 0
    op_name = operation_name or func.__name__ # Use provided name or function name for logs
    while attempt_number <= max_retries:
        try:
            result = func(*args, **kwargs)
            # If the function returns successfully, reset attempt count (though we return immediately)
            # attempt_number = 0 # Not strictly needed as we return
            return result  # Success
        except PendingRollbackError as pre:
            attempt_number += 1
            log_prefix = f"Operation '{op_name}'"
            logger.warning(f"⚠️ {log_prefix} encountered PendingRollbackError (Attempt {attempt_number}/{max_retries}).")

            if session:
                try:
                    logger.info(f"{log_prefix}: Attempting rollback due to PendingRollbackError.")
                    session.rollback()
                    logger.info(f"{log_prefix}: Rollback successful.")
                except Exception as rollback_err:
                    # If rollback itself fails, log critically and re-raise the original PendingRollbackError
                    logger.critical(
                        f"{log_prefix}: Rollback failed after PendingRollbackError: {rollback_err}",
                        exc_info=True
                    )
                    # Re-raise the original error that led to the rollback attempt
                    raise pre from rollback_err
            else:
                logger.error(f"{log_prefix}: PendingRollbackError occurred, but no session provided for rollback. Cannot proceed.")
                # If no session, we can't recover, so re-raise immediately.
                raise pre

            if attempt_number > max_retries:
                logger.error(
                    f"{log_prefix} failed after {max_retries} retries (including rollback attempts). Final exception: {pre}",
                    exc_info=False # Avoid duplicate stack trace if logged above
                )
                raise pre # Max retries reached

            delay = initial_delay * (backoff_factor ** (attempt_number - 1))
            logger.warning(f"{log_prefix}: Retrying after rollback in {delay:.2f} seconds...")
            time.sleep(delay)
            # Continue to the next iteration

        except retryable_exceptions as e:
            attempt_number += 1
            log_prefix = f"Operation '{op_name}'"
            if attempt_number > max_retries:
                logger.error(
                    f"{log_prefix} failed after {max_retries} retries. Final exception: {type(e).__name__}: {e}",
                    exc_info=True # Include stack trace in log
                )
                raise e  # Max retries reached, re-raise the last exception

            delay = initial_delay * (backoff_factor ** (attempt_number - 1))
            logger.warning(
                f"{log_prefix} encountered retryable error (Attempt {attempt_number}/{max_retries}): {type(e).__name__}. "
                f"Retrying in {delay:.2f} seconds..."
            )
            time.sleep(delay)
            # Continue to the next iteration

        except Exception as e:
            # Non-retryable exception encountered
            log_prefix = f"Operation '{op_name}'"
            logger.error(
                f"{log_prefix} encountered non-retryable error: {type(e).__name__}: {e}",
                exc_info=True # Include stack trace in log
            )
            raise e # Re-raise immediately

    # This part should theoretically not be reached if max_retries >= 0
    # If loop finishes without success or exception re-raise (e.g., max_retries is -1), return None or raise error.
    # For safety, raise an error if we exit the loop unexpectedly.
    final_error_msg = f"Operation '{op_name}' exited retry loop unexpectedly after {attempt_number} attempts."
    logger.error(final_error_msg)
    raise RuntimeError(final_error_msg)

# Example Usage (can be removed or kept for testing)
# def might_fail_db():
#     import random
#     if random.random() < 0.7:
#         print("DB Operation failed")
#         raise psycopg2.OperationalError("Simulated DB connection error")
#     else:
#         print("DB Operation succeeded")
#         return "DB Data"

# def might_fail_qdrant():
#     import random
#     if random.random() < 0.6:
#         print("Qdrant Operation failed")
#         raise httpx.RemoteProtocolError("Simulated Qdrant network error")
#     else:
#         print("Qdrant Operation succeeded")
#         return "Qdrant Data"

# if __name__ == "__main__":
#     logging.basicConfig(level=logging.INFO)
#     print("--- Testing DB Retry ---")
#     try:
#         db_result = execute_with_retry(might_fail_db, DB_RETRYABLE_EXCEPTIONS, max_retries=3)
#         print(f"DB Result: {db_result}")
#     except Exception as e:
#         print(f"DB Final Error: {e}")

#     print("\n--- Testing Qdrant Retry ---")
#     try:
#         qdrant_result = execute_with_retry(might_fail_qdrant, QDRANT_RETRYABLE_EXCEPTIONS, max_retries=4, initial_delay=0.5)
#         print(f"Qdrant Result: {qdrant_result}")
#     except Exception as e:
#         print(f"Qdrant Final Error: {e}")

#     print("\n--- Testing Non-Retryable Error ---")
#     def raises_value_error():
#         raise ValueError("This should not be retried")
#     try:
#         execute_with_retry(raises_value_error, DB_RETRYABLE_EXCEPTIONS)
#     except ValueError as e:
#         print(f"Caught expected non-retryable error: {e}")