import os
import platform
import uuid
from pathlib import Path
from werkzeug.utils import secure_filename
from flask import current_app, Request
from typing import Tuple, Optional

# Allowed image extensions based on FR2.1.2
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp', 'tiff'}
MAX_FILE_SIZE_MB = 5 # FR2.1.2
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024

def get_master_folder() -> Path:
    """
    Determines the MASTERFOLDER path based on the operating system and environment variables.
    FR2.1.1: Masterfolder is os.environ['WIN_MAIN_DIR'] when os is 'nt' and os.environ['LINUX_MAIN_DIR'] otherwise.
    """
    os_name = platform.system().lower()
    if os_name == 'windows':
        folder_path_str = os.environ.get('WIN_MAIN_DIR')
        if not folder_path_str:
            current_app.logger.error("WIN_MAIN_DIR environment variable not set for Windows.")
            raise ValueError("WIN_MAIN_DIR environment variable not set for Windows.")
    else: # Assume Linux/Unix-like
        folder_path_str = os.environ.get('LINUX_MAIN_DIR')
        if not folder_path_str:
            current_app.logger.error("LINUX_MAIN_DIR environment variable not set for Linux/Unix.")
            raise ValueError("LINUX_MAIN_DIR environment variable not set for Linux/Unix.")

    master_path = Path(folder_path_str)
    # Ensure the base directory exists, create if not (as per setup.py requirement FR4.56)
    try:
        master_path.mkdir(parents=True, exist_ok=True)
    except OSError as e:
        current_app.logger.error(f"Error creating master folder {master_path}: {e}")
        raise OSError(f"Could not create master folder: {master_path}") from e
    return master_path

def get_image_folder_path(ip_category: str, image_type: str) -> Path:
    """
    Constructs the full path to the specific image category folder.
    FR2.1.1: Folder Structure: MASTERFOLDER/<ip_type>/<image_type>/
    """
    master_folder = get_master_folder()
    # Basic validation for ip_type and image_type
    valid_ip_categories = {'trademark', 'copyright', 'patent'}
    valid_image_types = {'product', 'ip'}

    if ip_category not in valid_ip_categories:
        raise ValueError(f"Invalid ip_category: {ip_category}. Must be one of {valid_ip_categories}")
    if image_type not in valid_image_types:
        raise ValueError(f"Invalid image_type: {image_type}. Must be one of {valid_image_types}")

    folder_path = master_folder / ip_category / image_type
    # Ensure the specific category directory exists
    try:
        folder_path.mkdir(parents=True, exist_ok=True)
    except OSError as e:
        current_app.logger.error(f"Error creating image category folder {folder_path}: {e}")
        raise OSError(f"Could not create image category folder: {folder_path}") from e
    return folder_path

def allowed_file(filename: str) -> bool:
    """Checks if the file extension is allowed."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_file(file) -> Tuple[bool, str]:
    """Validates file based on extension and size."""
    if not file or not file.filename:
        return False, "No file selected."
    if not allowed_file(file.filename):
        return False, f"Invalid file type. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"

    # Check file size (requires seeking, which consumes the stream once)
    file.seek(0, os.SEEK_END)
    file_length = file.tell()
    file.seek(0) # Reset stream position for subsequent reads
    if file_length > MAX_FILE_SIZE_BYTES:
        return False, f"File exceeds maximum size of {MAX_FILE_SIZE_MB}MB."
    if file_length == 0:
        return False, "File is empty."

    return True, "File is valid."

def save_uploaded_file(file, ip_category: str, image_type: str) -> Tuple[Optional[Path], Optional[str]]:
    """
    Validates and saves an uploaded file to the correct directory, checking for conflicts.
    Returns the saved file path and original filename, or (None, error_message).
    FR2.1.2: Handle file uploads, validate formats/size, check for conflicts, save files.
    """
    is_valid, error_message = validate_file(file)
    if not is_valid:
        return None, error_message

    original_filename = secure_filename(file.filename)
    target_folder = get_image_folder_path(ip_category, image_type)
    target_path = target_folder / original_filename

    # FR2.1.2: Filename Conflicts Check
    if target_path.exists():
        error = f"Filename conflict: '{original_filename}' already exists in {ip_category}/{image_type}. Delete the existing file first if replacement is intended."
        current_app.logger.warning(error)
        return None, error

    try:
        file.save(target_path)
        current_app.logger.info(f"Saved uploaded file to: {target_path}")
        return target_path, original_filename
    except Exception as e:
        current_app.logger.error(f"Error saving file {original_filename} to {target_path}: {e}")
        # Attempt to clean up partially saved file if possible, though file.save usually handles this
        if target_path.exists():
            try:
                target_path.unlink()
            except OSError:
                pass # Ignore cleanup error
        return None, f"Could not save file: {e}"

def delete_image_file(image_path_str: str) -> bool:
    """Deletes the physical image file."""
    image_path = Path(image_path_str)
    try:
        if image_path.is_file():
            image_path.unlink()
            current_app.logger.info(f"Deleted image file: {image_path_str}")
            return True
        else:
            current_app.logger.warning(f"Attempted to delete non-existent file: {image_path_str}")
            return False # Or True if non-existence is acceptable
    except OSError as e:
        current_app.logger.error(f"Error deleting file {image_path_str}: {e}")
        return False

def construct_file_path_from_db(image_record) -> Path:
    """Constructs the full file path from a ModelTestsImage database record."""
    master_folder = get_master_folder()
    # Determine ip_category based on image_type
    if image_record.image_type == 'ip':
        ip_cat = image_record.ip_category
    elif image_record.image_type == 'product':
        # Need to infer ip_category for product images. This might require context
        # or a change in how product images are associated with categories.
        # For now, let's assume we can get it somehow or raise an error.
        # This part needs refinement based on how product categories are determined.
        # A temporary, potentially incorrect assumption:
        # Look for a related ground truth or comparison result to infer category? Risky.
        # Let's raise an error for now, indicating this needs resolving.
        raise NotImplementedError("Cannot reliably determine ip_category for 'product' images from the record alone. Context needed.")
        # Example placeholder if context was available:
        # ip_cat = context.get('ip_category') # Get from request or other source
    else:
        raise ValueError(f"Invalid image_type in record: {image_record.image_type}")

    if not ip_cat:
         raise ValueError(f"Could not determine ip_category for image {image_record.image_id}")

    return master_folder / ip_cat / image_record.image_type / image_record.original_filename