# Use an official Python runtime as a parent image
FROM python:3.10-slim

# Set the working directory in the container
WORKDIR /app

# Install system dependencies that might be needed by Python packages
# (Add any specific dependencies here if required later)
# RUN apt-get update && apt-get install -y --no-install-recommends some-package && rm -rf /var/lib/apt/lists/*

# Copy the requirements file into the container at /app
COPY backend/requirements.txt .

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire backend directory content into the container at /app
COPY backend/ .

# Copy the models config file
COPY models/config.json ./models/config.json

# Copy the setup.py script (might be useful)
COPY setup.py .

# Make port 5000 available to the world outside this container
EXPOSE 5000

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=backend.api:create_app()
# Add other environment variables if needed, or rely on docker-compose env_file

# Define the command to run the application
# Using flask run for development simplicity, consider gunicorn for production
CMD ["flask", "run", "--host=0.0.0.0"]