# Gemini API Implementation for Bounding Box System

## Overview

This document describes the implementation of dual API support for Gemini models in the bounding box visualization system. The system now automatically selects between **Vertex AI** and **GEMINI_API_KEY** based on the model name.

## API Selection Logic

### Automatic Model-Based Selection

The system uses the following logic to determine which API to use:

- **Gemini Flash models** → Use `GEMINI_API_KEY`
- **Gemini Pro models** → Use **Vertex AI**
- **Other models** → Default to **Vertex AI**

### Model Name Detection

The selection is based on case-insensitive string matching:

```python
def should_use_vertex_ai(model_name):
    # Use Vertex AI for Pro models
    if 'pro' in model_name.lower():
        return True
    
    # Use GEMINI_API_KEY for Flash models
    if 'flash' in model_name.lower():
        return False
    
    # Default to Vertex AI for other models
    return True
```

## Implementation Details

### Files Modified

1. **`backend/AI/GC_VertexAI_Simple.py`**
   - Added `should_use_vertex_ai()` function
   - Modified `vertex_genai_bbox_mask_async()` to auto-select API
   - Added GEMINI_API_KEY configuration logic

2. **`backend/AI/GC_VertexAI.py`**
   - Added `should_use_vertex_ai_for_model()` function
   - Modified `vertex_genai_bounding_box_async()` to auto-select API
   - Updated `_vertex_genai_common_async()` to prioritize GEMINI_API_KEY

3. **`.env`**
   - Added `GEMINI_API_KEY=AIzaSyAr0ULgItej6DcS13be520rTsR-8l6w7YM`

### Environment Variables

The system requires the following environment variable:

```bash
GEMINI_API_KEY=your_gemini_api_key_here
```

This key is obtained from [Google AI Studio](https://aistudio.google.com/app/apikey).

### Vertex AI Configuration

Vertex AI continues to use the service account file:
- **File**: `backend/AI/GoogleCouldServiceAccountKey/trodata-key.json`
- **Project**: `trodata`
- **Location**: `us-central1`

## Model Examples

### Flash Models (Use GEMINI_API_KEY)
- `gemini-1.5-flash`
- `gemini-2.0-flash-exp`
- `gemini-2.5-flash-preview-05-20`
- `gemini-2.5-flash`

### Pro Models (Use Vertex AI)
- `gemini-1.5-pro`
- `gemini-2.5-pro-exp-03-25`
- `gemini-2.5-pro-preview-06-05`
- `gemini-2.5-pro`

## Testing

### Test Scripts Created

1. **`test_model_api_selection.py`**
   - Tests the API selection logic
   - Verifies environment variable configuration
   - Validates service account file existence

2. **`create_test_models.py`**
   - Creates test models in the database
   - Demonstrates the API selection for different model types

### Running Tests

```bash
# Test the API selection logic
python test_model_api_selection.py

# Create test models in database
python create_test_models.py
```

## Usage in Bounding Box System

### Model Management

1. **Create Models**: Use the frontend to create new models with appropriate names
2. **Automatic Selection**: The system automatically chooses the correct API based on model name
3. **Fallback Logic**: If the preferred API fails, the system falls back to the alternative

### Experiment Execution

When running experiments:

1. **Flash models** will use the `GEMINI_API_KEY` for faster, cost-effective processing
2. **Pro models** will use **Vertex AI** for more advanced capabilities
3. **Logging** shows which API is being used for each model

### Console Output

The system provides clear logging:

```
Model: gemini-1.5-flash
Will use GEMINI_API_KEY for this model

Model: gemini-1.5-pro
Will use Vertex AI for this model
```

## Benefits

1. **Cost Optimization**: Flash models use the more cost-effective GEMINI_API_KEY
2. **Performance**: Pro models leverage Vertex AI's advanced features
3. **Automatic Selection**: No manual configuration required
4. **Fallback Support**: Graceful degradation if one API is unavailable
5. **Backward Compatibility**: Existing models continue to work

## Configuration Requirements

### Environment Setup

Ensure the following are configured:

1. **GEMINI_API_KEY** in `.env` file
2. **Service account file** for Vertex AI
3. **Database models** with appropriate names

### API Keys

- **GEMINI_API_KEY**: For Flash models
- **Service Account**: For Pro models via Vertex AI

## Troubleshooting

### Common Issues

1. **Missing GEMINI_API_KEY**: Flash models will fall back to Vertex AI
2. **Invalid Service Account**: Pro models will attempt GEMINI_API_KEY fallback
3. **Network Issues**: System will retry with exponential backoff

### Debug Information

The system provides detailed logging for troubleshooting:
- API selection decisions
- Authentication status
- Fallback attempts
- Error messages with context

## Future Enhancements

Potential improvements:

1. **Model-specific configuration**: Database field for API preference
2. **Load balancing**: Distribute requests across multiple keys
3. **Cost tracking**: Monitor usage per API type
4. **Performance metrics**: Compare response times between APIs
