version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    volumes:
      # Mount the project root for live code reloading during development
      - .:/app
    ports:
      - "5000:5000"
    depends_on:
      - redis
    env_file:
      - .env
    # Command to run the Flask development server
    command: flask run --host=0.0.0.0

  worker:
    build:
      context: .
      dockerfile: backend/Dockerfile
    volumes:
      # Mount the project root for live code reloading
      - .:/app
      # Mount the pictures directory from the host into the container
      # The target path inside the container comes from LINUX_MAIN_DIR in .env
      - ./pictures:/app/Data/pictures
    depends_on:
      - redis
    env_file:
      - .env
    # Command to run the Celery worker
    command: celery -A backend.celery_app worker --loglevel=info

  redis:
    image: "redis:alpine"
    ports:
      # Expose Redis port if needed for external debugging, otherwise optional
      - "6379:6379"

# Define named volumes if needed for persistent data (not strictly required for redis cache)
# volumes:
#   redis_data: