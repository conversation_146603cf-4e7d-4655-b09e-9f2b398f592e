**Internal data visualization roadmap**

I have a database with patent information. I want to build a visualization platform for it.

I already have another project using Flask backend and React front end. Ideally I want to add to that project, to reuse component and follow the same style. Even tough the 2 are not really related.

Maybe we can make them related by having a higher level menu that allow to select between both? In the future there might be 4 or 5 different platforms. Please advice


Requirements: 

1. Dashboard:
   1. statistics about what is in the database, number of records, piechart by each different classification (top 10 and others), % of Tro == True, design vs utility patent, % with missing : Applicant, inventors, assignee, associated patents, patent_title, classification
   2. statistics about what is in the database (same statistics) but only for a subset of the data: where TRO == True
2. Patent exploration: one big table with patent records. Ability to select how many patent per page. Ability to filter by key fields. For Tro field, have a drop down with true and false. Ability to select which columns to show (document id is always shown and title)
   1. When we click on a patent document ID we get an overlay with all the information available about this patent, including the lookup of the classification definition
   2. When we click on a patent title we get an overlay with all the the patent pictures
   3. After filtering the data, above the table we get some quick stats about the results:How many results, %TRO, % Design patents. This is for the whole answer to the request, not for page 1 of the table
