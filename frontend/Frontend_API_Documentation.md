## Frontend API Consumption Documentation

This document outlines how the frontend JavaScript files consume backend APIs.

### `frontend/src/services/api.js`

This file centralizes all API communication.

* **`uploadImages(formData)`**
  * **Endpoint:** `POST /api/data/images`
  * **Request Parameters:**
    * `formData`: A `FormData` object.
      * `files[]`: Array of files to upload.
      * `ip_category`: String (e.g., "copyright", "trademark", "patent").
      * `image_type`: String (e.g., "ip", "product").
  * **Response Data Structure (Expected based on usage in `ImageUpload.js`):**
    * `response.data.success_count`: Number of successfully uploaded images.
    * `response.data.message`: String success message.
    * `response.data.detail`: String error message (on failure).
* **`listImages(params)`**
  * **Endpoint:** `GET /api/data/images`
  * **Request Parameters (as an object):**
    * `params.ip_category`: String (optional).
    * `params.image_type`: String (optional, e.g., "product").
    * `params.missing_ip_owner`: <PERSON><PERSON><PERSON> (optional).
    * `params.page`: Number (optional).
    * `params.per_page`: Number (optional).
  * **Response Data Structure (Expected based on usage in `ImageBrowser.js` and `ByProductView.js`):**
    * `response.data.images`: Array of image objects. Each image object is expected to have:
      * `id` or `image_id`: String (UUID).
      * `original_filename` or `filename`: String.
      * `ip_category` or `category`: String.
      * `ip_owner`: String (nullable).
      * `uploaded_at`: String (date-time).
      * (Potentially other fields like `thumbnail_url` if used directly, though `ImageBrowser.js` constructs it as `/api/data/images/${image.id}/thumbnail`)
* **`getImageFile(imageId)`**
  * **Endpoint:** `GET /api/data/images/file/{image_id}`
  * **Request Parameters:**
    * `imageId`: String (UUID, path parameter).
  * **Response Data Structure:**
    * Raw image data (handled as a `blob`).
* **`deleteImage(imageId)`**
  * **Endpoint:** `DELETE /api/data/images/{image_id}`
  * **Request Parameters:**
    * `imageId`: String (UUID, path parameter).
  * **Response Data Structure (Expected based on usage in `ImageBrowser.js`):**
    * `response.data.detail`: String error message (on failure).
    * (Success is likely indicated by status code).
* **`updateImageIpOwner(imageId, ipOwner)`**
  * **Endpoint:** `PUT /api/data/images/{image_id}/ip_owner`
  * **Request Parameters:**
    * `imageId`: String (UUID, path parameter).
    * Request Body: `{ "ip_owner": ipOwner }` (where `ipOwner` is a string).
  * **Response Data Structure (Expected based on usage in `ImageBrowser.js`):**
    * `response.data.detail`: String error message (on failure).
    * (Success is likely indicated by status code).
* **`listModels(params)`**
  * **Endpoint:** `GET /api/models`
  * **Request Parameters (as an object):**
    * `params.ip_category`: String (optional, though comments suggest backend might not support it yet).
  * **Response Data Structure (Expected based on usage in `ByModelView.js`, `ModelManagement.js`, `CombinedScoresConfig.js`):**
    * `response.data`: Array of model objects. Each model object is expected to have:
      * `model_id`: String (UUID).
      * `model_name`: String.
      * `model_type`: String.
      * `applicable_ip_category` or `applicable_categories`: Array of strings.
      * `description`: String (nullable).
      * `is_active` or `active`: Boolean.
* **`refreshModels()`**
  * **Endpoint:** `POST /api/models/refresh`
  * **Request Parameters:** None.
  * **Response Data Structure (Expected based on usage in `ModelManagement.js`):**
    * `response.data.message`: String success/status message.
    * `response.data.detail`: String error message (on failure).
* **`computeFeatures(ipCategory)`**
  * **Endpoint:** `POST /api/tasks/compute-features/{ip_category}`
  * **Request Parameters:**
    * `ipCategory`: String (path parameter).
  * **Response Data Structure (Expected based on usage in `FeatureComputation.js`):**
    * `response.data.task_id`: String (UUID of the background task).
    * `response.data.detail`: String error message (on failure).
* **`getCombinedScores(params)`**
  * **Endpoint:** `GET /api/combined-scores`
  * **Request Parameters (as an object):**
    * `params.ip_category`: String (optional).
    * `params.is_active`: Boolean (optional).
  * **Response Data Structure (Expected based on usage in `ByModelView.js` and `CombinedScoresConfig.js`):**
    * `response.data`: Array of combined score configuration objects. Each object is expected to have:
      * `config_id` or `id`: String (UUID).
      * `config_name` or `name`: String.
      * `ip_category`: String.
      * `model_weights`: Array of objects, each with `model_name` (String) and `weight` (Number).
      * (Potentially `is_active` if filtered by it).
* **`updateCombinedScoreConfig(configId, configData)`**
  * **Endpoint:** `PUT /api/combined-scores/{config_id}`
  * **Request Parameters:**
    * `configId`: String (UUID, path parameter).
    * `configData` (Request Body): Object with:
      * `name`: String.
      * `ip_category`: String.
      * `model_weights`: Array of objects (`{ model_name: String, weight: Number }`).
  * **Response Data Structure (Expected based on usage in `CombinedScoresConfig.js`):**
    * `response.data.detail`: String error message (on failure).
    * (Success is likely indicated by status code).
* **`createCombinedScoreConfig(configData)`**
  * **Endpoint:** `POST /api/combined-scores`
  * **Request Parameters:**
    * `configData` (Request Body): Object with:
      * `name`: String.
      * `ip_category`: String.
      * `model_weights`: Array of objects (`{ model_name: String, weight: Number }`).
  * **Response Data Structure (Expected based on usage in `CombinedScoresConfig.js`):**
    * `response.data.detail`: String error message (on failure).
    * (Success is likely indicated by status code, possibly returns the created config).
* **`deleteCombinedScoreConfig(configId)`**
  * **Endpoint:** `DELETE /api/combined-scores/{config_id}`
  * **Request Parameters:**
    * `configId`: String (UUID, path parameter).
  * **Response Data Structure (Expected based on usage in `CombinedScoresConfig.js`):**
    * `response.data.detail`: String error message (on failure).
    * (Success is likely indicated by status code).
* **`getResultsByModel(params)`**
  * **Endpoint:** `GET /api/results/by-model`
  * **Request Parameters (as an object):**
    * `params.model_id`: String (UUID).
    * `params.ip_category`: String.
    * `params.page`: Number.
    * `params.per_page`: Number.
    * `params.limit`: Number (optional, commented out in `ByModelView.js`).
  * **Response Data Structure (Expected based on usage in `ByModelView.js`):**
    * `response.data.results`: Array of product result objects. Each object:
      * `product_id`: String (UUID).
      * `product_image`: Object with:
        * `id`: String (UUID).
        * `filename`: String.
        * `ip_category`: String.
      * `ground_truth_matches`: Array of ground truth IP image objects. Each GT object:
        * `ip_id`: String (UUID).
        * `ip_image_id`: String (UUID).
        * `ip_filename`: String.
        * `ip_owner`: String (nullable).
      * `suggestions`: Array of model suggestion objects. Each suggestion:
        * `ip_id`: String (UUID).
        * `ip_image_id`: String (UUID).
        * `ip_filename`: String.
        * `ip_owner`: String (nullable).
        * `normalized_similarity_score`: Number.
        * `is_ground_truth`: Boolean.
    * `response.data.page`: Number (current page).
    * `response.data.total_pages`: Number.
* **`getResultsByProduct(productImageId, params)`**
  * **Endpoint:** `GET /api/results/by-product/{product_image_id}`
  * **Request Parameters:**
    * `productImageId`: String (UUID, path parameter).
    * `params` (as an object):
      * `params.limit`: Number.
  * **Response Data Structure (Expected based on usage in `ByProductView.js`):**
    * `response.data`: Object with:
      * `product_image`: Object (details of the queried product image):
        * `id`: String (UUID).
        * `filename`: String.
        * `ip_category`: String.
      * `ground_truth_ip_images`: Array of ground truth IP image objects. Each GT object:
        * `id`: String (UUID).
        * `filename`: String.
        * `ip_owner`: String (nullable).
      * `results_by_model`: Object where keys are model names (String) and values are arrays of suggestion objects. Each suggestion:
        * `ip_image_id`: String (UUID).
        * `filename`: String.
        * `normalized_score`: Number.
        * `ip_owner`: String (nullable).
        * `is_ground_truth`: Boolean.
* **`addGroundTruth(productImageId, correctIpImageId)`**
  * **Endpoint:** `POST /api/data/ground_truth`
  * **Request Parameters (Request Body):**
    * `product_image_id`: String (UUID).
    * `correct_ip_image_id`: String (UUID).
  * **Response Data Structure (Expected based on usage in `ByModelView.js` and `ByProductView.js`):**
    * `response.data.detail`: String error message (on failure).
    * (Success is likely indicated by status code).
* **`removeGroundTruth(productImageId, correctIpImageId)`**
  * **Endpoint:** `DELETE /api/data/ground_truth`
  * **Request Parameters (Request Body, as `data` property for axios delete):**
    * `product_image_id`: String (UUID).
    * `correct_ip_image_id`: String (UUID).
  * **Response Data Structure (Expected based on usage in `ByModelView.js` and `ByProductView.js`):**
    * `response.data.detail`: String error message (on failure).
    * (Success is likely indicated by status code).
* **`getPerformanceSummary(ipCategory)`**
  * **Endpoint:** `GET /api/dashboard/performance-summary`
  * **Request Parameters:**
    * `ip_category`: String (query parameter).
  * **Response Data Structure (Expected based on usage in `MetricsDisplay.js`):**
    * `response.data`: Array of model performance summary objects. Each object:
      * `model_id`: String (UUID).
      * `model_name`: String.
      * `precision_avg_rank`: Number (nullable).
      * `avg_gt_score`: Number (nullable).
      * `avg_non_gt_score`: Number (nullable).
* **`getScoreDistribution(ipCategory, modelId)`** (Note: `api.js` has `modelId, ipCategory` but `MetricsDisplay.js` calls it with `ipCategory, modelId`)
  * **Endpoint:** `GET /api/dashboard/score-distribution`
  * **Request Parameters:**
    * `model_id`: String (UUID, query parameter).
    * `ip_category`: String (query parameter).
  * **Response Data Structure (Expected based on usage in `MetricsDisplay.js`):**
    * `response.data`: Object with:
      * `gt_scores`: Array of numbers.
      * `non_gt_scores`: Array of numbers.
* **`getConfusionMatrix(ipCategory, modelId, threshold)`** (Note: `api.js` has `modelId, ipCategory, threshold` but `MetricsDisplay.js` calls it with `ipCategory, modelId, threshold`)
  * **Endpoint:** `GET /api/dashboard/confusion-matrix`
  * **Request Parameters:**
    * `model_id`: String (UUID, query parameter).
    * `ip_category`: String (query parameter).
    * `threshold`: Number (query parameter).
  * **Response Data Structure (Expected based on usage in `MetricsDisplay.js`):**
    * `response.data`: Object with:
      * `tp`: Number (True Positives).
      * `fn`: Number (False Negatives).
      * `fp`: Number (False Positives).
      * `tn`: Number (True Negatives).
* **`getQdrantCollections()`**
  * **Endpoint:** `GET /api/qdrant/collections`
  * **Request Parameters:** None.
  * **Response Data Structure (Expected based on usage in `CollectionManagement.js`):**
    * `response.data`: Array of Qdrant collection objects. Each object:
      * `name`: String.
      * `ip_category`: String (nullable).
      * `model_name`: String (nullable).
      * `is_active`: Boolean.
      * `needs_migration`: Boolean.
* **`deleteQdrantCollection(collectionName)`**
  * **Endpoint:** `DELETE /api/qdrant/collections/{collection_name}`
  * **Request Parameters:**
    * `collectionName`: String (path parameter, URL encoded).
  * **Response Data Structure (Expected based on usage in `CollectionManagement.js`):**
    * `response.data`: Potentially a success message or status.
    * (Success also indicated by status code).
* **`getTaskStatus(taskId)`**
  * **Endpoint:** `GET /api/tasks/status/{task_id}`
  * **Request Parameters:**
    * `taskId`: String (UUID, path parameter).
  * **Response Data Structure (Expected based on usage in `FeatureComputation.js`):**
    * `response.data`: Object with:
      * `task_id`: String (UUID).
      * `status`: String (e.g., "PENDING", "PROGRESS", "SUCCESS", "FAILURE").
      * `progress`: Number (0-100, nullable).
      * `detail`: String (message or error detail, nullable).

---

### `frontend/src/pages/CopyrightPage.js`

* This page acts as a container for different views related to copyright results.
* It uses a Tab structure to switch between "By Model" and "By Product Picture" views.
* **API Calls:** None directly. It delegates API interactions to child components.
* **Child Components and Data Flow:**
  * `<ByModelView ipCategory="copyright" />`: Renders the view for results grouped by model for the "copyright" IP category.
  * `<ByProductView ipCategory="copyright" />`: Renders the view for results specific to a product image for the "copyright" IP category.
* **Data Utilization:** Does not directly utilize API data; passes `ipCategory` to children.

---

### `frontend/src/components/results/ByModelView.js`

* **API Calls:**
  * `listModels()`: Called on component mount (and when `ipCategory` changes) to fetch all models.
    * **Parameters:** None explicitly passed, so it fetches all models.
    * **Client-side Filtering:** Filters models based on `m.is_active` and if `m.applicable_ip_category` includes the current `ipCategory` prop or "all".
  * `getCombinedScores({ ip_category: ipCategory, is_active: true })`: Called on component mount (and when `ipCategory` changes) to fetch active combined score configurations for the given `ipCategory`.
    * **Parameters:** `ip_category` (from props), `is_active: true`.
  * `getResultsByModel(params)`: Called when `selectedModelId`, `ipCategory`, or `pagination.page` changes.
    * **Parameters:**
      * `model_id`: `selectedModelId` (from state).
      * `ip_category`: `ipCategory` (from props).
      * `page`: `pagination.page` (from state).
      * `per_page`: `RESULTS_PER_PAGE` (constant, 10).
  * `addGroundTruth(productId, ipId)`: Called when a user marks a suggestion as ground truth.
    * **Parameters:** `productId` (of the main product), `ipId` (of the suggested IP image).
  * `removeGroundTruth(productId, ipId)`: Called when a user unmarks a suggestion as ground truth.
    * **Parameters:** `productId`, `ipId`.
* **Data Utilization:**
  * **From `listModels` and `getCombinedScores`:**
    * Populates the "Select Model" dropdown.
    * `model.model_id` / `combined.config_id` as value.
    * `model.model_name` / `combined.config_name` as display text.
    * Stores a combined list in `models` state, each with an `id`, `name`, and `type` ('model' or 'combined').
  * **From `getResultsByModel` (`response.data`):**
    * `results`: Array used to render product result cards.
      * `productResult.product_id`: Used as key and for `handleGroundTruthToggle`.
      * `productResult.product_image.id`: For constructing image URL via `getImageUrl()`, for `alt` text.
      * `productResult.product_image.filename`: For `alt` text and display.
      * `productResult.product_image.ip_category`: For display.
      * `productResult.ground_truth_matches`: Array, mapped to display known correct match images.
        * `gt.ip_id`: Used as key.
        * `gt.ip_image_id`: For image URL.
        * `gt.ip_filename`: For tooltip and display.
        * `gt.ip_owner`: For tooltip.
      * `productResult.suggestions`: Array, mapped to display model suggestion images.
        * `suggestion.ip_id`: Used as key and for `handleGroundTruthToggle`.
        * `suggestion.ip_image_id`: For image URL.
        * `suggestion.ip_filename`: For tooltip and display.
        * `suggestion.normalized_similarity_score`: For display (formatted to 4 decimal places).
        * `suggestion.ip_owner`: For tooltip and display.
        * `suggestion.is_ground_truth`: To style the suggestion card border and determine icon for ground truth toggle.
    * `page`: Sets `pagination.page`.
    * `total_pages`: Sets `pagination.totalPages` for the Pagination component.
  * **Image URLs:** Constructed as `/api/data/images/file/{imageId}`.
* **Assumptions/Notes:**
  * Assumes `product_image` object exists with `id`, `filename`, and `ip_category`.
  * Assumes `ground_truth_matches` array contains objects with `ip_id`, `ip_image_id`, `ip_filename`, `ip_owner`.
  * Assumes `suggestions` array contains objects with `ip_id`, `ip_image_id`, `ip_filename`, `normalized_similarity_score`, `ip_owner`, and `is_ground_truth`.
  * Client-side filtering of models implies that `listModels` might return models not applicable to the current `ipCategory`.

---

### `frontend/src/components/results/ByProductView.js`

* **API Calls:**
  * `listImages(params)`: Called on component mount (and when `ipCategory` changes) to fetch product images for selection.
    * **Parameters:** `image_type: 'product'`. (Comment indicates `ip_category` is not used for filtering here).
  * `getResultsByProduct(selectedProduct.id, params)`: Called when `selectedProduct` changes.
    * **Parameters:**
      * `selectedProduct.id`: ID of the product image selected from the dropdown.
      * `params`: `{ limit: 3 }` (hardcoded, could be configurable).
  * `addGroundTruth(selectedProduct.id, ipImageId)`: Called to mark a suggestion as ground truth.
    * **Parameters:** `selectedProduct.id`, `ipImageId` (of the suggestion).
  * `removeGroundTruth(selectedProduct.id, ipImageId)`: Called to unmark a suggestion.
    * **Parameters:** `selectedProduct.id`, `ipImageId`.
* **Data Utilization:**
  * **From `listImages` (`response.data.images`):**
    * Populates the product image selection Autocomplete.
    * Each image is mapped to an object with `id` (from `image_id`), `filename` (from `original_filename`), `label` (from `original_filename`), `imageUrl` (constructed as `/api/data/images/file/{image_id}`), and `ip_category`.
  * **From `getResultsByProduct` (`response.data`):**
    * Stored in `results` state.
    * `results.product_image`: (Not directly used from this field, `selectedProduct` state already holds this info).
    * `results.ground_truth_ip_images`: Array, mapped to display known correct match images.
      * `gtImage.id`: For image URL and key.
      * `gtImage.filename`: For tooltip and alt text.
    * `results.results_by_model`: Object, iterated by model name.
      * For each `modelName` and its `suggestions` array:
        * `suggestion.ip_image_id`: For image URL and key.
        * `suggestion.filename`: For display and alt text.
        * `suggestion.normalized_score`: For display (formatted to 4 decimal places).
        * `suggestion.ip_owner`: For display.
        * `suggestion.is_ground_truth`: To show a checkmark icon and determine toggle action.
  * **Image URLs:** Base URL `/api/data/images/` is used, and image IDs are appended (e.g., `/api/data/images/{image_id}`). Note: `api.js` uses `/api/data/images/file/{image_id}` for `getImageFile`. This component seems to construct URLs like `/api/data/images/{image_id}` directly. This might be a discrepancy or an alternative endpoint for direct image serving.  *Correction* : The component uses `imageBaseUrl = '/api/data/images/'` and then constructs URLs like `${imageBaseUrl}${suggestion.ip_image_id}`. This implies the backend serves images directly from `/api/data/images/{image_id}` without `/file/`.
* **Assumptions/Notes:**
  * Assumes `listImages` with `image_type: 'product'` returns images with `image_id`, `original_filename`, and `ip_category`.
  * Assumes `getResultsByProduct` response structure as detailed above.
  * The limit for `getResultsByProduct` is hardcoded to 3.
  * Image URLs are constructed differently than in `ByModelView.js` (directly appending ID vs. using `/file/` path). This needs verification against backend API.

---

### `frontend/src/pages/TrademarkPage.js`

* Structurally identical to `CopyrightPage.js`.
* **API Calls:** None directly.
* **Child Components and Data Flow:**
  * `<ByModelView ipCategory="trademark" />`
  * `<ByProductView ipCategory="trademark" />`
* **Data Utilization:** Passes `ipCategory="trademark"` to children. API interactions and data handling are as described for `ByModelView.js` and `ByProductView.js`, but with "trademark" as the category.

---

### `frontend/src/pages/PatentPage.js`

* Structurally identical to `CopyrightPage.js`.
* **API Calls:** None directly.
* **Child Components and Data Flow:**
  * `<ByModelView ipCategory="patent" />`
  * `<ByProductView ipCategory="patent" />`
* **Data Utilization:** Passes `ipCategory="patent"` to children. API interactions and data handling are as described for `ByModelView.js` and `ByProductView.js`, but with "patent" as the category.

---

### `frontend/src/pages/DashboardPage.js`

* Container for displaying metrics for different IP categories using tabs.
* **API Calls:** None directly.
* **Child Components and Data Flow:**
  * `<MetricsDisplay ipCategory="trademark" />`
  * `<MetricsDisplay ipCategory="copyright" />`
  * `<MetricsDisplay ipCategory="patent" />`
* **Data Utilization:** Passes the respective `ipCategory` to the `MetricsDisplay` component.

---

### `frontend/src/components/dashboard/MetricsDisplay.js`

* **API Calls:**
  * `getPerformanceSummary(ipCategory)`: Called on mount and when `ipCategory` changes.
    * **Parameters:** `ipCategory` (from props).
  * `getScoreDistribution(ipCategory, selectedModel.model_id)`: Called when `selectedModel` or `ipCategory` changes.
    * **Parameters:** `ipCategory` (from props), `selectedModel.model_id` (from state).
  * `getConfusionMatrix(ipCategory, selectedModel.model_id, currentThreshold)`: Called when `selectedModel`, `threshold`, or `ipCategory` changes (via `fetchMatrix` callback).
    * **Parameters:** `ipCategory` (from props), `selectedModel.model_id` (from state), `currentThreshold` (from state, defaults to 0.5).
* **Data Utilization:**
  * **From `getPerformanceSummary` (`response.data`):**
    * Populates the "Model Performance Summary" table. Data is sorted by `precision_avg_rank`.
    * `model.model_id`: Used as key and to identify selected model.
    * `model.model_name`: Displayed in table.
    * `model.precision_avg_rank`: Displayed (formatted to 2 decimal places).
    * `model.avg_gt_score`: Displayed (formatted to 4 decimal places).
    * `model.avg_non_gt_score`: Displayed (formatted to 4 decimal places).
  * **From `getScoreDistribution` (`response.data`):**
    * `gt_scores`: Array of numbers, used for "Ground Truth Matches" histogram in Plotly.
    * `non_gt_scores`: Array of numbers, used for "Non-Matches" histogram in Plotly.
  * **From `getConfusionMatrix` (`response.data`):**
    * `tp`, `fn`, `fp`, `tn`: Numbers, displayed in the confusion matrix table.
* **Assumptions/Notes:**
  * Assumes `getPerformanceSummary` returns an array of objects with the fields listed.
  * Assumes `getScoreDistribution` returns an object with `gt_scores` and `non_gt_scores` arrays.
  * Assumes `getConfusionMatrix` returns an object with `tp`, `fn`, `fp`, `tn`.
  * The order of parameters for `getScoreDistribution` and `getConfusionMatrix` in `MetricsDisplay.js` calls (`ipCategory` first) differs from their definition in `api.js` (`modelId` first). This implies the `api.js` functions correctly map these to the backend query parameters.

---

### `frontend/src/pages/SettingsPage.js`

* Container for various settings components, managed via tabs.
* **API Calls:** None directly.
* **Child Components and Data Flow:**
  * Tab 0 ("Data Management"):
    * `<ImageUpload />`
    * `<ImageBrowser />`
  * Tab 1 ("Model Management"):
    * `<ModelManagement />`
  * Tab 2 ("Combined Scores"):
    * `<CombinedScoresConfig />`
  * Tab 3 ("Feature Computation"):
    * `<FeatureComputation />`
  * Tab 4 ("Qdrant Management"):
    * `<CollectionManagement />`
* **Data Utilization:** Does not directly utilize API data; renders child components based on active tab.

---

### `frontend/src/components/settings/ImageUpload.js`

* **API Calls:**
  * `uploadImages(formData)`: Called on form submission.
    * **Parameters:** `formData` object containing:
      * `files[]`: Array of selected files.
      * `ip_category`: Selected IP category (from state).
      * `image_type`: Selected image type (from state, converted to lowercase).
* **Data Utilization:**
  * **From `uploadImages` (`response.data`):**
    * `success_count`: Used if present, otherwise defaults to `selectedFiles.length`.
    * `message`: Displayed as success feedback.
    * `detail`: Displayed as error feedback (if `error.response.data.detail` exists).
  * Displays selected file names and sizes before upload.
* **Assumptions/Notes:**
  * Expects `uploadImages` to return `success_count` and `message` on success, or `detail` in error response.
  * The key for files in FormData is `files[]`.

---

### `frontend/src/components/settings/ImageBrowser.js`

* **API Calls:**
  * `listImages(params)`: Called on mount and when `refreshTrigger` changes.
    * **Parameters:** `params` is an empty object (fetches all images).
  * `updateImageIpOwner(editingImage.id, newIpOwner.trim())`: Called when saving changes in the edit IP owner dialog.
    * **Parameters:** `editingImage.id` (ID of the image being edited), `newIpOwner` (trimmed string from input).
  * `deleteImage(deletingImageId)`: Called when confirming image deletion.
    * **Parameters:** `deletingImageId` (ID of the image to delete).
* **Data Utilization:**
  * **From `listImages` (`response.data.images`):**
    * Populates the image table.
    * `image.id`: Used as key, for constructing thumbnail URL, and for edit/delete actions.
    * `image.filename`: Displayed.
    * `image.ip_category` (or `image.category` as fallback): Displayed.
    * `image.ip_owner`: Displayed; if missing, shows "(Missing)".
    * `image.uploaded_at`: Displayed (formatted).
    * Thumbnail URL constructed as `/api/data/images/{image.id}/thumbnail`.
  * **From `updateImageIpOwner` / `deleteImage`:**
    * Error messages (`err.response.data.detail`) are displayed in dialogs or as general error.
    * Success triggers a refresh of the image list.
* **Assumptions/Notes:**
  * Assumes `listImages` returns an array of image objects with `id`, `filename`, `ip_category`/`category`, `ip_owner`, and `uploaded_at`.
  * Assumes a thumbnail endpoint exists at `/api/data/images/{image_id}/thumbnail`.
  * Error details are expected in `err.response.data.detail`.

---

### `frontend/src/components/settings/ModelManagement.js`

* **API Calls:**
  * `listModels()`: Called on mount and after a successful model refresh.
    * **Parameters:** None.
  * `refreshModels()`: Called when the "Refresh Model List" button is clicked.
    * **Parameters:** None.
* **Data Utilization:**
  * **From `listModels` (`response.data`):**
    * Populates the models table.
    * `model.model_id`: Used as key.
    * `model.model_name`: Displayed.
    * `model.model_type`: Displayed.
    * `model.applicable_ip_category`: Array of strings, displayed as Chips.
    * `model.description`: Displayed.
    * `model.is_active`: Displayed as an "Active" or "Inactive" Chip.
  * **From `refreshModels` (`response.data`):**
    * `message`: Displayed in a Snackbar on success.
    * `detail`: Displayed as an error (persistent or in Snackbar) on failure.
* **Assumptions/Notes:**
  * Assumes `listModels` returns an array of model objects with the fields listed.
  * Assumes `refreshModels` returns a `message` on success or `detail` on error.

---

### `frontend/src/components/settings/CombinedScoresConfig.js`

* **API Calls:**
  * `getCombinedScores()`: Called on mount and when `refreshTrigger` changes.
    * **Parameters:** None (fetches all configurations).
  * `listModels()`: Called on mount, when opening create/edit dialog (if models not yet loaded).
    * **Parameters:** None.
  * `createCombinedScoreConfig(configData)`: Called when submitting the "Create New Configuration" dialog.
    * **Parameters:** `configData` object:
      * `name`: String.
      * `ip_category`: String.
      * `model_weights`: Array of `{ model_name: String, weight: Number }`.
  * `updateCombinedScoreConfig(currentConfig.config_id, configData)`: Called when submitting the "Edit Configuration" dialog.
    * **Parameters:** `currentConfig.config_id`, `configData` (same structure as for create).
  * `deleteCombinedScoreConfig(deletingConfigId)`: Called when confirming deletion.
    * **Parameters:** `deletingConfigId`.
* **Data Utilization:**
  * **From `getCombinedScores` (`response.data`):**
    * Populates the configurations table.
    * `config.id` or `config.config_id`: Used as key and for edit/delete actions.
    * `config.name`: Displayed.
    * `config.ip_category`: Displayed.
    * `config.model_weights`: Array, displayed as Chips (`model_name: weight`).
  * **From `listModels` (`response.data`):**
    * Populates `models` state, used in the create/edit dialog to select models for weighting.
    * Filters models based on `model.active` and if `model.applicable_categories` includes the `configCategory` selected in the dialog.
    * `model.name` (likely `model_name` from API): Used for display and as key in `modelWeights` state.
    * `model.type` (likely `model_type` from API): Displayed as secondary text.
  * **From create/update/delete calls:**
    * Error messages (`err.response.data.detail`) are displayed in dialogs or Snackbar.
    * Success messages are shown in Snackbar, and the config list is refreshed.
* **Assumptions/Notes:**
  * Assumes `getCombinedScores` returns configs with `id`/`config_id`, `name`, `ip_category`, and `model_weights` (array of `{model_name, weight}`).
  * Assumes `listModels` returns models with `name`/`model_name`, `type`/`model_type`, `active`/`is_active`, and `applicable_categories`/`applicable_ip_category`.
  * The dialog converts `model_weights` between the array format from backend and an object format (`{ model_name: weight }`) for state management.
  * Only models with weight > 0 are included in the `model_weights` array sent to the backend.

---

### `frontend/src/components/settings/FeatureComputation.js`

* **API Calls:**
  * `computeFeatures(category)`: Called when a "Recompute Features" button is clicked for a specific IP category.
    * **Parameters:** `category` (String, e.g., "trademark", "copyright", "patent").
  * `getTaskStatus(taskId)`: Polled at intervals after `computeFeatures` returns a `task_id`.
    * **Parameters:** `taskId` (String, from `computeFeatures` response).
* **Data Utilization:**
  * **From `computeFeatures` (`response.data`):**
    * `task_id`: Used to start polling for status updates.
    * `detail`: If an error occurs on task start, this is displayed.
  * **From `getTaskStatus` (`response.data`):**
    * `status`: String (e.g., "PENDING", "PROGRESS", "SUCCESS", "FAILURE"). Used to update UI display (Chip label, color).
    * `progress`: Number (0-100). Used for LinearProgress bar if status is "PROGRESS".
    * `detail`: String. If status is "PROGRESS", prepended to "Computing: ". If status is "FAILURE", used as error message.
* **Assumptions/Notes:**
  * `computeFeatures` is expected to return a `task_id`.
  * `getTaskStatus` is expected to return `status`, `progress`, and `detail`.
  * Polling interval is 5 seconds.
  * The component manages separate loading and task states for each IP category.

---

### `frontend/src/components/settings/CollectionManagement.js`

* **API Calls:**
  * `getQdrantCollections()`: Called on component mount and after a successful deletion.
    * **Parameters:** None.
  * `deleteQdrantCollection(collectionName)`: Called when a delete button is clicked and confirmed.
    * **Parameters:** `collectionName` (String).
* **Data Utilization:**
  * **From `getQdrantCollections` (`data` which is `response.data` from api.js):**
    * Populates the collections table.
    * `collection.name`: Displayed, used as key, and passed to `handleDelete`.
    * `collection.ip_category`: Displayed.
    * `collection.model_name`: Displayed.
    * `collection.is_active`: Used to display "Active" or "Orphaned" Chip.
    * `collection.needs_migration`: If true, displays "(Needs Migration)" text.
  * **From `deleteQdrantCollection`:**
    * Error messages (`err.response.data.detail` or `err.message`) are set to `error` state and shown in an alert (TODO suggests Snackbar).
    * Success triggers a re-fetch of collections and an alert (TODO suggests Snackbar).
* **Assumptions/Notes:**
  * Assumes `getQdrantCollections` returns an array of collection objects with `name`, `ip_category`, `model_name`, `is_active`, and `needs_migration`.
  * Deletion is confirmed via `window.confirm`.
