import React, { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Typography,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Box,
} from '@mui/material';
import { getPatentDashboardStatistics } from '../../services/api_patent_viz';
import StatisticsSection from '../../components/patent-viz/StatisticsSection'; // Will be created next

// Helper function (can be moved to a utils file)
const formatTimestamp = (isoString) => {
  if (!isoString) return 'N/A';
  try {
    return new Date(isoString).toLocaleString();
  } catch (e) {
    return 'Invalid Date';
  }
};

// TDD: TEST: PatentDashboardPage fetches data on mount
// TDD: TEST: PatentDashboardPage displays loading state while fetching
// TDD: TEST: PatentDashboardPage displays error message on fetch failure
// TDD: TEST: PatentDashboardPage renders StatisticsSection for overall and TRO-specific data
// TDD: TEST: PatentDashboardPage Refresh button calls API with refresh=true
const PatentDashboardPage = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchDashboardData = useCallback(async (refresh = false) => {
    setIsLoading(true);
    setError(null);
    try {
      // TDD: TEST: API_CLIENT.get is called with correct URL and refresh param (covered by getPatentDashboardStatistics)
      const response = await getPatentDashboardStatistics(refresh);
      setDashboardData(response.data);
    } catch (apiError) {
      setError(apiError.message || 'Failed to fetch dashboard data.');
      console.error("Error fetching dashboard data:", apiError);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchDashboardData(false);
  }, [fetchDashboardData]);

  const handleRefreshClick = () => {
    fetchDashboardData(true);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom component="h1">
        Patent Statistics Dashboard
      </Typography>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="caption">
          {dashboardData && dashboardData.last_updated_timestamp
            ? `Last Updated: ${formatTimestamp(dashboardData.last_updated_timestamp)} (Cache: ${dashboardData.cache_status || 'N/A'})`
            : 'Loading update time...'}
        </Typography>
        <Button variant="contained" onClick={handleRefreshClick} disabled={isLoading}>
          {isLoading ? 'Refreshing...' : 'Refresh Data'}
        </Button>
      </Box>

      {isLoading && !dashboardData && ( // Show main loader only if no data yet
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>
      )}

      {dashboardData && !isLoading && !error && ( // Render sections if data is available, not loading, and no error
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="h5" gutterBottom component="h2">
              Overall Database Statistics
            </Typography>
            {/* TDD: TEST: StatisticsSection for overall_statistics receives correct props */}
            <StatisticsSection
              statsData={dashboardData.overall_statistics}
            />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h5" gutterBottom component="h2">
              TRO-Specific Statistics (TRO = True)
            </Typography>
            {/* TDD: TEST: StatisticsSection for tro_specific_statistics receives correct props */}
            <StatisticsSection
              statsData={dashboardData.tro_specific_statistics}
            />
          </Grid>
        </Grid>
      )}
    </Container>
  );
};

export default PatentDashboardPage;