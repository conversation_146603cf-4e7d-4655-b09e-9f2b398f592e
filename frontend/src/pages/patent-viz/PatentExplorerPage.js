import React, { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Typography,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  getPatentsForExploration,
  getPatentDetails,
  getPatentImagesInfo,
} from '../../services/api_patent_viz';
import { DayPicker } from "react-day-picker"; // Added
import "react-day-picker/style.css"; // Added

// Import sub-components
import FilterControls from '../../components/patent-viz/FilterControls';
import QuickStatsDisplay from '../../components/patent-viz/QuickStatsDisplay';
import PatentTable from '../../components/patent-viz/PatentTable';
import PaginationControls from '../../components/patent-viz/PaginationControls';
import PatentDetailModal from '../../components/patent-viz/PatentDetailModal';
import PatentImageModal from '../../components/patent-viz/PatentImageModal';

// TDD: TEST: PatentExplorerPage fetches patent list on mount and when filters change
// TDD: TEST: PatentExplorerPage displays loading/error states for patent list
// TDD: TEST: PatentExplorerPage renders FilterControls and PatentTable
// TDD: TEST: PatentExplorerPage handles pagination and sorting correctly
// TDD: TEST: PatentExplorerPage opens/closes PatentDetailModal and fetches data
// TDD: TEST: PatentExplorerPage opens/closes PatentImageModal and fetches image paths

const INITIAL_FILTERS = {
  document_id_search: "",
  patent_title_search: "",
  abstract_search: "",
  date_published_range: { from: null, to: null }, // Changed for react-day-picker
  patent_types: [],
  tro_status: "All",
  inventors_search: "",
  assignee_search: "",
  applicant_search: "",
  uspc_class_search: "",
  loc_code_search: "",
  cpc_class_search: "",
  page: 1,
  per_page: 25,
  sort_by: "date_published",
  sort_dir: "desc",
  selected_columns: ["document_id", "patent_title", "date_published", "patent_type", "tro"], // Default visible
};

// Define based on patents_records table from schema
const ALL_AVAILABLE_COLUMNS = [
  { field: "id", headerName: "ID", alwaysVisible: false, defaultHidden: true }, // Internal ID, usually not shown
  { field: "document_id", headerName: "Document ID", alwaysVisible: true },
  { field: "patent_title", headerName: "Patent Title", alwaysVisible: true },
  { field: "date_published", headerName: "Date Published" },
  { field: "patent_type", headerName: "Patent Type" },
  { field: "tro", headerName: "TRO" },
  { field: "abstract", headerName: "Abstract" },
  { field: "inventors", headerName: "Inventors" }, // Array, might need formatting
  { field: "assignee", headerName: "Assignee" },
  { field: "applicant", headerName: "Applicant" },
  { field: "uspc_class", headerName: "USPC Class" }, // Array, might need formatting
  { field: "loc_code", headerName: "LOC Code" }, // Array, might need formatting
  { field: "cpc_class", headerName: "CPC Class" }, // Array, might need formatting
  { field: "reg_no", headerName: "Registration No.", defaultHidden: true },
  { field: "application_number", headerName: "Application No.", defaultHidden: true },
  { field: "date_filed", headerName: "Date Filed", defaultHidden: true },
  { field: "claims_count", headerName: "Claims Count", defaultHidden: true },
  { field: "independent_claims_count", headerName: "Independent Claims", defaultHidden: true },
  // Add other fields as needed, e.g., foreign_priority, related_applications
];


function PatentExplorerPage() {
  const [filters, setFilters] = useState(INITIAL_FILTERS);
  const [patentsData, setPatentsData] = useState({ patents: [], pagination: {}, quick_stats: {} });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedPatentForDetail, setSelectedPatentForDetail] = useState(null);
  const [isDetailLoading, setIsDetailLoading] = useState(false);
  const [detailError, setDetailError] = useState(null);

  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [selectedPatentForImages, setSelectedPatentForImages] = useState(null);
  const [imagePathsData, setImagePathsData] = useState({ image_paths: [], patent_reg_no: "", base_image_folder_path: "" });
  const [isImagesLoading, setIsImagesLoading] = useState(false);
  const [imagesError, setImagesError] = useState(null);

  const [availableColumns] = useState(ALL_AVAILABLE_COLUMNS);

  const fetchPatents = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // TDD: TEST: API_CLIENT.get for patent list is called with correct filter params
      const apiFilters = {
        ...filters,
        date_published_start: filters.date_published_range?.from,
        date_published_end: filters.date_published_range?.to,
      };
      delete apiFilters.date_published_range; // Clean up the temporary range object

      const response = await getPatentsForExploration(apiFilters);
      setPatentsData(response.data);
    } catch (apiError) {
      setError(apiError.message || 'Failed to fetch patents');
      setPatentsData({ patents: [], pagination: {}, quick_stats: {} }); // Reset data on error
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchPatents();
  }, [fetchPatents]);

  const handleFilterChange = useCallback((newFilterValues) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      ...newFilterValues,
      page: 1, // Reset to page 1 on filter change
    }));
  }, []);

  const handlePageChange = useCallback((newPage) => {
    setFilters((prevFilters) => ({ ...prevFilters, page: newPage }));
  }, []);

  const handlePerPageChange = useCallback((newPerPage) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      per_page: newPerPage,
      page: 1, // Reset to page 1
    }));
  }, []);

  const handleSortChange = useCallback((sortByField, sortDirection) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      sort_by: sortByField,
      sort_dir: sortDirection,
    }));
  }, []);

  const handleColumnSelectionChange = useCallback((newlySelectedColumnsFieldsArray) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      selected_columns: newlySelectedColumnsFieldsArray,
    }));
  }, []);

  const openDetailModal = useCallback(async (patentIdForDetail) => {
    setIsDetailModalOpen(true);
    setIsDetailLoading(true);
    setDetailError(null);
    setSelectedPatentForDetail(null); // Clear previous data
    try {
      // TDD: TEST: API_CLIENT.get for patent detail is called with correct patent_id
      const response = await getPatentDetails(patentIdForDetail);
      setSelectedPatentForDetail(response.data);
    } catch (apiError) {
      setDetailError(apiError.message || 'Failed to fetch patent details');
    } finally {
      setIsDetailLoading(false);
    }
  }, []);

  const closeDetailModal = useCallback(() => {
    setIsDetailModalOpen(false);
    setSelectedPatentForDetail(null);
  }, []);

  const openImageModal = useCallback(async (patentForImages) => { // patentForImages is {id, reg_no, patent_title}
    setSelectedPatentForImages(patentForImages);
    setIsImageModalOpen(true);
    setIsImagesLoading(true);
    setImagesError(null);
    setImagePathsData({ image_paths: [], patent_reg_no: "", base_image_folder_path: "" }); // Clear previous
    try {
      // TDD: TEST: API_CLIENT.get for patent images is called with correct patent_id
      const response = await getPatentImagesInfo(patentForImages.id);
      setImagePathsData(response.data);
    } catch (apiError) {
      setImagesError(apiError.message || 'Failed to fetch patent images');
    } finally {
      setIsImagesLoading(false);
    }
  }, []);

  const closeImageModal = useCallback(() => {
    setIsImageModalOpen(false);
    setSelectedPatentForImages(null);
  }, []);
  
  const displayedColumnsConfig = React.useMemo(() => {
    return filters.selected_columns
      .map(field => availableColumns.find(col => col.field === field))
      .filter(Boolean); // Ensure only valid columns are passed
  }, [filters.selected_columns, availableColumns]);

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Patent Explorer
      </Typography>

      {/* TDD: TEST: FilterControls receives current_filters and callbacks */}
      <FilterControls
        current_filters={filters}
        on_filter_change={handleFilterChange}
        available_columns={availableColumns}
        on_column_selection_change={handleColumnSelectionChange}
      />

      {/* TDD: TEST: QuickStatsDisplay receives correct stats */}
      {patentsData.quick_stats && patentsData.quick_stats.total_filtered_results != null && (
        <QuickStatsDisplay stats={patentsData.quick_stats} />
      )}

      {isLoading ? (
        <CircularProgress sx={{ display: 'block', margin: '20px auto' }} />
      ) : error ? (
        <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>
      ) : patentsData.patents && patentsData.patents.length > 0 ? (
        <>
          {/* TDD: TEST: PatentTable receives patents and callbacks */}
          <PatentTable
            patents={patentsData.patents}
            displayed_columns={displayedColumnsConfig}
            on_row_id_click={openDetailModal}
            on_row_title_click={openImageModal}
            current_sort_by={filters.sort_by}
            current_sort_dir={filters.sort_dir}
            on_sort_change={handleSortChange}
          />
          {/* TDD: TEST: PaginationControls receives correct props and callbacks */}
          <PaginationControls
            current_page={filters.page}
            items_per_page={filters.per_page}
            total_items={patentsData.pagination.total_items || 0}
            on_page_change={handlePageChange}
            on_per_page_change={handlePerPageChange}
          />
        </>
      ) : (
        <Alert severity="info" sx={{ mt: 2 }}>No patents found matching your criteria.</Alert>
      )}

      {/* TDD: TEST: PatentDetailModal receives correct props */}
      <PatentDetailModal
        is_open={isDetailModalOpen}
        on_close={closeDetailModal}
        patent_data={selectedPatentForDetail}
        is_loading={isDetailLoading}
        error={detailError}
      />

      {/* TDD: TEST: PatentImageModal receives correct props */}
      <PatentImageModal
        is_open={isImageModalOpen}
        on_close={closeImageModal}
        patent_title={selectedPatentForImages ? selectedPatentForImages.patent_title : ""}
        image_data={imagePathsData}
        is_loading={isImagesLoading}
        error={imagesError}
      />
    </Container>
  );
}

export default PatentExplorerPage;