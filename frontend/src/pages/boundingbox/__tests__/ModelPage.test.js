import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom'; // ModelPage itself doesn't use routing components, but good practice if it might in future or if a Layout context is involved.
import ModelPage from '../ModelPage';

// Mock the API service used by ModelPage
jest.mock('../../services/api_bounding_box', () => ({
  getBbModels: jest.fn(() => Promise.resolve([])), // Resolve with empty array
  updateBbModel: jest.fn(() => Promise.resolve({})),
}));

describe('ModelPage', () => {
  test('renders the page title', () => {
    render(
      <MemoryRouter>
        <ModelPage />
      </MemoryRouter>
    );
    expect(screen.getByText(/Model Management \(Bounding Box\)/i)).toBeInTheDocument();
  });

  test('renders table headers', () => {
    render(
      <MemoryRouter>
        <ModelPage />
      </MemoryRouter>
    );
    expect(screen.getByText(/Model Name/i)).toBeInTheDocument();
    expect(screen.getByText(/Description/i)).toBeInTheDocument();
    expect(screen.getByText(/GCP Model Name/i)).toBeInTheDocument();
    expect(screen.getByText(/Active/i)).toBeInTheDocument();
  });
});
