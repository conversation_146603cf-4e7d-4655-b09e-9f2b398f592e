import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import ResultsPage from '../ResultsPage';

// Mock the API service used by ResultsPage
jest.mock('../../services/api_bounding_box', () => ({
  getBbPictures: jest.fn(() => Promise.resolve([])), // Resolve with empty array to prevent further processing
  getBbExperiments: jest.fn(() => Promise.resolve({ experiments: [], totalPages: 0 })),
  getBbExperimentConfigurations: jest.fn(() => Promise.resolve([])),
  createBbExperiment: jest.fn(() => Promise.resolve({})),
  updateBbResultScore: jest.fn(() => Promise.resolve({})),
}));

describe('ResultsPage', () => {
  test('renders the page title', () => {
    render(
      <MemoryRouter>
        <ResultsPage />
      </MemoryRouter>
    );
    expect(screen.getByText(/Results Analysis by Product Pictures/i)).toBeInTheDocument();
  });

  test('renders Select Product Image label', () => {
    render(
      <MemoryRouter>
        <ResultsPage />
      </MemoryRouter>
    );
    expect(screen.getByLabelText(/Select Product Image/i)).toBeInTheDocument();
  });

  test('renders New Experiment button', () => {
    render(
      <MemoryRouter>
        <ResultsPage />
      </MemoryRouter>
    );
    // The button might be initially disabled, but it should be present
    expect(screen.getByRole('button', { name: /\+ New Experiment/i })).toBeInTheDocument();
  });
});
