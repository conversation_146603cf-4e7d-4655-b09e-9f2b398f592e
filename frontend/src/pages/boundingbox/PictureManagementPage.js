import React, { useState, useEffect, useCallback } from 'react';
import {
  Container, Typo<PERSON>, Box, Button, CircularProgress, Alert,
  List, ListItem, ListItemText, Paper, Input, TextField,
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow,
  IconButton, Tooltip, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle,
  Pagination, Grid
} from '@mui/material';
import { uploadBbPicture, getBbPictures, updateBbPictureName, deleteBbPicture, scanLocalImages } from '../../services/api_bounding_box';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import PhotoCameraBackIcon from '@mui/icons-material/PhotoCameraBack'; // Placeholder for thumbnail

const ACCEPTED_FILE_TYPES = ['.jpg', '.jpeg', '.png'];
const ACCEPTED_FILE_TYPES_STRING = ACCEPTED_FILE_TYPES.join(', ');
const ITEMS_PER_PAGE = 5; // For image browser pagination

const BoundingBoxPictureManagementPage = () => {
  // Upload states
  const [selectedFile, setSelectedFile] = useState(null);
  const [filePreview, setFilePreview] = useState(null);
  const [uploadError, setUploadError] = useState('');
  const [uploadSuccess, setUploadSuccess] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  // Image Browser states
  const [picturesList, setPicturesList] = useState([]);
  const [isLoadingPictures, setIsLoadingPictures] = useState(false);
  const [picturesError, setPicturesError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalPictures, setTotalPictures] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');

  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Edit states
  const [editPictureId, setEditPictureId] = useState(null);
  const [editPictureName, setEditPictureName] = useState('');
  const [isRenameModalOpen, setIsRenameModalOpen] = useState(false);
  const [renameError, setRenameError] = useState('');

  // Delete states
  const [pictureToDeleteId, setPictureToDeleteId] = useState(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);

  // Scan local images states
  const [isScanningLocal, setIsScanningLocal] = useState(false);
  const [scanLocalError, setScanLocalError] = useState('');
  const [scanLocalSuccess, setScanLocalSuccess] = useState('');


  // Debounce search term
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // Reset to page 1 on new search
    }, 500); // 500ms delay
    return () => clearTimeout(handler);
  }, [searchTerm]);


  const fetchPicturesList = useCallback(async (page, search) => {
    setIsLoadingPictures(true);
    setPicturesError('');
    try {
      const data = await getBbPictures(page, search, ITEMS_PER_PAGE);
      setPicturesList(data.pictures || []);
      setTotalPages(data.totalPages || 0);
      setTotalPictures(data.totalPictures || 0);
      setCurrentPage(data.currentPage || 1);
    } catch (error) {
      console.error('Fetch pictures error:', error);
      setPicturesError(error.message || 'Failed to fetch pictures.');
      setPicturesList([]);
      setTotalPages(0);
      setTotalPictures(0);
    } finally {
      setIsLoadingPictures(false);
    }
  }, []);

  useEffect(() => {
    fetchPicturesList(currentPage, debouncedSearchTerm);
  }, [currentPage, debouncedSearchTerm, fetchPicturesList]);

  // Auto-scan for local images on first load
  useEffect(() => {
    const autoScan = async () => {
      try {
        await scanLocalImages();
        // Refresh the list after auto-scan
        fetchPicturesList(1, '');
      } catch (error) {
        // Silently fail auto-scan - user can manually scan if needed
        console.log('Auto-scan failed:', error.message);
      }
    };
    autoScan();
  }, []); // Only run once on mount

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    setUploadSuccess(''); setUploadError('');
    if (!file) {
      setSelectedFile(null); setFilePreview(null); return;
    }
    const fileType = `.${file.name.split('.').pop().toLowerCase()}`;
    if (!ACCEPTED_FILE_TYPES.includes(fileType)) {
      setUploadError(`Invalid file type. Please select ${ACCEPTED_FILE_TYPES_STRING}.`);
      setSelectedFile(null); setFilePreview(null); event.target.value = null; return;
    }
    setSelectedFile(file);
    const reader = new FileReader();
    reader.onloadend = () => setFilePreview(reader.result);
    reader.readAsDataURL(file);
  };

  const handleUpload = async () => {
    if (!selectedFile) { setUploadError('No file selected.'); return; }
    setIsUploading(true); setUploadError(''); setUploadSuccess('');
    const formData = new FormData();
    formData.append('picture', selectedFile);
    try {
      const response = await uploadBbPicture(formData);
      setUploadSuccess(`File "${response.name}" uploaded! ID: ${response.id}`);
      setSelectedFile(null); setFilePreview(null);
      if(document.getElementById('bb-picture-upload-input')) {
        document.getElementById('bb-picture-upload-input').value = null;
      }
      fetchPicturesList(1, ''); // Refresh list on page 1, clear search
    } catch (error) {
      setUploadError(error.message || 'Upload failed.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const handlePageChange = (event, value) => {
    setCurrentPage(value);
  };

  // Rename handlers
  const openRenameModal = (picture) => {
    setEditPictureId(picture.id);
    setEditPictureName(picture.name);
    setIsRenameModalOpen(true);
    setRenameError('');
  };
  const closeRenameModal = () => {
    setIsRenameModalOpen(false); setEditPictureId(null); setEditPictureName(''); setRenameError('');
  };
  const handleRenamePicture = async () => {
    if (!editPictureName.trim()) { setRenameError('Filename cannot be empty.'); return; }
    try {
      await updateBbPictureName(editPictureId, editPictureName.trim());
      fetchPicturesList(currentPage, debouncedSearchTerm); // Refresh
      closeRenameModal();
    } catch (error) {
      setRenameError(error.message || 'Failed to rename picture.');
    }
  };

  // Delete handlers
  const openDeleteConfirm = (pictureId) => {
    setPictureToDeleteId(pictureId);
    setIsDeleteConfirmOpen(true);
  };
  const closeDeleteConfirm = () => {
    setIsDeleteConfirmOpen(false); setPictureToDeleteId(null);
  };
  const handleDeletePicture = async () => {
    try {
      await deleteBbPicture(pictureToDeleteId);
      fetchPicturesList(1, ''); // Refresh list, go to page 1 and clear search
      closeDeleteConfirm();
    } catch (error) {
      setPicturesError(error.message || 'Failed to delete picture.'); // Show error in main list area
      closeDeleteConfirm();
    }
  };

  const handleScanLocalImages = async () => {
    setIsScanningLocal(true);
    setScanLocalError('');
    setScanLocalSuccess('');
    try {
      const result = await scanLocalImages();
      setScanLocalSuccess(result.message);
      if (result.imported > 0) {
        fetchPicturesList(1, ''); // Refresh list on page 1, clear search
      }
    } catch (error) {
      setScanLocalError(error.message || 'Failed to scan local images');
    } finally {
      setIsScanningLocal(false);
    }
  };

  const formatDate = (isoString) => isoString ? new Date(isoString).toLocaleString() : 'N/A';

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom component="h1">Picture Management (Bounding Box)</Typography>

      <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>Upload New Picture</Typography>
        <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>Accepted file types: {ACCEPTED_FILE_TYPES_STRING}</Typography>
        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
          <strong>Tip:</strong> You can also place images directly in the Input folder and click "Scan Local Images" to import them automatically.
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, alignItems: 'flex-start' }}>
          <Input type="file" id="bb-picture-upload-input" onChange={handleFileSelect} inputProps={{ accept: ACCEPTED_FILE_TYPES_STRING }} sx={{ display: 'block', mb: 1 }}/>
          {filePreview && selectedFile && (
            <Box sx={{ my: 1, textAlign: 'left', border: '1px solid #ddd', p:1, borderRadius:1, maxWidth:220 }}>
              <Typography variant="subtitle2">Preview:</Typography>
              <img src={filePreview} alt={selectedFile.name} style={{ width: '200px', height: 'auto', maxHeight: '200px', marginTop: '10px', objectFit:'contain' }} />
              <Typography variant="caption" display="block">{selectedFile.name}</Typography>
            </Box>
          )}
          {uploadError && <Alert severity="error" sx={{ width: '100%', mt:1 }}>{uploadError}</Alert>}
          {uploadSuccess && <Alert severity="success" sx={{ width: '100%', mt:1 }}>{uploadSuccess}</Alert>}
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Button variant="contained" color="primary" onClick={handleUpload} disabled={!selectedFile || isUploading} startIcon={isUploading ? <CircularProgress size={20} color="inherit" /> : null}>
              {isUploading ? 'Uploading...' : 'Upload Picture'}
            </Button>
            <Button variant="outlined" color="secondary" onClick={handleScanLocalImages} disabled={isScanningLocal} startIcon={isScanningLocal ? <CircularProgress size={20} color="inherit" /> : null}>
              {isScanningLocal ? 'Scanning...' : 'Scan Local Images'}
            </Button>
          </Box>
          {scanLocalError && <Alert severity="error" sx={{ width: '100%', mt: 1 }}>{scanLocalError}</Alert>}
          {scanLocalSuccess && <Alert severity="success" sx={{ width: '100%', mt: 1 }}>{scanLocalSuccess}</Alert>}
        </Box>
      </Paper>

      <Typography variant="h6" gutterBottom sx={{mt:4}}>Image Browser</Typography>
      <TextField fullWidth label="Search by filename" variant="outlined" size="small" value={searchTerm} onChange={handleSearchChange} sx={{ mb: 2 }}/>

      {isLoadingPictures && <Box sx={{display:'flex', justifyContent:'center', my:2}}><CircularProgress /></Box>}
      {picturesError && <Alert severity="error" sx={{ mb: 2 }}>{picturesError}</Alert>}

      {!isLoadingPictures && picturesList.length === 0 && (
        <Typography sx={{textAlign:'center', my:2}}>No images to display. Use the upload tool above to add pictures.</Typography>
      )}

      {!isLoadingPictures && picturesList.length > 0 && (
        <>
          <TableContainer component={Paper}>
            <Table sx={{ minWidth: 650 }} aria-label="pictures table">
              <TableHead>
                <TableRow>
                  <TableCell sx={{width:120}}>Thumbnail</TableCell>
                  <TableCell>Filename</TableCell>
                  <TableCell>Created At</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {picturesList.map((pic) => (
                  <TableRow key={pic.id}>
                    <TableCell>
                      {pic.image_data ? (
                        <img src={pic.image_data} alt={pic.name} style={{ width: 100, height: 'auto', maxHeight: 70, objectFit: 'contain' }} />
                      ) : <PhotoCameraBackIcon sx={{fontSize: 40, color: 'grey.400'}} />}
                    </TableCell>
                    <TableCell>{pic.name}</TableCell>
                    <TableCell>{formatDate(pic.created_at)}</TableCell>
                    <TableCell align="right">
                      <Tooltip title="Edit Filename">
                        <IconButton onClick={() => openRenameModal(pic)} size="small"><EditIcon /></IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Picture">
                        <IconButton onClick={() => openDeleteConfirm(pic.id)} size="small" color="error"><DeleteIcon /></IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
              <Pagination count={totalPages} page={currentPage} onChange={handlePageChange} color="primary" disabled={isLoadingPictures} />
            </Box>
          )}
          <Typography variant="caption" sx={{display:'block', textAlign:'right', mt:1}}>Total pictures: {totalPictures}</Typography>
        </>
      )}

      {/* Rename Modal */}
      <Dialog open={isRenameModalOpen} onClose={closeRenameModal}>
        <DialogTitle>Rename Picture</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{mb:1}}>Enter a new filename for the picture:</DialogContentText>
          {renameError && <Alert severity="error" sx={{mb:2}}>{renameError}</Alert>}
          <TextField autoFocus margin="dense" id="name" label="New Filename" type="text" fullWidth variant="outlined" value={editPictureName} onChange={(e) => setEditPictureName(e.target.value)} />
        </DialogContent>
        <DialogActions>
          <Button onClick={closeRenameModal}>Cancel</Button>
          <Button onClick={handleRenamePicture} disabled={!editPictureName.trim()}>Save</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteConfirmOpen} onClose={closeDeleteConfirm}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>Are you sure you want to delete this picture? This action may also affect associated experiments and results. This cannot be undone.</DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDeleteConfirm}>Cancel</Button>
          <Button onClick={handleDeletePicture} color="error">Delete</Button>
        </DialogActions>
      </Dialog>

    </Container>
  );
};

export default BoundingBoxPictureManagementPage;
