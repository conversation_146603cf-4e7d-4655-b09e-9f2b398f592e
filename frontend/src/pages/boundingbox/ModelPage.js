import React, { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  CircularProgress,
  Alert,
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Tooltip
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { getBbModels, updateBbModel, createBbModel, deleteBbModel } from '../../services/api_bounding_box';

const ModelPage = () => {
  const [models, setModels] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Add model dialog state
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newModelName, setNewModelName] = useState('');
  const [newModelDescription, setNewModelDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  // Delete confirmation state
  const [deleteModelId, setDeleteModelId] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchModels = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await getBbModels();
      setModels(data || []); // Ensure models is an array
    } catch (err) {
      setError('Failed to load models.');
      console.error(err);
      setModels([]); // Clear models on error
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchModels();
  }, [fetchModels]);

  const handleToggleActive = async (modelId, isActive) => {
    // Optimistic UI update
    setModels(prevModels =>
      prevModels.map(model =>
        model.id === modelId ? { ...model, is_active: isActive } : model
      )
    );
    setError(null); // Clear previous errors

    try {
      await updateBbModel(modelId, { is_active: isActive });
      // Optionally: show a success snackbar or log
      console.log(`Model ${modelId} active status updated to ${isActive}`);
      // If API returns updated model, you could update state with it:
      // setModels(prevModels => prevModels.map(m => m.id === modelId ? updatedModelFromApi : m));
    } catch (err) {
      setError(`Failed to update model ${modelId}. Reverting change.`);
      console.error(err);
      // Revert optimistic update on error
      setModels(prevModels =>
        prevModels.map(model =>
          model.id === modelId ? { ...model, is_active: !isActive } : model
        )
      );
    }
  };

  const handleCreateModel = async () => {
    if (!newModelName.trim()) {
      setError('Model name is required');
      return;
    }

    setIsCreating(true);
    setError(null);
    try {
      const newModel = await createBbModel({
        name: newModelName.trim(),
        description: newModelDescription.trim(),
        is_active: true
      });
      setModels([...models, newModel]);
      setIsAddDialogOpen(false);
      setNewModelName('');
      setNewModelDescription('');
    } catch (error) {
      setError(`Failed to create model: ${error.message}`);
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteModel = async (modelId) => {
    setIsDeleting(true);
    setError(null);
    try {
      await deleteBbModel(modelId);
      setModels(models.filter(model => model.id !== modelId));
      setDeleteModelId(null);
    } catch (error) {
      setError(`Failed to delete model: ${error.message}`);
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography sx={{ mt: 1 }}>Loading models...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Model Management (Bounding Box)
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setIsAddDialogOpen(true)}
        >
          Add Model
        </Button>
      </Box>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <TableContainer component={Paper}>
        <Table aria-label="model management table">
          <TableHead>
            <TableRow>
              <TableCell>Model Name</TableCell>
              <TableCell>Description</TableCell>
              <TableCell align="center">Active</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {models.length === 0 && !isLoading && (
              <TableRow>
                <TableCell colSpan={4} align="center">
                  No models found.
                </TableCell>
              </TableRow>
            )}
            {models.map((model) => (
              <TableRow key={model.id}>
                <TableCell component="th" scope="row">
                  {model.name}
                </TableCell>
                <TableCell>{model.description}</TableCell>
                <TableCell align="center">
                  <Switch
                    checked={model.is_active}
                    onChange={(e) => handleToggleActive(model.id, e.target.checked)}
                    inputProps={{ 'aria-label': `toggle ${model.name} active status` }}
                  />
                </TableCell>
                <TableCell align="center">
                  <Tooltip title="Delete Model">
                    <IconButton
                      color="error"
                      onClick={() => setDeleteModelId(model.id)}
                      disabled={isDeleting}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add Model Dialog */}
      <Dialog open={isAddDialogOpen} onClose={() => setIsAddDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Model</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Model Name"
            fullWidth
            variant="outlined"
            value={newModelName}
            onChange={(e) => setNewModelName(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Description (Optional)"
            fullWidth
            variant="outlined"
            multiline
            rows={3}
            value={newModelDescription}
            onChange={(e) => setNewModelDescription(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsAddDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleCreateModel}
            variant="contained"
            disabled={isCreating || !newModelName.trim()}
          >
            {isCreating ? <CircularProgress size={20} /> : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteModelId !== null} onClose={() => setDeleteModelId(null)}>
        <DialogTitle>Delete Model</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this model? This will also delete all associated experiment results.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteModelId(null)}>Cancel</Button>
          <Button
            onClick={() => handleDeleteModel(deleteModelId)}
            color="error"
            variant="contained"
            disabled={isDeleting}
          >
            {isDeleting ? <CircularProgress size={20} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ModelPage;
