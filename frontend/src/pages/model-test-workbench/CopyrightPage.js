import React, { useState } from 'react';
import { Box, Typography, Tabs, Tab, Paper } from '@mui/material';
import ByModelView from '../../components/model-test-workbench/ByModelView';
import ByProductView from '../../components/model-test-workbench/ByProductView'; // Import the ByProductView component

// TabPanel component to manage content display
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`copyright-tabpanel-${index}`}
      aria-labelledby={`copyright-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}> {/* Add padding top */}
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `copyright-tab-${index}`,
    'aria-controls': `copyright-tabpanel-${index}`,
  };
}

function CopyrightPage() {
  const [activeTab, setActiveTab] = useState(0); // 0 for "By Model", 1 for "By Product"

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h4" gutterBottom sx={{ mb: 2 }}>
        Copyright Results Analysis
      </Typography>

      <Paper elevation={1} sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label="Copyright result views">
            <Tab label="By Model" {...a11yProps(0)} />
            <Tab label="By Product Picture" {...a11yProps(1)} /> {/* Enable the tab */}
          </Tabs>
        </Box>
        <TabPanel value={activeTab} index={0}>
          {/* Render ByModelView for the 'copyright' category */}
          <ByModelView ipCategory="copyright" />
        </TabPanel>
        <TabPanel value={activeTab} index={1}>
          {/* Render ByProductView for the 'copyright' category */}
          <ByProductView ipCategory="copyright" />
        </TabPanel>
      </Paper>
    </Box>
  );
}

export default CopyrightPage;