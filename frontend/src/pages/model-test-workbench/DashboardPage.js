import React, { useState } from 'react';
import { Box, Tabs, Tab, Typography, Paper } from '@mui/material';
import MetricsDisplay from '../../components/model-test-workbench/MetricsDisplay'; // Adjust path as needed

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dashboard-tabpanel-${index}`}
      aria-labelledby={`dashboard-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}> {/* Add padding top to separate content from tabs */}
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `dashboard-tab-${index}`,
    'aria-controls': `dashboard-tabpanel-${index}`,
  };
}

const DashboardPage = () => {
  const [value, setValue] = useState(0); // 0: Trademark, 1: Copyright, 2: Patent

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%', p: 3 }}> {/* Add padding around the whole page */}
       <Typography variant="h4" gutterBottom component="div">
            Dashboard
       </Typography>
       <Paper elevation={1}> {/* Wrap Tabs in Paper for better visual separation */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs value={value} onChange={handleChange} aria-label="IP category performance tabs">
                <Tab label="Trademark Metrics" {...a11yProps(0)} />
                <Tab label="Copyright Metrics" {...a11yProps(1)} />
                <Tab label="Patent Metrics" {...a11yProps(2)} />
                </Tabs>
            </Box>
            <TabPanel value={value} index={0}>
                <MetricsDisplay ipCategory="trademark" />
            </TabPanel>
            <TabPanel value={value} index={1}>
                <MetricsDisplay ipCategory="copyright" />
            </TabPanel>
            <TabPanel value={value} index={2}>
                <MetricsDisplay ipCategory="patent" />
            </TabPanel>
       </Paper>
    </Box>
  );
};

export default DashboardPage;