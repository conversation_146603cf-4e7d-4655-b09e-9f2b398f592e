import apiClient from './api'; // Assuming api.js exports the configured axios instance

const API_BASE_URL = '/api/v1/boundingbox';


export const getBbPictures = async (page = 1, searchTerm = '', perPage = 5) => {
  try {
    const response = await apiClient.get(`${API_BASE_URL}/pictures`, {
      params: { page, search: searchTerm, per_page: perPage }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching bounding box pictures:', error);
    throw error;
  }
};

export const uploadBbPicture = async (formData) => {
  try {
    const response = await apiClient.post(`${API_BASE_URL}/pictures`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error uploading bounding box picture:', error);
    throw error;
  }
};

export const updateBbPictureName = async (pictureId, newName) => {
  try {
    const response = await apiClient.put(`${API_BASE_URL}/pictures/${pictureId}`, {
      name: newName
    });
    return response.data;
  } catch (error) {
    console.error('Error updating bounding box picture name:', error);
    throw error;
  }
};

export const deleteBbPicture = async (pictureId) => {
  try {
    const response = await apiClient.delete(`${API_BASE_URL}/pictures/${pictureId}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting bounding box picture:', error);
    throw error;
  }
};

export const scanLocalImages = async () => {
  try {
    const response = await apiClient.post(`${API_BASE_URL}/pictures/scan-local`);
    return response.data;
  } catch (error) {
    console.error('Error scanning local images:', error);
    throw error;
  }
};

export const getBbExperiments = async (pictureId, page = 1, perPage = 5) => {
  try {
    if (!pictureId) {
      return { experiments: [], totalPages: 0, currentPage: page, totalExperiments: 0 };
    }

    const response = await apiClient.get(`${API_BASE_URL}/experiments`, {
      params: { picture_id: pictureId, page, per_page: perPage }
    });

    // Transform backend response to match frontend expectations
    return {
      experiments: response.data.experiments || [],
      totalPages: response.data.pages || 0,
      currentPage: response.data.current_page || page,
      totalExperiments: response.data.total || 0
    };
  } catch (error) {
    console.error('Error fetching bounding box experiments:', error);
    throw error;
  }
};

export const updateBbResultScore = async (resultId, score) => {
  try {
    const response = await apiClient.put(`${API_BASE_URL}/results/${resultId}`, {
      score: score
    });
    return response.data;
  } catch (error) {
    console.error('Error updating bounding box result score:', error);
    throw error;
  }
};

export const getBbExperimentConfigurations = async () => {
  try {
    const response = await apiClient.get(`${API_BASE_URL}/experiments/configurations`);
    return response.data;
  } catch (error) {
    console.error('Error fetching bounding box experiment configurations:', error);
    throw error;
  }
};

export const createBbExperiment = async (experimentData) => {
  try {
    const response = await apiClient.post(`${API_BASE_URL}/experiments`, experimentData);
    return response.data;
  } catch (error) {
    console.error('Error creating bounding box experiment:', error);
    throw error;
  }
};

export const getBbModels = async () => {
  try {
    const response = await apiClient.get(`${API_BASE_URL}/models`);
    return response.data;
  } catch (error) {
    console.error('Error fetching bounding box models:', error);
    throw error;
  }
};

export const createBbModel = async (modelData) => {
  try {
    const response = await apiClient.post(`${API_BASE_URL}/models`, modelData);
    return response.data;
  } catch (error) {
    console.error('Error creating bounding box model:', error);
    throw error;
  }
};

export const updateBbModel = async (modelId, data) => {
  try {
    const response = await apiClient.put(`${API_BASE_URL}/models/${modelId}`, data);
    return response.data;
  } catch (error) {
    console.error('Error updating bounding box model:', error);
    throw error;
  }
};

export const deleteBbModel = async (modelId) => {
  try {
    const response = await apiClient.delete(`${API_BASE_URL}/models/${modelId}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting bounding box model:', error);
    throw error;
  }
};

export const deleteBbExperiment = async (experimentId) => {
  try {
    const response = await apiClient.delete(`${API_BASE_URL}/experiments/${experimentId}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting bounding box experiment:', error);
    throw error;
  }
};

export const getBbRankData = async () => {
  try {
    // For ranking, we need both models and all experiments
    const [modelsResponse, experimentsResponse] = await Promise.all([
      getBbModels(),
      apiClient.get(`${API_BASE_URL}/experiments/all`) // Assuming there's an endpoint for all experiments
    ]);

    return {
      models: modelsResponse,
      experiments: experimentsResponse.data || []
    };
  } catch (error) {
    console.error('Error fetching bounding box rank data:', error);
    throw error;
  }
};
