import axios from 'axios';

// Use empty string to let the proxy handle the routing
const API_BASE_URL = '';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
});

// Add a response interceptor
apiClient.interceptors.response.use(
  (response) => {
    // Check for backend-specific success messages
    if (response.data && response.data.message) {
      // You might want to attach this to the response object for components to use
      // e.g., response.successMessage = response.data.message;
    } else if (response.data && response.data.detail && response.status >= 200 && response.status < 300) {
      // Fallback for success messages in 'detail'
      // response.successMessage = response.data.detail;
    }
    // For successful image uploads, the structure is different (response.data.success, response.data.errors)
    // This will be handled in the ImageUpload.js component as per instructions.
    return response;
  },
  (error) => {
    // Check for backend-specific error messages
    if (error.response && error.response.data) {
      if (error.response.data.error) {
        error.message = error.response.data.error;
      } else if (error.response.data.detail) {
        // Fallback for error messages in 'detail'
        error.message = error.response.data.detail;
      }
      // Attach the structured errors if available (e.g., for form validation)
      if (error.response.data.errors) {
        error.errors = error.response.data.errors;
      }
    }
    // If no specific message, Axios error.message or a default will be used
    return Promise.reject(error);
  }
);

// Export the apiClient instance for use in other service files
export default apiClient;

// Functions that were not categorized as ModelTestWorkBench or PatentViz specific
// could remain here if they are truly generic.
// For now, assuming all specific functions have been moved.
// If any shared functions are identified later, they can be added here.

// Example of a shared utility if needed (currently none identified from the original file)
// export const getSomeSharedResource = () => {
//   return apiClient.get('/shared-resource');
// };