import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import TrademarkPage from './pages/model-test-workbench/TrademarkPage';
import CopyrightPage from './pages/model-test-workbench/CopyrightPage';
import PatentPage from './pages/model-test-workbench/PatentPage';
import DashboardPage from './pages/model-test-workbench/DashboardPage';
import SettingsPage from './pages/model-test-workbench/SettingsPage';
import PatentPlatformPage from './pages/PatentPlatformPage'; // Import the new Patent Platform Page
import ResultsPage from './pages/boundingbox/ResultsPage'; // Import Bounding Box Results Page
import ModelPage from './pages/boundingbox/ModelPage'; // Import Bounding Box Model Page
import BoundingBoxPictureManagementPage from './pages/boundingbox/PictureManagementPage'; // Import Bounding Box Picture Management Page
import RankPage from './pages/boundingbox/RankPage'; // Import Bounding Box Rank Page
import './App.css'; // Keep default styles for now

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Layout />}>
          {/* Default route redirects to Dashboard */}
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="trademark" element={<TrademarkPage />} />
          <Route path="copyright" element={<CopyrightPage />} />
          <Route path="patent" element={<PatentPage />} />
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="settings" element={<SettingsPage />} />
          <Route path="boundingbox" element={<ResultsPage />} />
          <Route path="boundingbox/models" element={<ModelPage />} />
          <Route path="boundingbox/pictures" element={<BoundingBoxPictureManagementPage />} />
          <Route path="boundingbox/ranking" element={<RankPage />} /> {/* New Bounding Box Ranking Route */}
          <Route path="patent-viz/*" element={<PatentPlatformPage />} /> {/* Route for Patent Visualization Platform */}
          {/* Add other nested routes or a 404 handler here if needed */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} /> {/* Basic fallback */}
        </Route>
      </Routes>
    </BrowserRouter>
  );
}

export default App;
