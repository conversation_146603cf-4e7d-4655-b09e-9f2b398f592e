import React from 'react';
import { TablePagination, Paper } from '@mui/material';

// TDD: TEST: PaginationControls displays correct page info and options
// TDD: TEST: PaginationControls calls on_page_change and on_per_page_change

function PaginationControls({
  current_page, // 1-indexed
  items_per_page,
  total_items,
  on_page_change, // Expects 1-indexed new page
  on_per_page_change,
}) {
  if (total_items === undefined || total_items === null || total_items <= 0) {
    return null; // Don't render pagination if there are no items or total_items is not set
  }
  
  const handlePageChange = (event, new_page_zero_indexed) => {
    on_page_change(new_page_zero_indexed + 1);
  };

  const handleRowsPerPageChange = (event) => {
    on_per_page_change(parseInt(event.target.value, 10));
  };

  return (
    <Paper sx={{ mt: 2, display: 'flex', justifyContent: 'center' }} elevation={0}>
      <TablePagination
        component="div"
        count={total_items || 0} // Ensure count is not undefined
        page={current_page - 1} // MUI TablePagination is 0-indexed for page
        rowsPerPage={items_per_page}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        rowsPerPageOptions={[10, 25, 50, 100, 200]}
        showFirstButton
        showLastButton
        // sx={{ width: '100%' }} // Make it take full width if needed
      />
    </Paper>
  );
}

export default PaginationControls;