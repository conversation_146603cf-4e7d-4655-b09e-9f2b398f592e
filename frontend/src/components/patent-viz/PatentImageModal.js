import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardMedia,
  Typography,
  Box,
} from '@mui/material';

// TDD: TEST: PatentImageModal displays patent_title
// TDD: TEST: PatentImageModal displays images from image_data.image_paths
// TDD: TEST: PatentImageModal shows loading/error states
// TDD: TEST: PatentImageModal handles case with no images

function PatentImageModal({
  is_open,
  on_close,
  patent_title,
  image_data, // { image_paths, patent_reg_no, base_image_folder_path }
  is_loading,
  error,
}) {
  // Construct full image URLs if base_image_folder_path is relative
  // For now, assuming image_paths are absolute or directly usable.
  // If base_image_folder_path is provided and paths are relative, you'd join them.
  // e.g., `${image_data.base_image_folder_path}/${image_path}`
  // The backend currently provides full relative paths from the static folder.

  const images = image_data?.image_paths || [];

  return (
    <Dialog open={is_open} onClose={on_close} fullWidth maxWidth="lg" scroll="paper">
      <DialogTitle sx={{ borderBottom: '1px solid #ccc' }}>
        Images for: {patent_title || 'Loading...'}
        {image_data?.patent_reg_no && ` (Reg No: ${image_data.patent_reg_no})`}
      </DialogTitle>
      <DialogContent dividers>
        {is_loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
            <CircularProgress />
          </Box>
        )}
        {error && !is_loading && (
          <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>
        )}
        {!is_loading && !error && images.length > 0 && (
          <Grid container spacing={2} sx={{ p: 1 }}>
            {images.map((image_path, index) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                <Card raised sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <CardMedia
                    component="img"
                    // Assuming image_path is like "/static/patent_images/US12345/image1.png"
                    // The API_BASE_URL might need to be prepended if paths are not absolute from domain root
                    // For now, assuming paths are directly usable by browser if served from same domain.
                    image={image_path} 
                    alt={`Patent Image ${index + 1} for ${patent_title}`}
                    sx={{
                      objectFit: 'contain', // Or 'cover' based on preference
                      maxHeight: 300, // Limit image height
                      p: 0.5 // Some padding around the image
                    }}
                  />
                  {/* Optional: Display image filename or other info */}
                  {/* <CardContent sx={{flexGrow: 1}}>
                    <Typography variant="caption" display="block" gutterBottom>
                      {image_path.split('/').pop()}
                    </Typography>
                  </CardContent> */}
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
        {!is_loading && !error && images.length === 0 && (
          <Alert severity="info" sx={{ mt: 2 }}>
            No images found for this patent.
          </Alert>
        )}
      </DialogContent>
      <DialogActions sx={{ borderTop: '1px solid #ccc', p: '16px 24px' }}>
        <Button onClick={on_close} variant="outlined">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default PatentImageModal;