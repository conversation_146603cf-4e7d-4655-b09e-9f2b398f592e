import React from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  Alert,
  Typography,
  Grid,
  Box,
  Paper,
  List,
  ListItem,
  ListItemText,
  Divider,
} from '@mui/material';

// TDD: TEST: PatentDetailModal displays all patent_data fields when available
// TDD: TEST: PatentDetailModal displays USPC, LOC, CPC definitions correctly
// TDD: TEST: PatentDetailModal shows loading/error states

const formatLabel = (key) => {
  return key
    .replace(/_/g, ' ')
    .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize each word
};

const renderValue = (value) => {
  if (value === null || value === undefined || value === '') {
    return <Typography variant="body2" color="text.secondary">N/A</Typography>;
  }
  if (Array.isArray(value)) {
    return value.length > 0 ? value.join(', ') : <Typography variant="body2" color="text.secondary">N/A</Typography>;
  }
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }
  if (typeof value === 'object') {
    // Basic object display, might need refinement for specific nested objects
    return <pre>{JSON.stringify(value, null, 2)}</pre>;
  }
  return String(value);
};

const DetailItem = ({ label, value }) => (
  <Grid item xs={12} sm={6} md={4}>
    <Paper elevation={0} sx={{ p: 1.5, border: '1px solid #eee', height: '100%' }}>
      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
        {formatLabel(label)}
      </Typography>
      <Typography variant="body2" component="div" sx={{ wordBreak: 'break-word' }}>
        {renderValue(value)}
      </Typography>
    </Paper>
  </Grid>
);

const SectionTitle = ({ children }) => (
    <Typography variant="h6" component="h3" sx={{ mt: 3, mb: 2, borderBottom: '1px solid #ddd', pb: 1}}>
        {children}
    </Typography>
);


function PatentDetailModal({ is_open, on_close, patent_data, is_loading, error }) {
  const generalInfoFields = [
    'document_id', 'patent_title', 'reg_no', 'application_number', 'patent_type', 'tro',
    'date_published', 'date_filed', 'claims_count', 'independent_claims_count',
    'inventors', 'assignee', 'applicant', 'family_id', 'publication_country', 'publication_language'
    // Add or remove fields as necessary based on what's available and relevant
  ];
  
  const abstractField = 'abstract';
  
  const classificationFields = {
      uspc: 'uspc_definition_text',
      loc: 'loc_definition_text', // This might be an object { class_definition, subclass_definition }
      cpc: 'cpc_definitions' // This is an array of { cpc_code, definition_text }
  };


  return (
    <Dialog open={is_open} onClose={on_close} fullWidth maxWidth="lg" scroll="paper">
      <DialogTitle sx={{ borderBottom: '1px solid #ccc' }}>
        Patent Details: {patent_data ? patent_data.patent_title : 'Loading...'}
      </DialogTitle>
      <DialogContent dividers>
        {is_loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
            <CircularProgress />
          </Box>
        )}
        {error && !is_loading && (
          <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>
        )}
        {!is_loading && !error && patent_data && (
          <Box>
            <SectionTitle>General Information</SectionTitle>
            <Grid container spacing={2}>
              {generalInfoFields.map((key) =>
                patent_data.hasOwnProperty(key) ? (
                  <DetailItem key={key} label={key} value={patent_data[key]} />
                ) : null
              )}
            </Grid>

            {patent_data[abstractField] && (
                <>
                    <SectionTitle>Abstract</SectionTitle>
                    <Paper elevation={0} sx={{ p: 2, border: '1px solid #eee', whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                        <Typography variant="body2">{patent_data[abstractField]}</Typography>
                    </Paper>
                </>
            )}
            
            <SectionTitle>Classifications</SectionTitle>
            {patent_data[classificationFields.uspc] && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom>USPC Definition:</Typography>
                <Paper elevation={0} sx={{ p: 1.5, border: '1px solid #eee' }}>
                  <Typography variant="body2">{renderValue(patent_data[classificationFields.uspc])}</Typography>
                </Paper>
              </Box>
            )}

            {patent_data[classificationFields.loc] && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom>LOC Definition:</Typography>
                <Paper elevation={0} sx={{ p: 1.5, border: '1px solid #eee' }}>
                  {typeof patent_data[classificationFields.loc] === 'object' ? (
                    <>
                      <Typography variant="body2"><strong>Class:</strong> {renderValue(patent_data[classificationFields.loc].class_definition)}</Typography>
                      <Typography variant="body2"><strong>Subclass:</strong> {renderValue(patent_data[classificationFields.loc].subclass_definition)}</Typography>
                    </>
                  ) : (
                    <Typography variant="body2">{renderValue(patent_data[classificationFields.loc])}</Typography>
                  )}
                </Paper>
              </Box>
            )}

            {patent_data[classificationFields.cpc] && Array.isArray(patent_data[classificationFields.cpc]) && patent_data[classificationFields.cpc].length > 0 && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>CPC Definitions:</Typography>
                <List disablePadding>
                  {patent_data[classificationFields.cpc].map((cpc, index) => (
                    <React.Fragment key={index}>
                      <ListItem sx={{p:1, border: '1px solid #eee', mb:1, borderRadius: '4px'}}>
                        <ListItemText
                          primary={<Typography variant="body2"><strong>{cpc.cpc_code}:</strong> {cpc.definition_text || 'N/A'}</Typography>}
                        />
                      </ListItem>
                    </React.Fragment>
                  ))}
                </List>
              </Box>
            )}
            
            {/* You can add more sections here for other complex fields if needed */}

          </Box>
        )}
        {!is_loading && !error && !patent_data && (
          <Alert severity="info" sx={{ mt: 2 }}>No patent data to display.</Alert>
        )}
      </DialogContent>
      <DialogActions sx={{ borderTop: '1px solid #ccc', p: '16px 24px' }}>
        <Button onClick={on_close} variant="outlined">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default PatentDetailModal;