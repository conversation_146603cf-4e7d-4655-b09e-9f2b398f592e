import React from 'react';
import { Box, Typography, Paper } from '@mui/material';

// TDD: TEST: QuickStatsDisplay renders all stats correctly formatted

const formatPercentage = (value) => {
  if (value === null || value === undefined) return "N/A";
  return `${(value * 100).toFixed(1)}%`;
};

function QuickStatsDisplay({ stats }) {
  if (!stats || stats.total_filtered_results === undefined) { // Check for undefined instead of null for count
    return null;
  }

  return (
    <Paper sx={{ p: 1.5, mb: 2, backgroundColor: 'grey.100' }} elevation={0} variant="outlined">
      <Typography variant="body2" component="div" sx={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
        <span><strong>Total Results:</strong> {stats.total_filtered_results.toLocaleString()}</span>
        <span><strong>% TRO in Results:</strong> {formatPercentage(stats.percentage_tro_in_results)}</span>
        <span><strong>% Design in Results:</strong> {formatPercentage(stats.percentage_design_in_results)}</span>
        {/* Add other stats as they become available in the API response */}
      </Typography>
    </Paper>
  );
}

export default QuickStatsDisplay;