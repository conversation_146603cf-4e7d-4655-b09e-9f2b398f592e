import React from 'react';
import { Card, CardContent, Typography } from '@mui/material';

// Helper function to format numbers with commas
const formatValue = (value) => {
  if (value === null || value === undefined) return 'N/A';

  // If it's a number, format it with commas
  if (typeof value === 'number' || !isNaN(Number(value))) {
    return Number(value).toLocaleString();
  }

  // Otherwise return as is
  return value;
};

// TDD: TEST: SingleStatisticDisplay renders label and value correctly
const SingleStatisticDisplay = ({ label, value }) => {
  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="subtitle1" color="textSecondary" gutterBottom>
          {label}
        </Typography>
        <Typography variant="h5" component="div">
          {formatValue(value)}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default SingleStatisticDisplay;