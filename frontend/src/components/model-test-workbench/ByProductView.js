import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
    Box,
    Typography,
    CircularProgress,
    Alert,
    Grid,
    Autocomplete,
    TextField,
    Card,
    CardContent,
    CardMedia,
    IconButton,
    Tooltip,
    Dialog,
    DialogContent,
    <PERSON>alogTitle,
    <PERSON><PERSON>,
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import AddTaskIcon from '@mui/icons-material/AddTask'; // Icon for Mark as Ground Truth
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline'; // Icon for Remove Ground Truth
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ClearIcon from '@mui/icons-material/Clear';
// Use named imports for API functions
import { listImages, getResultsByProduct, addGroundTruth, removeGroundTruth } from '../../services/api_model_workbench';

const INITIAL_SUGGESTIONS_LIMIT = 50; // Total number of suggestions to fetch per model initially
const DEFAULT_VISIBLE_SUGGESTIONS = 5; // Number of suggestions to show initially and increment

const ByProductView = ({ ipCategory }) => {
    console.log(`ByProductView received ipCategory: ${ipCategory}`); // Debug log for ipCategory
    const [rawProductImages, setRawProductImages] = useState([]); // Renamed state to hold raw API data
    const [selectedProduct, setSelectedProduct] = useState(null); // Store the whole product object
    const [results, setResults] = useState(null);
    const [loadingProducts, setLoadingProducts] = useState(false);
    const [loadingResults, setLoadingResults] = useState(false);
    const [error, setError] = useState(null);
    const [imageToEnlarge, setImageToEnlarge] = useState(null);
    const [visibleCounts, setVisibleCounts] = useState({}); // State to track visible suggestions per model

    const imageBaseUrl = '/api/data/images'; // Added for useMemo

    // useMemo hook as provided by the user for productImagesForAutocomplete
    const productImagesForAutocomplete = useMemo(() => {
        console.log('[useMemo] productImages state:', rawProductImages); // Log the input state (rawProductImages)
        
        const imagesToMap = rawProductImages || [];
        console.log('[useMemo] imagesToMap (rawProductImages || []):', imagesToMap);
        console.log('[useMemo] imagesToMap length:', imagesToMap.length);

        const mappedImages = imagesToMap.map((image, index) => {
            if (index < 3) { // Keep existing detailed log for first few items
                console.log(`[useMemo map] Mapping image ${index}:`, JSON.stringify(image, null, 2));
                console.log(`[useMemo map] Image ${index} - image_id:`, image.image_id, `original_filename:`, image.original_filename);
            }
            return {
                id: image.image_id,
                label: image.original_filename || 'Unnamed Image',
                imageUrl: image.image_id ? `${imageBaseUrl}/file/${image.image_id}` : null, // Ensure image.image_id exists for URL
                ip_category: image.ip_category
            };
        });
        console.log('[useMemo] mappedImages (after .map()):', mappedImages);
        console.log('[useMemo] mappedImages length:', mappedImages.length);

        const filteredImages = mappedImages.filter(option => option.id && String(option.id).trim() !== '');
        console.log('[useMemo] filteredImages (after .filter()):', filteredImages); // This is the existing log that shows []
        
        return filteredImages;
    }, [rawProductImages, imageBaseUrl]);

    // Consistent image URL construction
    const getImageUrl = (imageId) => imageId ? `/api/data/images/file/${imageId}` : '';

    // Fetch product images for the dropdown
    useEffect(() => {
        const fetchProductImages = async () => {
            setLoadingProducts(true);
            setError(null);
            setRawProductImages([]); // Use renamed setter
            setSelectedProduct(null);
            setResults(null);
            try {
                const params = {
                    image_type: 'product',
                    ip_category: ipCategory,
                };
                console.log('Params for api.listImages:', params); // Debug log for params
                const response = await listImages(params);
                // Enhanced console logging for response.data.images
                console.log('Raw response.data.images from api.listImages (count):', response.data.images ? response.data.images.length : 0);
                if (response.data.images && response.data.images.length > 0) {
                    console.log('First image object from API:', JSON.stringify(response.data.images[0], null, 2));
                    console.log('Keys of first image object from API:', Object.keys(response.data.images[0]));
                }
                // console.log('Raw response.data.images from api.listImages:', response.data.images); // Original Debug log for raw images

                // Set raw product images; transformation is now handled by useMemo
                setRawProductImages(response.data.images || []);
            } catch (err) {
                console.error('Error fetching product images:', err);
                setError(`Failed to load product images for ${ipCategory}. ${err.response?.data?.detail || err.response?.data?.error || err.message}`);
            } finally {
                setLoadingProducts(false);
            }
        };

        if (ipCategory) {
            fetchProductImages();
        }
    }, [ipCategory]);

    // Fetch results when a product is selected
    useEffect(() => {
        const fetchResultsForProduct = async () => {
            if (!selectedProduct) {
                setResults(null);
                setVisibleCounts({}); // Reset visible counts when product is deselected
                return;
            }
            setLoadingResults(true);
            setError(null);
            try {
                // The API endpoint is /api/results/by-product/{product_image_id}
                // It expects 'limit' as a query parameter.
                const params = { limit: INITIAL_SUGGESTIONS_LIMIT }; // Use the initial fetch limit
                const response = await getResultsByProduct(selectedProduct.id, params);
                // Backend response structure:
                // {
                //   "product_image": { "id": "uuid", "filename": "str", "ip_category": "str" },
                //   "ground_truth_ips": [ { "id": "uuid", "filename": "str", "ip_owner": "str" } ],
                //   "results_by_model": {
                //     "model_name_or_id_1": [ { "ip_image_id": "uuid", "ip_filename": "str", "similarity_score": "float", "is_ground_truth": "bool", "ip_owner": "str" } ],
                //     "model_name_or_id_2": [ ... ]
                //   }
                // }
                setResults(response.data);

                // Initialize visible counts for each model
                if (response.data && response.data.results_by_model) {
                    const initialCounts = {};
                    Object.keys(response.data.results_by_model).forEach(modelName => {
                        initialCounts[modelName] = DEFAULT_VISIBLE_SUGGESTIONS;
                    });
                    setVisibleCounts(initialCounts);
                } else {
                    setVisibleCounts({}); // Reset if no models or data
                }

            } catch (err) {
                console.error(`Error fetching results for product ${selectedProduct.id}:`, err);
                setError(`Failed to load results for product ${selectedProduct.filename}. ${err.response?.data?.detail || err.response?.data?.error || err.message}`);
                setResults(null);
                setVisibleCounts({}); // Reset visible counts on error
            } finally {
                setLoadingResults(false);
            }
        };

        fetchResultsForProduct();
    }, [selectedProduct, INITIAL_SUGGESTIONS_LIMIT, DEFAULT_VISIBLE_SUGGESTIONS]); // Add constants to dependency array

    const handleMarkAsGroundTruth = useCallback(async (suggestedIpImageId, isCurrentlyGroundTruth) => {
        if (!selectedProduct || !selectedProduct.id) return;
        setError(null);
        try {
            if (isCurrentlyGroundTruth) {
                await removeGroundTruth(selectedProduct.id, suggestedIpImageId);
            } else {
                await addGroundTruth(selectedProduct.id, suggestedIpImageId);
            }
            // Refetch results with the initial limit to get updated ground truth status
            const params = { limit: INITIAL_SUGGESTIONS_LIMIT }; // Use the initial fetch limit
            const response = await getResultsByProduct(selectedProduct.id, params);
            setResults(response.data);
            // Note: visibleCounts state is intentionally NOT reset here,
            // so the "show more" state is preserved after marking ground truth.
        } catch (err) {
            console.error('Error updating ground truth:', err);
            setError(`Failed to update ground truth. ${err.response?.data?.detail || err.response?.data?.error || err.message}`);
        }
    }, [selectedProduct, INITIAL_SUGGESTIONS_LIMIT]); // Add constant to dependency array

    const handleImageClick = (imageUrl) => {
        setImageToEnlarge(imageUrl);
    };

    const handleCloseDialog = () => {
        setImageToEnlarge(null);
    };

    // suggestion object: { ip_image_id, ip_filename, similarity_score, is_ground_truth, ip_owner }
    const renderSuggestion = (suggestion) => (
        <Card sx={{ display: 'flex', mb: 1, alignItems: 'center', position: 'relative', width: '100%' }}>
            {suggestion.is_ground_truth && (
                <Tooltip title="Known Correct Match">
                    <CheckCircleIcon color="success" sx={{ position: 'absolute', top: 4, left: 4, zIndex: 1, backgroundColor: 'white', borderRadius: '50%' }} />
                </Tooltip>
            )}
            <CardMedia
                component="img"
                sx={{ width: 80, height: 80, objectFit: 'contain', cursor: 'pointer', p: 0.5 }}
                image={getImageUrl(suggestion.ip_image_id)} // Use ip_image_id
                alt={suggestion.ip_filename || 'IP Image'} // Use ip_filename
                onClick={() => handleImageClick(getImageUrl(suggestion.ip_image_id))}
            />
            <Box sx={{ display: 'flex', flexDirection: 'column', flexGrow: 1, justifyContent: 'center', pl: 1 }}>
                <Typography variant="caption" sx={{ wordBreak: 'break-all' }}>
                    {suggestion.ip_filename || 'N/A'} {/* Use ip_filename */}
                </Typography>
                <Typography variant="caption">
                    Score: {suggestion.similarity_score?.toFixed(4) ?? 'N/A'} {/* Use similarity_score */}
                </Typography>
                 <Typography variant="caption">
                    Owner: {suggestion.ip_owner || 'N/A'}
                </Typography>
            </Box>
             <Tooltip title={suggestion.is_ground_truth ? "Remove from Ground Truth" : "Mark as Ground Truth"}>
                <IconButton
                    size="small"
                    onClick={() => handleMarkAsGroundTruth(suggestion.ip_image_id, suggestion.is_ground_truth)}
                    color={suggestion.is_ground_truth ? "error" : "success"}
                    sx={{ alignSelf: 'center', mr: 1 }}
                >
                    {suggestion.is_ground_truth ? <RemoveCircleOutlineIcon /> : <AddTaskIcon />}
                </IconButton>
            </Tooltip>
        </Card>
    );

    return (
        <Box sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>Results by Product Picture</Typography>

            {loadingProducts && <CircularProgress size={24} />}
            {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

            <Autocomplete
                options={productImagesForAutocomplete} // Use memoized and transformed images
                getOptionLabel={(option) => option.label || ""}
                value={selectedProduct}
                onChange={(event, newValue) => {
                    setSelectedProduct(newValue);
                }}
                isOptionEqualToValue={(option, value) => option.id === value.id}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        label={`Select Product Image (${ipCategory})`}
                        variant="outlined"
                        size="small"
                        disabled={loadingProducts || productImagesForAutocomplete.length === 0} // Use memoized images for disabled check
                    />
                )}
                renderOption={(htmlProps, option) => {
                    // Destructure 'key' from htmlProps to prevent spreading it if it exists,
                    // as this can cause React warnings. The actual React key for the list item
                    // should be option.id.
                    const { key: muiProvidedKey, ...otherHtmlProps } = htmlProps;
                    return (
                        <Box component="li" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...otherHtmlProps} key={option.id}>
                            <img
                                loading="lazy"
                                width="40"
                                // option.imageUrl should be valid due to filtering,
                                // but pass null if it's somehow empty to avoid empty src warning.
                                src={option.imageUrl || null}
                                alt={option.filename || 'Product thumbnail'} // Fallback for alt text
                            />
                            {option.label || 'Unnamed Product'} ({option.id ? option.id.substring(0, 8) : 'N/A'}...)
                        </Box>
                    );
                }}
                sx={{ mb: 2, maxWidth: 500 }}
                disabled={loadingProducts}
            />

            {loadingResults && <CircularProgress />}

            {selectedProduct && results && !loadingResults && (
                <Grid container spacing={3}>
                    {/* Selected Product Image - uses results.product_image */}
                    <Grid item xs={12} md={3}>
                        <Typography variant="subtitle1" gutterBottom>Selected Product</Typography>
                        <Card>
                            <CardMedia
                                component="img"
                                sx={{ height: 200, objectFit: 'contain', cursor: 'pointer' }}
                                image={getImageUrl(results.product_image?.id)}
                                alt={`${results.product_image?.filename || 'N/A'} - ${results.product_image?.ip_category || 'N/A'}`}
                                onClick={() => results.product_image?.id && handleImageClick(getImageUrl(results.product_image.id))}
                            />
                            <CardContent>
                                <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>{results.product_image?.filename || 'N/A'}</Typography>
                                <Typography variant="caption" display="block" sx={{ fontStyle: 'italic', mt: 0.5 }}>
                                    Category: {results.product_image?.ip_category || 'N/A'}
                                </Typography>
                            </CardContent>
                         {/* Known Correct Matches Section - uses results.ground_truth_ips */}
                         {results.ground_truth_ips && results.ground_truth_ips.length > 0 && (
                            <Box mt={2}>
                                <Typography variant="subtitle1" gutterBottom>Known Correct Matches</Typography>
                                <Grid container spacing={1}>
                                    {/* Each item in ground_truth_ips is an IPImageSchema */}
                                    {results.ground_truth_ips.map((gtImage) => (
                                        <Grid item key={gtImage.id} xs={6} sm={4} md={6}>
                                            <Card sx={{ position: 'relative', textAlign: 'center', p: 1 }}>
                                                <Tooltip title={gtImage.filename || 'Ground Truth Image'}>
                                                    <CardMedia
                                                        component="img"
                                                        sx={{ height: 80, objectFit: 'contain', cursor: 'pointer', margin: '0 auto' }}
                                                        image={getImageUrl(gtImage.id)}
                                                        alt={gtImage.filename || 'Ground Truth'}
                                                        onClick={() => handleImageClick(getImageUrl(gtImage.id))}
                                                    />
                                                </Tooltip>
                                                <Typography variant="body2" sx={{ mt: 1, wordBreak: 'break-all' }}>
                                                    {gtImage.filename || 'N/A'}
                                                </Typography>
                                                <IconButton
                                                    onClick={() => handleMarkAsGroundTruth(gtImage.id, true)}
                                                    sx={{
                                                        mt: 1,
                                                        color: 'error.main',
                                                        border: '1px solid',
                                                        borderColor: 'error.main',
                                                        borderRadius: '50%',
                                                        width: 36,
                                                        height: 36,
                                                        display: 'inline-flex',
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                    }}
                                                >
                                                    <ClearIcon />
                                                </IconButton>
                                            </Card>
                                        </Grid>
                                    ))}
                                </Grid>
                            </Box>
                        )}
                        </Card>
                    </Grid>

                    {/* Results Area - uses results.results_by_model */}
                    <Grid item xs={12} md={9}>
                        <Typography variant="subtitle1" gutterBottom>Model Suggestions</Typography>
                        {results.results_by_model && Object.entries(results.results_by_model).length > 0 ? (
                            Object.entries(results.results_by_model).map(([modelName, suggestions]) => (
                                <Box key={modelName} mb={3}>
                                    <Typography variant="h6" component="div" gutterBottom>{modelName}</Typography>
                                    {suggestions && suggestions.length > 0 ? (
                                        <>
                                            <Grid container spacing={1}>
                                                {/* Determine how many suggestions to show for this model */}
                                                {suggestions.slice(0, visibleCounts[modelName] || DEFAULT_VISIBLE_SUGGESTIONS).map((suggestion) => (
                                                    <Grid item xs={12} sm={6} md={4} key={`${modelName}-${suggestion.ip_image_id}`}>
                                                        {renderSuggestion(suggestion)}
                                                    </Grid>
                                                ))}
                                            </Grid>
                                            {/* Show "Show More" button if there are more suggestions to display */}
                                            {(visibleCounts[modelName] || DEFAULT_VISIBLE_SUGGESTIONS) < suggestions.length && (
                                                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, width: '100%' }}>
                                                    <Button
                                                        variant="outlined"
                                                        onClick={() => {
                                                            setVisibleCounts(prevCounts => ({
                                                                ...prevCounts,
                                                                [modelName]: (prevCounts[modelName] || DEFAULT_VISIBLE_SUGGESTIONS) + DEFAULT_VISIBLE_SUGGESTIONS
                                                            }));
                                                        }}
                                                    >
                                                        Show {DEFAULT_VISIBLE_SUGGESTIONS} More Suggestions
                                                    </Button>
                                                </Box>
                                            )}
                                        </>
                                    ) : (
                                        <Typography variant="body2">No suggestions found for this model.</Typography>
                                    )}
                                </Box>
                            ))
                        ) : (
                             <Alert severity="info">No model results available for this product image.</Alert>
                        )}
                    </Grid>
                </Grid>
            )}

            {/* Image Enlarge Dialog */}
            <Dialog open={Boolean(imageToEnlarge)} onClose={handleCloseDialog} maxWidth="md">
                <DialogTitle>Enlarged Image</DialogTitle>
                <DialogContent>
                    <img src={imageToEnlarge} alt="Enlarged view" style={{ width: '100%', height: 'auto' }} />
                </DialogContent>
                <Button onClick={handleCloseDialog} sx={{ m: 1 }}>Close</Button>
            </Dialog>
        </Box>
    );
};

export default ByProductView;