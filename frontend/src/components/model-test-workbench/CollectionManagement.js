import React, { useState, useEffect } from 'react';
import { getQdrantCollections, deleteQdrantCollection } from '../../services/api_model_workbench';
import {
    Box,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    CircularProgress,
    Alert,
    Button,
    Chip
} from '@mui/material';

const CollectionManagement = () => {
    const [collections, setCollections] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const fetchCollections = async () => {
        setLoading(true);
        setError(null);
        try {
            const data = await getQdrantCollections();
            setCollections(data);
        } catch (err) {
            console.error("Error fetching collections:", err);
            setError('Failed to fetch collections. Please check the console for details.');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchCollections();
    }, []);

    const handleDelete = async (collectionName) => {
        if (window.confirm(`Are you sure you want to delete collection '${collectionName}'? This cannot be undone.`)) {
            try {
                await deleteQdrantCollection(collectionName);
                // Re-fetch collections after successful deletion
                fetchCollections();
                // TODO: Replace alert with a Snackbar notification
                alert(`Collection '${collectionName}' deleted successfully.`);
            } catch (err) {
                console.error(`Error deleting collection ${collectionName}:`, err);
                const errorMsg = `Failed to delete collection '${collectionName}'. Please check the console for details.`;
                setError(errorMsg);
                // TODO: Replace alert with a Snackbar notification
                alert(errorMsg);
            }
        }
    };

    if (loading) {
        return (
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', p: 3 }}>
                <CircularProgress sx={{ mb: 2 }} />
                <Typography>Loading collections...</Typography>
            </Box>
        );
    }

    // Display error prominently if it occurs, even if there are collections loaded previously
    if (error && !loading) { // Show error only after loading finishes to avoid flicker
        return <Alert severity="error" sx={{ mb: 2 }}>Error: {error}</Alert>;
    }


    return (
        <Box sx={{ mt: 2 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
                Qdrant Collection Management
            </Typography>
            {collections.length === 0 ? (
                <Typography>No collections found.</Typography>
            ) : (
                <TableContainer component={Paper}>
                    <Table sx={{ minWidth: 650 }} aria-label="collection management table" size="small">
                        <TableHead>
                            <TableRow>
                                <TableCell>Name</TableCell>
                                <TableCell>IP Category</TableCell>
                                <TableCell>Model Name</TableCell>
                                <TableCell>Status</TableCell>
                                <TableCell>Data Count</TableCell>
                                <TableCell>Actions</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {collections.map((collection) => (
                                <TableRow
                                    key={collection.name}
                                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                                >
                                    <TableCell component="th" scope="row">
                                        {collection.name}
                                    </TableCell>
                                    <TableCell>{collection.ip_category || 'N/A'}</TableCell>
                                    <TableCell>{collection.model_name || 'N/A'}</TableCell>
                                    <TableCell>
                                        <Chip
                                            label={collection.is_active ? 'Active' : 'Orphaned'}
                                            color={collection.is_active ? 'success' : 'error'}
                                            size="small"
                                            sx={{ mr: 1 }}
                                        />
                                        <Chip
                                            label={collection.needs_migration ? 'Needs Migration: Yes' : 'Needs Migration: No'}
                                            color={collection.needs_migration ? 'warning' : 'default'}
                                            size="small"
                                        />
                                    </TableCell>
                                    <TableCell>{collection.points_count !== null && collection.points_count !== undefined ? collection.points_count : 'N/A'}</TableCell>
                                    <TableCell>
                                        <Button
                                            variant="contained"
                                            color="error"
                                            size="small"
                                            onClick={() => handleDelete(collection.name)}
                                        >
                                            Delete
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            )}
        </Box>
    );
};

export default CollectionManagement;