import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  TextField,
  Pagination,
  Grid, // Added for layout
  TableSortLabel, // Added for sorting
  LinearProgress, // Added for subtle loading indication
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import RefreshIcon from '@mui/icons-material/Refresh';
import { visuallyHidden } from '@mui/utils'; // Added for sorting accessibility
import { listImages, updateImageIpOwner, deleteImage } from '../../services/api_model_workbench';

// Helper to format date string
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleString();
  } catch (e) {
    return dateString; // Return original if parsing fails
  }
};

const ITEMS_PER_PAGE = 20;

const headCells = [
  { id: 'thumbnail', numeric: false, disablePadding: true, label: 'Thumbnail', sortable: false },
  { id: 'original_filename', numeric: false, disablePadding: false, label: 'Filename', sortable: true },
  { id: 'image_type', numeric: false, disablePadding: false, label: 'Type', sortable: true },
  { id: 'ip_category', numeric: false, disablePadding: false, label: 'IP', sortable: true },
  { id: 'ip_owner', numeric: false, disablePadding: false, label: 'IP Owner', sortable: true },
  { id: 'created_at', numeric: false, disablePadding: false, label: 'Created At', sortable: true },
  { id: 'actions', numeric: false, disablePadding: false, label: 'Actions', sortable: false },
];

// --- Hardcoded Dropdown Options ---
const HARDCODED_IP_CATEGORIES = [
  { value: '', label: 'All IP Categories' },
  { value: 'trademark', label: 'Trademark' },
  { value: 'copyright', label: 'Copyright' },
  { value: 'patent', label: 'Patent' },
];

const HARDCODED_IMAGE_TYPES = [
  { value: '', label: 'All Image Types' },
  { value: 'product', label: 'Product' },
  { value: 'ip', label: 'IP' },
];

// Debounce utility function
const debounce = (func, delay) => {
  let timeout;
  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), delay);
  };
};

const ImageBrowser = () => {
  const [images, setImages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Pagination state
  const [totalPagesApi, setTotalPagesApi] = useState(0);   // API's total pages
  const [currentPageUi, setCurrentPageUi] = useState(1);   // UI's current page (for Pagination component)

  // State for Edit Dialog
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingImage, setEditingImage] = useState(null);
  const [newIpOwner, setNewIpOwner] = useState('');
  const [editLoading, setEditLoading] = useState(false);
  const [editError, setEditError] = useState('');

  // State for Delete Dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deletingImageId, setDeletingImageId] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteError, setDeleteError] = useState('');

  // Filter state
  const [filenameFilter, setFilenameFilter] = useState(''); // For immediate input display
  const [debouncedFilenameFilter, setDebouncedFilenameFilter] = useState(''); // For API calls
  const [ipFilter, setIpFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');

  // State for dropdown options (now using hardcoded values)
  const ipCategoryOptions = HARDCODED_IP_CATEGORIES;
  const imageTypeOptions = HARDCODED_IMAGE_TYPES;

  // Sorting state
  const [order, setOrder] = useState('desc');
  const [orderBy, setOrderBy] = useState('created_at');


  const fetchData = useCallback(async (page, currentFilters, currentSortConfig) => {
    setIsLoading(true);
    setError('');
    try {
      const params = {
        page,
        per_page: ITEMS_PER_PAGE,
        original_filename: currentFilters.debouncedFilenameFilter || undefined, // Use debounced value
        ip_category: currentFilters.ipFilter || undefined,
        image_type: currentFilters.typeFilter || undefined,
        sort_by: currentSortConfig.orderBy || undefined,
        sort_order: currentSortConfig.orderBy ? (currentSortConfig.order === 'desc' ? 'desc' : 'asc') : undefined,
      };
      // console.log("ImageBrowser: Fetching data with params:", params);
      const response = await listImages(params);
      // console.log('ImageBrowser API response.data:', response.data);
      const apiData = response.data;
      if (apiData && typeof apiData === 'object') {
        const imageList = apiData.images;
        const paginationData = apiData.pagination;

        if (Array.isArray(imageList)) {
          setImages(imageList);
        } else {
          console.error('ImageBrowser: response.data.images is not an array. Received:', imageList);
          setImages([]);
        }

        if (paginationData && typeof paginationData === 'object') {
          setTotalPagesApi(paginationData.total_pages || 0);
        } else {
          console.error('ImageBrowser: response.data.pagination is not a valid object or is missing. Received:', paginationData);
          setTotalPagesApi(0);
        }
      } else {
        console.error('ImageBrowser: response.data is not a valid object or is missing. Received:', apiData);
        setImages([]);
        setTotalPagesApi(0);
      }
    } catch (err) {
      console.error("ImageBrowser: Failed to fetch images. Error object:", err);
      setError(err.response?.data?.detail || err.message || 'Failed to fetch images.');
      setImages([]);
      setTotalPagesApi(0);
    } finally {
      setIsLoading(false);
    }
  }, []); // fetchData itself doesn't depend on changing state, its callers do.

  useEffect(() => {
    // Use debouncedFilenameFilter for the API call
    const currentFilters = { debouncedFilenameFilter, ipFilter, typeFilter };
    const currentSortConfig = { orderBy, order };
    fetchData(currentPageUi, currentFilters, currentSortConfig);
  }, [fetchData, refreshTrigger, currentPageUi, debouncedFilenameFilter, ipFilter, typeFilter, orderBy, order]);


  // Debounced function to update the filter used for API calls
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const updateDebouncedFilenameFilter = useCallback(
    debounce((value) => {
      setDebouncedFilenameFilter(value);
      setCurrentPageUi(1); // Reset to first page when debounced search term changes
    }, 500), // 500ms delay
    []
  );

  const handleFilenameChange = (event) => {
    const { value } = event.target;
    setFilenameFilter(value); // Update input field immediately
    updateDebouncedFilenameFilter(value); // Call debounced function to update API filter
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Specific handler for IP and Type filters (non-debounced)
  const handleSelectFilterChange = (setter) => (event) => {
    setter(event.target.value);
    setCurrentPageUi(1); // Reset to first page on filter change
  };


  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
    setCurrentPageUi(1); // Reset to first page on sort change
  };

  // --- Edit IP Owner Logic ---
  const handleOpenEditDialog = (image) => {
    setEditingImage({ id: image.id, currentOwner: image.ip_owner });
    setNewIpOwner(image.ip_owner || '');
    setEditError('');
    setEditDialogOpen(true);
  };

  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
    setEditingImage(null);
    setNewIpOwner('');
    setEditError('');
  };

  const handleUpdateIpOwner = async () => {
    if (!editingImage || !newIpOwner.trim()) {
      setEditError('IP Owner cannot be empty.');
      return;
    }
    setEditLoading(true);
    setEditError('');
    try {
      await updateImageIpOwner(editingImage.id, newIpOwner.trim());
      handleCloseEditDialog();
      handleRefresh();
    } catch (err) {
      console.error("Failed to update IP owner:", err);
      setEditError(err.response?.data?.detail || err.message || 'Failed to update IP owner.');
    } finally {
      setEditLoading(false);
    }
  };

  // --- Delete Image Logic ---
  const handleOpenDeleteDialog = (imageId) => {
    console.log("handleOpenDeleteDialog entered. imageId:", imageId);
    setDeletingImageId(imageId);
    setDeleteError('');
    setDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeletingImageId(null);
    setDeleteError('');
  };

  const handleDeleteImage = async () => {
    console.log("handleDeleteImage entered. deletingImageId:", deletingImageId);
    if (!deletingImageId) return;
    setDeleteLoading(true);
    setDeleteError('');
    try {
      console.log(`Attempting to delete image with ID: ${deletingImageId}`);
      await deleteImage(deletingImageId);
      console.log(`Delete API call successful for image ID: ${deletingImageId}`);
      handleCloseDeleteDialog();
      handleRefresh();
    } catch (err) {
      console.error(`Delete API call failed for image ID: ${deletingImageId}`, err);
      setDeleteError(err.response?.data?.detail || err.message || 'Failed to delete image.');
    } finally {
      setDeleteLoading(false);
    }
  };

  const handlePageChange = (event, value) => {
    setCurrentPageUi(value);
  };

  return (
    <Box sx={{ mt: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Image Browser & Metadata</Typography>
        <Tooltip title="Refresh List">
          <span>
            <IconButton onClick={handleRefresh} disabled={isLoading}>
              <RefreshIcon />
            </IconButton>
          </span>
        </Tooltip>
      </Box>

      {/* Filter Inputs */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="subtitle1" gutterBottom>Filters</Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Filename"
              variant="outlined"
              size="small"
              value={filenameFilter} // TextField displays the immediate value
              onChange={handleFilenameChange} // Use the new handler for debouncing
              disabled={isLoading}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small" variant="outlined" disabled={isLoading} sx={{ minWidth: '160px' }}>
              <InputLabel id="ip-category-filter-label">IP Category</InputLabel>
              <Select
                labelId="ip-category-filter-label"
                id="ip-category-filter"
                value={ipFilter}
                label="IP Category"
                onChange={handleSelectFilterChange(setIpFilter)} // Use non-debounced for select
              >
                {ipCategoryOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small" variant="outlined" disabled={isLoading} sx={{ minWidth: '160px' }}>
              <InputLabel id="image-type-filter-label">Image Type</InputLabel>
              <Select
                labelId="image-type-filter-label"
                id="image-type-filter"
                value={typeFilter}
                label="Image Type"
                onChange={handleSelectFilterChange(setTypeFilter)} // Use non-debounced for select
              >
                {imageTypeOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      {isLoading && !images.length ? ( // Show main loader only if no images are displayed yet
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <TableContainer component={Paper} sx={{ position: 'relative' }}>
            {isLoading && <LinearProgress sx={{position: 'absolute', top: 0, width: '100%'}} /> }
            <Table sx={{ minWidth: 650 }} aria-label="image browser table" size="small">
              <TableHead>
                <TableRow>
                  {headCells.map((headCell) => (
                    <TableCell
                      key={headCell.id}
                      align={headCell.numeric ? 'right' : 'left'}
                      padding={headCell.disablePadding ? 'none' : 'normal'}
                      sortDirection={orderBy === headCell.id ? order : false}
                    >
                      {headCell.sortable ? (
                        <TableSortLabel
                          active={orderBy === headCell.id}
                          direction={orderBy === headCell.id ? order : 'asc'}
                          onClick={(event) => handleRequestSort(event, headCell.id)}
                          disabled={isLoading}
                        >
                          {headCell.label}
                          {orderBy === headCell.id ? (
                            <Box component="span" sx={visuallyHidden}>
                              {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                            </Box>
                          ) : null}
                        </TableSortLabel>
                      ) : (
                        headCell.label
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {images.length === 0 && !isLoading && (
                  <TableRow>
                    <TableCell colSpan={headCells.length} align="center">No images found.</TableCell>
                  </TableRow>
                )}
                {images.map((image) => (
                  <TableRow
                    hover
                    key={image.image_id || image.id} // Use image.id as fallback if image_id is missing
                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                  >
                    <TableCell component="th" scope="row" padding="none" sx={{pl: '6px'}}>
                      <img
                        src={`/api/data/images/file/${image.image_id || image.id}`}
                        alt={image.original_filename}
                        style={{ height: '50px', width: 'auto', maxWidth: '70px', objectFit: 'contain', verticalAlign: 'middle', padding: '4px 0' }}
                        onError={(e) => { e.target.style.display = 'none'; }}
                      />
                    </TableCell>
                    <TableCell>{image.original_filename}</TableCell>
                    <TableCell>{image.image_type || 'N/A'}</TableCell>
                    <TableCell>{image.ip_category || 'N/A'}</TableCell>
                    <TableCell>
                      {image.ip_owner || <Typography variant="caption" color="textSecondary">(Missing)</Typography>}
                    </TableCell>
                    <TableCell>{formatDate(image.created_at)}</TableCell>
                    <TableCell>
                      <Tooltip title="Edit IP Owner">
                        <span>
                          <IconButton size="small" onClick={() => handleOpenEditDialog(image)} disabled={editLoading || deleteLoading || isLoading}>
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>
                      <Tooltip title="Delete Image">
                        <span>
                          <IconButton size="small" onClick={() => handleOpenDeleteDialog(image.image_id)} disabled={editLoading || deleteLoading || isLoading}>
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {totalPagesApi > 1 && (
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
              <Pagination
                count={totalPagesApi}
                page={currentPageUi}
                onChange={handlePageChange}
                color="primary"
                showFirstButton
                showLastButton
                disabled={isLoading}
              />
            </Box>
          )}
        </>
      )}

      {/* Edit IP Owner Dialog */}
      <Dialog open={editDialogOpen} onClose={handleCloseEditDialog}>
        <DialogTitle>Edit IP Owner</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Enter the IP Owner for the image: {editingImage?.id}
          </DialogContentText>
          {editError && <Alert severity="error" sx={{ mb: 2 }}>{editError}</Alert>}
          <TextField
            autoFocus
            margin="dense"
            id="ip_owner"
            label="IP Owner"
            type="text"
            fullWidth
            variant="standard"
            value={newIpOwner}
            onChange={(e) => setNewIpOwner(e.target.value)}
            disabled={editLoading}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEditDialog} disabled={editLoading}>Cancel</Button>
          <Button onClick={handleUpdateIpOwner} disabled={editLoading || !newIpOwner.trim()}>
            {editLoading ? <CircularProgress size={20} /> : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{"Confirm Deletion"}</DialogTitle>
        <DialogContent>
          {deleteError && <Alert severity="error" sx={{ mb: 2 }}>{deleteError}</Alert>}
          <DialogContentText id="alert-dialog-description">
            Warning: Deleting this image will permanently remove its data and any associated features or comparison results. This action cannot be undone. Are you sure you want to proceed?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} disabled={deleteLoading}>
            Cancel
          </Button>
          <Button onClick={handleDeleteImage} color="error" autoFocus disabled={deleteLoading}>
            {deleteLoading ? <CircularProgress size={20} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

    </Box>
  );
};

export default ImageBrowser;