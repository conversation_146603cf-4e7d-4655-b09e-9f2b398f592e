import React, { useState } from 'react';
import {
  Box,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
  Alert,
  CircularProgress,
  Input, // Use Input for the file input styling
  List,
  ListItem,
  ListItemText,
  Paper // For displaying errors
} from '@mui/material';
import { uploadImages } from '../../services/api_model_workbench'; // Corrected import path

// Define separate constants
const IP_CATEGORIES = ['trademark', 'copyright', 'patent'];
const IMAGE_TYPES = ['IP', 'Product'];


const ImageUpload = () => {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [selectedIpCategory, setSelectedIpCategory] = useState(''); // New state for IP Category
  const [imageType, setImageType] = useState(''); // Renamed state for Image Type
  const [isLoading, setIsLoading] = useState(false);
  const [feedback, setFeedback] = useState({ type: '', message: '', errors: [] }); // 'success' or 'error', and detailed errors

  const handleFileChange = (event) => {
    console.log("File change event:", event.target.files); // Add logging
    setSelectedFiles(Array.from(event.target.files));
    setFeedback({ type: '', message: '', errors: [] }); // Clear feedback on new selection
  };

  // Handler for the new IP Category dropdown
  const handleIpCategoryChange = (event) => {
    setSelectedIpCategory(event.target.value);
    setFeedback({ type: '', message: '', errors: [] }); // Clear feedback
  };

  // Renamed handler for Image Type dropdown
  const handleImageTypeChange = (event) => {
    setImageType(event.target.value);
    setFeedback({ type: '', message: '', errors: [] }); // Clear feedback
  };


  const handleSubmit = async (event) => {
    event.preventDefault();
    // Updated validation to check both new fields
    if (selectedFiles.length === 0 || !selectedIpCategory || !imageType) {
      setFeedback({ type: 'error', message: 'Please select files, an IP category, and an image type.', errors: [] });
      return;
    }

    setIsLoading(true);
    setFeedback({ type: '', message: '', errors: [] });

    const formData = new FormData();
    selectedFiles.forEach(file => {
      formData.append('files[]', file); // Ensure key matches backend expectation 'files[]'
    });

    // Use state variables directly
    formData.append('ip_category', selectedIpCategory); // Send selected IP category
    formData.append('image_type', imageType.toLowerCase()); // Send selected image type

    try {
      const response = await uploadImages(formData);
      const successes = response.data?.success || [];
      const errors = response.data?.errors || [];
      const successCount = successes.length;

      let message = '';
      let messageType = 'info'; // Default to info for mixed results

      if (successCount > 0 && errors.length === 0) {
        message = `${successCount} image(s) uploaded successfully as ${selectedIpCategory} / ${imageType}.`;
        messageType = 'success';
        setSelectedFiles([]); // Clear selection on full success
        setSelectedIpCategory(''); // Clear IP category on full success
        setImageType(''); // Clear image type on full success
        document.getElementById('image-upload-input').value = null; // Clear file input
      } else if (successCount > 0 && errors.length > 0) {
        message = `${successCount} image(s) uploaded successfully. ${errors.length} image(s) failed. See details below.`;
        messageType = 'warning';
         // Partially clear or keep files? For now, keep them to allow re-attempt or correction.
      } else if (successCount === 0 && errors.length > 0) {
        message = `All ${errors.length} image(s) failed to upload. See details below.`;
        messageType = 'error';
      } else if (response.data?.message) { // General success message from backend
        message = response.data.message;
        messageType = 'success';
        setSelectedFiles([]);
        setSelectedIpCategory('');
        setImageType('');
        document.getElementById('image-upload-input').value = null;
      } else { // Fallback for unexpected response
        message = 'Upload process completed. Check server logs for details if anything is amiss.';
        messageType = 'info';
      }

      setFeedback({ type: messageType, message: message, errors: errors });

    } catch (error) {
      console.error("Image upload failed:", error);
      const errorMessage = error.response?.data?.detail || error.response?.data?.error || error.message || 'Image upload failed. Check console for details.';
      const responseErrors = error.response?.data?.errors || []; // From interceptor or direct
      setFeedback({ type: 'error', message: errorMessage, errors: responseErrors });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2, p: 2, border: '1px dashed grey', borderRadius: 1 }}>
      <Typography variant="h6" gutterBottom>Upload Images</Typography>

      {feedback.message && (
        <Alert severity={feedback.type} sx={{ mb: 2 }}>
          {feedback.message}
        </Alert>
      )}

      {feedback.errors && feedback.errors.length > 0 && (
        <Paper elevation={2} sx={{ p: 2, mb: 2, maxHeight: 200, overflow: 'auto', backgroundColor: (theme) => theme.palette.mode === 'dark' ? '#333' : '#fce4e4' }}>
          <Typography variant="subtitle2" color="error">Upload Errors:</Typography>
          <List dense>
            {feedback.errors.map((err, index) => (
              <ListItem key={index}>
                <ListItemText
                  primary={err.filename || `File ${index + 1}`}
                  secondary={err.error || 'Unknown error'}
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      )}

      {/* New IP Category Dropdown */}
      <FormControl fullWidth margin="normal" required>
        <InputLabel id="ip-category-select-label">IP Category</InputLabel>
        <Select
          labelId="ip-category-select-label"
          id="ip-category-select"
          value={selectedIpCategory}
          label="IP Category"
          onChange={handleIpCategoryChange}
          disabled={isLoading}
        >
          {IP_CATEGORIES.map((cat) => (
            <MenuItem key={cat} value={cat} sx={{ textTransform: 'capitalize' }}>
              {cat}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Modified Image Type Dropdown */}
      <FormControl fullWidth margin="normal" required>
        <InputLabel id="image-type-select-label">Image Type</InputLabel>
        <Select
          labelId="image-type-select-label"
          id="image-type-select"
          value={imageType}
          label="Image Type"
          onChange={handleImageTypeChange}
          disabled={isLoading}
        >
          {IMAGE_TYPES.map((type) => (
            <MenuItem key={type} value={type}>{type}</MenuItem>
          ))}
        </Select>
      </FormControl>

      <FormControl fullWidth margin="normal">
         {/* Use a styled button to trigger the hidden file input */}
         <Button
            variant="contained"
            component="label" // Makes the button act like a label for the input
            disabled={isLoading}
          >
            Select Images
            {/* Use a standard hidden input for reliable multiple file selection */}
            <input
              type="file"
              id="image-upload-input" // Added ID for clearing
              multiple // Directly apply the multiple attribute
              hidden // Hide the default input appearance
              onChange={handleFileChange}
            />
          </Button>
      </FormControl>

      {selectedFiles.length > 0 && (
        <Box sx={{ my: 2 }}>
          <Typography variant="subtitle1">Selected Files:</Typography>
          <List dense>
            {selectedFiles.map((file, index) => (
              <ListItem key={index}>
                <ListItemText primary={file.name} secondary={`${(file.size / 1024).toFixed(2)} KB`} />
              </ListItem>
            ))}
          </List>
        </Box>
      )}


      <Button
        type="submit"
        variant="contained"
        color="primary"
        disabled={isLoading || selectedFiles.length === 0 || !selectedIpCategory || !imageType}
        sx={{ mt: 2 }}
      >
        {isLoading ? <CircularProgress size={24} /> : 'Upload'}
      </Button>
    </Box>
  );
};

export default ImageUpload;