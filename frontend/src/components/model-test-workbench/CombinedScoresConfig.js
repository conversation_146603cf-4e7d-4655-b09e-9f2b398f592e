import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper,
  CircularProgress, Alert, Button, IconButton, Tooltip, Dialog, DialogActions, DialogContent,
  DialogContentText, DialogTitle, TextField, Select, MenuItem, FormControl, InputLabel, List, ListItem, ListItemText,
  Snackbar, Chip, Switch, FormControlLabel
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import RefreshIcon from '@mui/icons-material/Refresh';
import {
  getCombinedScores, createCombinedScoreConfig, updateCombinedScoreConfig, deleteCombinedScoreConfig, listModels,
  triggerComputeCombinedScores // Import the new function
} from '../../services/api_model_workbench';

const IP_CATEGORIES = ["trademark", "copyright", "patent"]; // Consistent casing with backend

const CombinedScoresConfig = () => {
  const [configs, setConfigs] = useState([]);
  const [models, setModels] = useState([]); // Stores { id, name, type, applicable_categories, ... }
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Dialog State
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentConfig, setCurrentConfig] = useState(null); // For editing
  const [editingConfigId, setEditingConfigId] = useState(null); // Store ID separately for update
  const [configName, setConfigName] = useState('');
  const [configCategory, setConfigCategory] = useState('');
  const [modelWeights, setModelWeights] = useState({}); // { model_uuid: weight }
  const [isActive, setIsActive] = useState(true); // For is_active status
  const [dialogLoading, setDialogLoading] = useState(false);
  const [dialogError, setDialogError] = useState('');

  // Delete Dialog State
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deletingConfigId, setDeletingConfigId] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteError, setDeleteError] = useState('');

  // Snackbar State
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');

  const fetchConfigs = useCallback(async () => {
    setIsLoading(true);
    setError('');
    try {
      const response = await getCombinedScores(); // Assuming GET /api/combined-scores
      // Backend sends: { id, config_name, ip_category, model_weights: {uuid: weight}, is_active }
      setConfigs(response.data || []);
    } catch (err) {
      handleApiError(err, 'Failed to fetch combined score configurations.');
      setConfigs([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchModels = useCallback(async () => {
    try {
      const response = await listModels();
      // Backend sends: { id (uuid), name, type, applicable_categories, active }
      setModels(response.data || []);
    } catch (err) {
      handleApiError(err, 'Failed to fetch models for configuration.');
      setModels([]);
    }
  }, []);

  useEffect(() => {
    fetchConfigs();
    fetchModels();
  }, [fetchConfigs, fetchModels, refreshTrigger]);

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleOpenCreateDialog = () => {
    setIsEditing(false);
    setCurrentConfig(null);
    setConfigName('');
    setConfigCategory('');
    setModelWeights({});
    setIsActive(true);
    setDialogError('');
    setDialogOpen(true);
    if (models.length === 0) fetchModels();
  };

  const handleOpenEditDialog = (config) => {
    setIsEditing(true);
    setCurrentConfig(config);
    setEditingConfigId(config.config_id); // Set the editing ID
    setConfigName(config.config_name || config.name || ''); // Prefer config_name
    setConfigCategory(config.ip_category);
    setModelWeights(config.model_weights || {}); // Expects { uuid: weight }
    setIsActive(typeof config.is_active === 'boolean' ? config.is_active : true);
    setDialogError('');
    setDialogOpen(true);
    if (models.length === 0) fetchModels();
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setTimeout(() => {
      setCurrentConfig(null);
      setConfigName('');
      setConfigCategory('');
      setModelWeights({});
      setIsActive(true);
      setDialogError('');
      setDialogLoading(false);
      setEditingConfigId(null); // Reset editing ID
    }, 200);
  };

  const handleWeightChange = (modelId, value) => { // modelId is UUID
    // Store the raw input value as a string
    setModelWeights(prev => ({
      ...prev,
      [modelId]: value
    }));
  };
  
  const handleIsActiveChange = (event) => {
    setIsActive(event.target.checked);
  };

  const handleDialogSubmit = async () => {
    setDialogLoading(true);
    setDialogError('');

    const processedModelWeights = Object.entries(modelWeights)
      .reduce((obj, [uuid, weightStr]) => {
        const weight = parseFloat(weightStr);
        if (!isNaN(weight)) {
          const clampedWeight = Math.max(0, Math.min(1, weight));
          if (clampedWeight > 0) {
            obj[uuid] = parseFloat(clampedWeight.toFixed(4)); // Store with fixed precision
          }
        }
        return obj;
      }, {});

    if (Object.keys(processedModelWeights).length === 0) {
      setDialogError('At least one model must have a weight greater than 0.');
      setDialogLoading(false);
      return;
    }

    // Optional: Validate sum of weights if backend requires it (e.g., sum to 1)
    // const totalWeight = Object.values(processedModelWeights).reduce((sum, weight) => sum + weight, 0);
    // if (Math.abs(totalWeight - 1.0) > 0.001) { /* error */ }


    const configData = {
      config_name: configName.trim(), // Use config_name for payload
      ip_category: configCategory,
      model_weights: processedModelWeights, // Object { uuid: weight }
      is_active: isActive,
    };

    try {
      if (isEditing && currentConfig) {
        // For PUT, ip_category is removed by api.js if present in configData
        await updateCombinedScoreConfig(editingConfigId, configData); // Use editingConfigId
        showSnackbar('Configuration updated successfully.', 'success');
        // Trigger combined score computation after update
        await triggerComputeCombinedScores(configCategory);
        showSnackbar(`Combined score computation triggered for ${configCategory}.`, 'info');
      } else {
        await createCombinedScoreConfig(configData);
        showSnackbar('Configuration created successfully.', 'success');
        // Trigger combined score computation after creation
        await triggerComputeCombinedScores(configCategory);
        showSnackbar(`Combined score computation triggered for ${configCategory}.`, 'info');
      }
      handleCloseDialog();
      handleRefresh();
    } catch (err) {
      handleApiError(err, isEditing ? 'Failed to update configuration.' : 'Failed to create configuration.', setDialogError);
    } finally {
      setDialogLoading(false);
    }
  };

  const handleOpenDeleteDialog = (configId) => {
    setDeletingConfigId(configId);
    setDeleteError('');
    setDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    console.log('Closing delete dialog.'); // Added log
    setDeleteDialogOpen(false);
    setDeletingConfigId(null);
    setDeleteError('');
    setDeleteLoading(false);
  };

  const handleDeleteConfig = async (configIdToDelete) => {
    setDeleteLoading(true);
    setDeleteError('');
    try {
      await deleteCombinedScoreConfig(configIdToDelete);
      showSnackbar('Configuration deleted successfully.', 'success');
      handleCloseDeleteDialog();
      handleRefresh();
    } catch (err) {
      handleApiError(err, 'Failed to delete configuration.', setDeleteError);
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleApiError = (err, defaultMessage, specificErrorSetter = setError) => {
    console.error(defaultMessage, err);
    const message = err.response?.data?.error || err.response?.data?.detail || err.message || defaultMessage;
    specificErrorSetter(message);
    if (specificErrorSetter === setError) {
      showSnackbar(message, 'error');
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = (event, reason) => {
    if (reason === 'clickaway') return;
    setSnackbarOpen(false);
  };

  const applicableModels = models.filter(model =>
    model.is_active && (
      model.applicable_ip_category?.includes(configCategory.toLowerCase()) ||
      model.applicable_ip_category?.includes('all')
    )
  );

  const processedModelWeights = React.useMemo(() => {
    return Object.entries(modelWeights)
      .filter(([, weightStr]) => {
        const weightNum = parseFloat(weightStr);
        return !isNaN(weightNum) && weightNum > 0;
      })
      .reduce((obj, [uuid, weightStr]) => {
        obj[uuid] = parseFloat(weightStr);
        return obj;
      }, {});
  }, [modelWeights]);

  return (
    <Box sx={{ mt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Combined Score Configurations</Typography>
        <Box>
           <Tooltip title="Refresh List">
              <IconButton onClick={handleRefresh} disabled={isLoading || dialogOpen}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
           <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleOpenCreateDialog}
              disabled={isLoading || dialogOpen}
              sx={{ ml: 1 }}
            >
              Create New
            </Button>
        </Box>
      </Box>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}><CircularProgress /></Box>
      ) : (
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>IP Category</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Model Weights</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {configs.length === 0 && !isLoading && (
                <TableRow><TableCell colSpan={5} align="center">No configurations found.</TableCell></TableRow>
              )}
              {configs.map((config) => {
                console.log('Rendering config:', config); // Added log
                const displayWeights = config.model_weights ? Object.entries(config.model_weights).map(([uuid, weight]) => {
                  const model = models.find(m => m.id === uuid);
                  return { name: model ? model.name : `UUID: ${uuid.substring(0,8)}...`, weight: weight };
                }) : [];

                return (
                  <TableRow key={config.config_id}> {/* Use config_id for key */}
                    <TableCell>{config.config_name || config.name}</TableCell>
                    <TableCell sx={{ textTransform: 'capitalize' }}>{config.ip_category}</TableCell>
                    <TableCell>
                        <Chip label={config.is_active ? "Active" : "Inactive"} color={config.is_active ? "success" : "default"} size="small" />
                    </TableCell>
                    <TableCell>
                      {displayWeights.length > 0 ? displayWeights.map(mw => (
                        <Chip key={mw.name} label={`${mw.name}: ${Number(mw.weight).toFixed(2)}`} size="small" sx={{ mr: 0.5, mb: 0.5 }}/>
                      )) : 'N/A'}
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Edit Configuration">
                        <IconButton size="small" onClick={() => handleOpenEditDialog(config)} disabled={dialogOpen}>
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Configuration">
                        <IconButton size="small" onClick={() => handleOpenDeleteDialog(config.config_id)} disabled={dialogOpen} sx={{ pointerEvents: 'auto' }}> {/* Use config.config_id */}
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>{isEditing ? 'Edit Configuration' : 'Create New Configuration'}</DialogTitle>
        <DialogContent>
          {dialogError && <Alert severity="error" sx={{ mb: 2 }}>{dialogError}</Alert>}
          <TextField
            autoFocus
            margin="dense"
            id="config-name"
            label="Configuration Name"
            type="text"
            fullWidth
            variant="outlined"
            value={configName}
            onChange={(e) => setConfigName(e.target.value)}
            disabled={dialogLoading}
            sx={{ mb: 2 }}
            required
          />
          <FormControl fullWidth margin="dense" variant="outlined" disabled={dialogLoading || isEditing} sx={{ mb: 2 }} required>
             <InputLabel id="config-category-label">IP Category</InputLabel>
             <Select
                labelId="config-category-label"
                id="config-category"
                value={configCategory}
                label="IP Category"
                onChange={(e) => {
                    setConfigCategory(e.target.value);
                    setModelWeights({}); // Reset weights when category changes
                }}
             >
               {IP_CATEGORIES.map(cat => <MenuItem key={cat} value={cat} sx={{ textTransform: 'capitalize' }}>{cat}</MenuItem>)}
             </Select>
          </FormControl>

          <FormControlLabel
            control={<Switch checked={isActive} onChange={handleIsActiveChange} disabled={dialogLoading} />}
            label="Active Configuration"
            sx={{ mb: 1, mt:1 }}
          />

          {configCategory && (
            <>
              <Typography variant="subtitle1" sx={{ mt: 2, mb: 1 }}>Model Weights (0.0 - 1.0)</Typography>
              {applicableModels.length === 0 && models.length > 0 && (
                 <Alert severity="warning" sx={{ mb: 2 }}>No active models found for the selected category '{configCategory}'.</Alert>
              )}
              {applicableModels.length === 0 && models.length === 0 && !isLoading && (
                 <Alert severity="error" sx={{ mb: 2 }}>Could not load models. Please try refreshing.</Alert>
              )}
              <List dense sx={{maxHeight: 300, overflow: 'auto'}}>
                {applicableModels.map(model => ( // model.id is the UUID
                  <ListItem key={model.id} disablePadding sx={{ mb: 1.5, display: 'flex', justifyContent: 'space-between' }}>
                     <ListItemText primary={model.model_name} secondary={model.model_type} sx={{ mr: 2, flexGrow: 1}}/>
                     <TextField
                        label="Weight"
                        id={`weight-${model.model_id}`}
                        value={modelWeights[model.model_id] || ''}
                        onChange={(e) => handleWeightChange(model.model_id, e.target.value)}
                        type="number"
                        inputProps={{ step: "0.01", min: "0", max: "1" }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '120px' }}
                        disabled={dialogLoading}
                     />
                  </ListItem>
                ))}
              </List>
            </>
          )}

        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={dialogLoading}>Cancel</Button>
          <Button
             onClick={handleDialogSubmit}
             disabled={dialogLoading || !configName || !configCategory || (applicableModels.length > 0 && Object.keys(processedModelWeights).length === 0) || (isEditing && !editingConfigId)}
             variant="contained"
          >
            {dialogLoading ? <CircularProgress size={24} /> : (isEditing ? 'Save Changes' : 'Create')}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          {deleteError && <Alert severity="error" sx={{ mb: 2 }}>{deleteError}</Alert>}
          <DialogContentText>
            Are you sure you want to delete this combined score configuration? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} disabled={deleteLoading}>Cancel</Button>
          <Button onClick={() => handleDeleteConfig(deletingConfigId)} color="error" autoFocus disabled={deleteLoading}> {/* Removed console log */}
            {deleteLoading ? <CircularProgress size={20} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleCloseSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CombinedScoresConfig;