import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Button,
  Tooltip,
  Chip, // For displaying categories/status
  Snackbar, // For refresh feedback
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import { listModels, refreshModels } from '../../services/api_model_workbench'; // Use correct function name listModels

const ModelManagement = () => {
  const [models, setModels] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [refreshLoading, setRefreshLoading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success'); // 'success' or 'error'

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setError('');
    try {
      const response = await listModels(); // Use correct function name
      setModels(response.data || []); // Assuming API returns an array directly in data
    } catch (err) {
      console.error("Failed to fetch models:", err);
      setError(err.response?.data?.detail || err.message || 'Failed to fetch models.');
      setModels([]); // Clear models on error
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleRefreshModels = async () => {
    setRefreshLoading(true);
    setError(''); // Clear previous errors
    try {
      const response = await refreshModels();
      setSnackbarMessage(response.data?.message || 'Model list refresh initiated successfully.');
      setSnackbarSeverity('success');
      fetchData(); // Refresh the list after triggering the refresh
    } catch (err) {
      console.error("Failed to refresh models:", err);
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to refresh model list.';
      setError(errorMessage); // Show persistent error if refresh fails badly
      setSnackbarMessage(errorMessage);
      setSnackbarSeverity('error');
    } finally {
      setRefreshLoading(false);
      setSnackbarOpen(true);
    }
  };

  const handleCloseSnackbar = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  return (
    <Box sx={{ mt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Registered Models</Typography>
        <Tooltip title="Refresh model list from configuration">
          <span> {/* Span needed for tooltip when button is disabled */}
            <Button
              variant="outlined"
              startIcon={refreshLoading ? <CircularProgress size={20} /> : <RefreshIcon />}
              onClick={handleRefreshModels}
              disabled={isLoading || refreshLoading}
            >
              Refresh Model List
            </Button>
          </span>
        </Tooltip>
      </Box>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table sx={{ minWidth: 650 }} aria-label="model management table" size="small">
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Applicable Categories</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Active</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {models.length === 0 && !isLoading && (
                <TableRow>
                  <TableCell colSpan={5} align="center">No models registered or found.</TableCell>
                </TableRow>
              )}
              {models.map((model) => (
                <TableRow
                  key={model.model_id}
                  sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                >
                  <TableCell component="th" scope="row">{model.model_name}</TableCell>
                  <TableCell>{model.model_type}</TableCell>
                  <TableCell>
                    {model.applicable_ip_category?.map(cat => (
                      <Chip label={cat} key={cat} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                    )) || 'N/A'}
                  </TableCell>
                  <TableCell>{model.description || 'N/A'}</TableCell>
                  <TableCell>
                    <Chip
                      label={model.is_active ? 'Active' : 'Inactive'}
                      color={model.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ModelManagement;