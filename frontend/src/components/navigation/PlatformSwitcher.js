import React from 'react';
import { Select, MenuItem, FormControl, InputLabel } from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';

// TDD: TEST: PlatformSwitcher renders a dropdown menu
// TDD: TEST: PlatformSwitcher lists all available platforms
// TDD: TEST: PlatformSwitcher correctly identifies and highlights the current platform based on URL
// TDD: TEST: PlatformSwitcher navigates to the correct base path when a new platform is selected

const availablePlatforms = [
  { name: "ModelTestWorkbench", path_prefix: "/", default_sub_route: "/dashboard" }, // Or "" if root has no default sub-route
  { name: "Patent Visualization", path_prefix: "/patent-viz", default_sub_route: "/dashboard" },
  { name: "Bounding Box", path_prefix: "/boundingbox", default_sub_route: "" }
  // Add other platforms here if needed
];

const PlatformSwitcher = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const getCurrentSelectedPlatform = () => {
    for (const platform of availablePlatforms) {
      // Handle the root path case more specifically
      if (platform.path_prefix === "/" && location.pathname === "/") {
        return platform;
      }
      // For other platforms, or if root path has sub-routes
      if (platform.path_prefix !== "/" && location.pathname.startsWith(platform.path_prefix)) {
        return platform;
      }
    }
    // Fallback for root if no other platform matches, or if current path is a sub-route of root
    const rootPlatform = availablePlatforms.find(p => p.path_prefix === "/");
    if (rootPlatform && location.pathname.startsWith(rootPlatform.path_prefix)) {
        return rootPlatform;
    }
    return availablePlatforms[0]; // Default to the first platform if no match
  };

  const selectedPlatform = getCurrentSelectedPlatform();
  const selectedPlatformValue = selectedPlatform ? selectedPlatform.path_prefix : availablePlatforms[0].path_prefix;

  const handlePlatformChange = (event) => {
    const newPlatformPathPrefix = event.target.value;
    const platformConfig = availablePlatforms.find(p => p.path_prefix === newPlatformPathPrefix);

    if (platformConfig) {
      navigate(platformConfig.path_prefix + (platformConfig.default_sub_route || ""));
    }
  };

  return (
    <FormControl sx={{ m: 1, minWidth: 120 }} size="small">
      <InputLabel id="platform-switcher-label">Platform</InputLabel>
      <Select
        labelId="platform-switcher-label"
        id="platform-switcher-select"
        value={selectedPlatformValue}
        label="Platform"
        onChange={handlePlatformChange}
        sx={{ '.MuiSelect-select': { color: 'inherit', borderColor: 'inherit' } }}
      >
        {availablePlatforms.map((platform) => (
          <MenuItem key={platform.path_prefix} value={platform.path_prefix}>
            {platform.name}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default PlatformSwitcher;