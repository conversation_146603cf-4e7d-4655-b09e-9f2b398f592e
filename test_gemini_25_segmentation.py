#!/usr/bin/env python3
"""
Test script to verify Gemini 2.5 models are correctly prompted for segmentation.
"""

import asyncio
import os
import sys
from io import BytesIO
from PIL import Image, ImageDraw

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def create_test_image():
    """Create a simple test image with distinct objects."""
    img = Image.new('RGB', (640, 640), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw a red rectangle
    draw.rectangle([100, 100, 300, 200], fill='red', outline='darkred', width=3)
    
    # Draw a blue circle
    draw.ellipse([400, 150, 550, 300], fill='blue', outline='darkblue', width=3)
    
    # Add text
    try:
        from PIL import ImageFont
        font = ImageFont.load_default()
        draw.text((200, 400), "TEST OBJECTS", fill='black', font=font)
    except:
        draw.text((200, 400), "TEST OBJECTS", fill='black')
    
    return img

async def test_model_segmentation(model_name):
    """Test a specific model for segmentation capability."""
    print(f"\n🧪 Testing {model_name}")
    print("=" * 60)
    
    try:
        # Import the AI function
        from backend.AI.GC_VertexAI_Simple import vertex_genai_bbox_mask_async
        
        # Create test image
        test_img = create_test_image()
        
        # Convert to bytes
        buffer = BytesIO()
        test_img.save(buffer, format='PNG')
        buffer.seek(0)
        img_bytes = buffer.getvalue()
        
        # Prepare data for AI function
        data_list = [
            ('text', 'Detect and segment all objects in this image'),
            ('image_cv2', img_bytes)
        ]
        
        # Test with segmentation masks requested
        print(f"🔍 Calling {model_name} with segmentation masks...")
        result = await vertex_genai_bbox_mask_async(
            data_list=data_list,
            model_name=model_name,
            include_masks=True
        )
        
        print(f"📋 Result type: {type(result)}")
        
        if isinstance(result, dict) and 'error' in result:
            print(f"❌ Error: {result['error']}")
            if 'details' in result:
                print(f"   Details: {result['details']}")
        elif isinstance(result, list):
            print(f"✅ Success: Found {len(result)} objects")
            
            masks_found = 0
            for i, item in enumerate(result):
                if isinstance(item, dict):
                    label = item.get('label', 'Unknown')
                    has_bbox = 'box_2d' in item
                    has_mask = 'mask' in item
                    
                    print(f"   Object {i+1}: {label}")
                    print(f"      Bounding box: {'✅' if has_bbox else '❌'}")
                    print(f"      Segmentation mask: {'✅' if has_mask else '❌'}")
                    
                    if has_mask:
                        masks_found += 1
                        mask_data = item['mask']
                        print(f"         Mask data length: {len(str(mask_data))}")
                        print(f"         Starts with data URI: {str(mask_data).startswith('data:image')}")
            
            print(f"📊 Summary: {masks_found}/{len(result)} objects have segmentation masks")
            
            if masks_found == 0:
                print(f"⚠️  WARNING: {model_name} did not generate any segmentation masks!")
                print("   This suggests the model may not support segmentation or the prompt needs adjustment.")
            else:
                print(f"🎉 SUCCESS: {model_name} generated {masks_found} segmentation masks!")
        else:
            print(f"❓ Unexpected result format: {result}")
            
    except Exception as e:
        print(f"❌ Exception testing {model_name}: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function."""
    print("🧪 Gemini 2.5 Segmentation Test")
    print("=" * 50)
    print("Testing if Gemini 2.5 models now correctly generate segmentation masks")
    print()
    
    # Test the models from your screenshots
    test_models = [
        "gemini-2.5-pro",
        "gemini-2.5-flash", 
        "gemini-2.5-flash-preview-05-20",
        "gemini-2.5-flash-lite-preview-06-17"  # This one should work
    ]
    
    for model in test_models:
        await test_model_segmentation(model)
    
    print("\n📋 SUMMARY")
    print("=" * 30)
    print("After the fix, all Gemini 2.5 models should:")
    print("✅ Be recognized as segmentation-capable")
    print("✅ Receive enhanced prompts requesting segmentation masks")
    print("✅ Generate both bounding boxes AND segmentation masks")
    print()
    print("If any model still shows 0 segmentation masks, it may indicate:")
    print("- The model doesn't actually support segmentation despite being 2.5")
    print("- The prompt needs further refinement for that specific model")
    print("- API limitations or temporary issues")

if __name__ == "__main__":
    asyncio.run(main())
